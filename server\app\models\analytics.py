from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, String, Foreign<PERSON>ey, DateTime, JSON, Date, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..database import Base

class QueryLog(Base):
    """Query log model."""
    __tablename__ = "query_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    query_id = Column(Integer, ForeignKey("perplexity_queries.id"))
    pull_request_id = Column(Integer, ForeignKey("pull_requests.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", backref="query_logs")
    query = relationship("PerplexityQuery", back_populates="logs")
    pull_request = relationship("PullRequest", backref="query_logs")

class UserActivity(Base):
    """User activity model."""
    __tablename__ = "user_activities"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    action = Column(String)
    details = Column(JSON, default={})
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", backref="activities")

class UsageMetric(Base):
    """Usage metric model."""
    __tablename__ = "usage_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    metric = Column(String)
    value = Column(Integer, default=0)
    date = Column(Date)
    
    # Constraints
    __table_args__ = (
        UniqueConstraint("metric", "date", name="uix_metric_date"),
    )
