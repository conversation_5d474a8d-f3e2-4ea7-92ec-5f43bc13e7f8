import os
from typing import List
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Default CORS origins
DEFAULT_CORS_ORIGINS = ["http://localhost:3000"]

class Settings(BaseModel):
    # Base settings
    PROJECT_NAME: str = "GitHub Enterprise + Perplexity API Integration"
    API_V1_STR: str = "/api"
    DEBUG: bool = os.getenv("DEBUG", "True") == "True"

    # Supabase settings
    SUPABASE_URL: str = os.getenv("SUPABASE_URL", "")
    SUPABASE_ANON_KEY: str = os.getenv("SUPABASE_ANON_KEY", "")
    SUPABASE_SERVICE_ROLE_KEY: str = os.getenv("SUPABASE_SERVICE_ROLE_KEY", "")

    # Database settings
    DATABASE_URL: str = os.getenv("DATABASE_URL", "postgresql://postgres:<EMAIL>:5432/postgres")

    # Clerk Authentication settings
    CLERK_SECRET_KEY: str = os.getenv("CLERK_SECRET_KEY", "")
    CLERK_PUBLISHABLE_KEY: str = os.getenv("CLERK_PUBLISHABLE_KEY", "")
    CLERK_JWT_VERIFICATION_KEY: str = os.getenv("CLERK_JWT_VERIFICATION_KEY", "")

    # Security settings
    SECRET_KEY: str = os.getenv("SECRET_KEY", "insecure-secret-key-for-dev-only")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24  # 1 day

    # CORS settings
    CORS_ALLOWED_ORIGINS: List[str] = Field(default_factory=lambda: DEFAULT_CORS_ORIGINS)

    # GitHub settings
    GITHUB_CLIENT_ID: str = os.getenv("GITHUB_CLIENT_ID", "")
    GITHUB_CLIENT_SECRET: str = os.getenv("GITHUB_CLIENT_SECRET", "")
    GITHUB_ENTERPRISE_URL: str = os.getenv("GITHUB_ENTERPRISE_URL", "")
    GITHUB_REDIRECT_URI: str = os.getenv("GITHUB_REDIRECT_URI", "")

    # Frontend URL
    FRONTEND_URL: str = os.getenv("FRONTEND_URL", "http://localhost:3000")

    # Perplexity API settings
    PERPLEXITY_API_KEY: str = os.getenv("PERPLEXITY_API_KEY", "")

    # Redis settings
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")

# Initialize settings
settings = Settings()

# Try to load CORS origins from environment
cors_origins_str = os.getenv("CORS_ALLOWED_ORIGINS")
if cors_origins_str:
    try:
        settings.CORS_ALLOWED_ORIGINS = [origin.strip() for origin in cors_origins_str.split(",") if origin.strip()]
    except Exception:
        # If there's an error, use the default
        pass
