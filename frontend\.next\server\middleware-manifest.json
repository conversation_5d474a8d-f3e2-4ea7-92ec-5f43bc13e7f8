{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_b7d3adfc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_dc8ed1d6.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "C/JocVL7ys+CwRRmR/v4bWBnsjBqj8kDAERyAOU0xLo=", "__NEXT_PREVIEW_MODE_ID": "80c26f8f4d5e534a437a69994824a1ee", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "80d94efff9f04c65b7cb54bf75a1b75b312fd5faf5b4b12f303a0f5b29886185", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c08e3ad0c7d2837333b86aa96b7bc006ebe3e71ab3bafd57ccc6cba612cf4c2f"}}}, "sortedMiddleware": ["/"], "functions": {}}