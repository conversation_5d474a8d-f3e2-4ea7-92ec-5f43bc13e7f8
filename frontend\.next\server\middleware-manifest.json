{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_b7d3adfc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_dc8ed1d6.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "C/JocVL7ys+CwRRmR/v4bWBnsjBqj8kDAERyAOU0xLo=", "__NEXT_PREVIEW_MODE_ID": "6d5381cc81a7088336688f78c218ed37", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a71bdafbf72b95a94094cd52cb161eaad774e04307fc7d52ad2fdb1ce6d8690b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bc4136be04f250c968b3b28ef349bb1351cccc8cfbaf02c0cc855d1d8260e2c8"}}}, "sortedMiddleware": ["/"], "functions": {}}