from typing import List, Optional
from fastapi import APIRout<PERSON>, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
import logging
import asyncio

from ..database import get_db, SessionLocal
from ..dependencies import get_current_user, get_current_user_profile
from ..auth.router import oauth2_scheme
from ..models.core import Repository, UserProfile, UserRepositoryAccess
from ..models.repository_analysis import (
    RepositoryAnalysis, DependencyAnalysis, TechnologyTrend, 
    SecurityFinding, IssueAugmentation
)
from ..utils.repository_analyzer import RepositoryAnalyzerService
from . import schemas

logger = logging.getLogger(__name__)
router = APIRouter()

async def check_repository_access(
    repository_id: int, 
    current_user_profile: UserProfile, 
    db: Session
) -> Repository:
    """
    Check if the current user has access to the specified repository.
    
    Args:
        repository_id: Repository ID to check access for
        current_user_profile: Current user profile
        db: Database session
        
    Returns:
        Repository object if access is granted
        
    Raises:
        HTTPException: If repository not found or access denied
    """
    logger.info(f"=== REPO ACCESS CHECK: Checking access for repository_id={repository_id}, user={current_user_profile.github_username} ===")
    
    # Get all repository IDs the user has access to
    all_repo_ids = set()

    # User owned repositories
    user_owned_repos_query = db.query(Repository.id).filter(Repository.owner_id == current_user_profile.id)
    user_owned_repo_ids = {repo_id for (repo_id,) in user_owned_repos_query.all()}
    all_repo_ids.update(user_owned_repo_ids)
    logger.info(f"=== REPO ACCESS CHECK: User owned repo IDs: {user_owned_repo_ids} ===")

    # Organization repositories
    org_ids = []
    if current_user_profile.organizations:
        org_ids = [org.id for org in current_user_profile.organizations]
    if org_ids:
        org_repos_query = db.query(Repository.id).filter(Repository.organization_id.in_(org_ids))
        org_repo_ids = {repo_id for (repo_id,) in org_repos_query.all()}
        all_repo_ids.update(org_repo_ids)
        logger.info(f"=== REPO ACCESS CHECK: Org repo IDs: {org_repo_ids} from orgs: {org_ids} ===")

    # Explicitly granted access
    access_repository_ids_query = db.query(UserRepositoryAccess.repository_id).filter(
        UserRepositoryAccess.user_id == current_user_profile.id
    )
    explicit_access_repo_ids = {repo_id for (repo_id,) in access_repository_ids_query.all()}
    all_repo_ids.update(explicit_access_repo_ids)
    logger.info(f"=== REPO ACCESS CHECK: Explicit access repo IDs: {explicit_access_repo_ids} ===")
    
    logger.info(f"=== REPO ACCESS CHECK: Total accessible repo IDs: {all_repo_ids} ===")
    
    # Check if user has access to this specific repository
    if repository_id not in all_repo_ids:
        logger.error(f"=== REPO ACCESS CHECK: ACCESS DENIED! Repository {repository_id} not in accessible repos {all_repo_ids} ===")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Repository not found or access denied"
        )
    
    # Get the repository
    repository = db.query(Repository).filter(Repository.id == repository_id).first()
    if not repository:
        logger.error(f"=== REPO ACCESS CHECK: Repository {repository_id} not found in database ===")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Repository not found"
        )
    
    logger.info(f"=== REPO ACCESS CHECK: ACCESS GRANTED! Repository {repository_id} ({repository.full_name}) ===")
    return repository

@router.post("/repositories/{repository_id}/analyze", response_model=schemas.RepositoryAnalysisResponse)
async def analyze_repository(
    repository_id: int,
    analysis_request: schemas.RepositoryAnalysisRequest,
    background_tasks: BackgroundTasks,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Start a comprehensive repository analysis using Perplexity Sonar.
    """
    logger.info(f"=== ANALYZE REPOSITORY: Starting analysis for repository_id={repository_id}, type={analysis_request.analysis_type} ===")
    
    try:
        logger.info(f"=== ANALYZE REPOSITORY: Getting current user ===")
        current_user = await get_current_user(token, db)
        logger.info(f"=== ANALYZE REPOSITORY: Got user: {current_user.username} ===")
        
        logger.info(f"=== ANALYZE REPOSITORY: Getting current user profile ===")
        current_user_profile = await get_current_user_profile(current_user, db)
        logger.info(f"=== ANALYZE REPOSITORY: Got profile: {current_user_profile.github_username} ===")
        
        # Get repository
        logger.info(f"=== ANALYZE REPOSITORY: Checking repository access ===")
        repository = await check_repository_access(repository_id, current_user_profile, db)
        logger.info(f"=== ANALYZE REPOSITORY: Access granted for {repository.full_name} ===")
        
        # Initialize analyzer with user's GitHub token
        logger.info(f"=== ANALYZE REPOSITORY: Initializing analyzer ===")
        github_token = getattr(current_user_profile, 'github_access_token', None)
        logger.info(f"=== ANALYZE REPOSITORY: GitHub token available: {bool(github_token)} ===")
        
        analyzer = RepositoryAnalyzerService(github_token=github_token)
        logger.info(f"=== ANALYZE REPOSITORY: Analyzer initialized, Perplexity available: {analyzer.perplexity is not None} ===")
        
        # Extract owner and repo name from full_name
        logger.info(f"=== ANALYZE REPOSITORY: Parsing repository full_name: {repository.full_name} ===")
        repo_parts = repository.full_name.split('/')
        if len(repo_parts) != 2:
            logger.error(f"=== ANALYZE REPOSITORY: Invalid full_name format: {repository.full_name} ===")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid repository full name format"
            )
        
        repo_owner, repo_name = repo_parts
        logger.info(f"=== ANALYZE REPOSITORY: Repo owner: {repo_owner}, name: {repo_name} ===")
        
        # Start analysis
        logger.info(f"=== ANALYZE REPOSITORY: Starting {analysis_request.analysis_type} analysis ===")
        if analysis_request.analysis_type == "full":
            logger.info(f"=== ANALYZE REPOSITORY: Running full analysis ===")
            analysis = await analyzer.analyze_repository_full(
                repo_owner, repo_name, db, repository_id
            )
            logger.info(f"=== ANALYZE REPOSITORY: Full analysis completed, ID: {analysis.id} ===")
        else:
            # Check if there's already a pending/in_progress analysis of this type
            logger.info(f"=== ANALYZE REPOSITORY: Checking for existing {analysis_request.analysis_type} analysis ===")
            existing_analysis = db.query(RepositoryAnalysis).filter(
                RepositoryAnalysis.repository_id == repository_id,
                RepositoryAnalysis.analysis_type == analysis_request.analysis_type,
                RepositoryAnalysis.status.in_(["pending", "in_progress"])
            ).first()
            
            if existing_analysis:
                logger.warning(f"=== ANALYZE REPOSITORY: Analysis of type {analysis_request.analysis_type} already in progress (ID: {existing_analysis.id}) ===")
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Analysis of type '{analysis_request.analysis_type}' is already in progress for this repository"
                )
            
            # For specific analysis types, create a basic analysis record
            logger.info(f"=== ANALYZE REPOSITORY: Creating basic analysis record ===")
            analysis = RepositoryAnalysis(
                repository_id=repository_id,
                analysis_type=analysis_request.analysis_type,
                status="pending"
            )
            db.add(analysis)
            db.commit()
            db.refresh(analysis)
            logger.info(f"=== ANALYZE REPOSITORY: Basic analysis record created, ID: {analysis.id} ===")
            
            # Add specific analysis to background tasks
            if analysis_request.analysis_type == "dependency":
                logger.info(f"=== ANALYZE REPOSITORY: Adding dependency analysis to background tasks ===")
                background_tasks.add_task(
                    _run_dependency_analysis, 
                    github_token, repo_owner, repo_name, analysis.id
                )
            elif analysis_request.analysis_type == "technology_trend":
                logger.info(f"=== ANALYZE REPOSITORY: Adding technology analysis to background tasks ===")
                background_tasks.add_task(
                    _run_technology_analysis, 
                    github_token, repo_owner, repo_name, analysis.id
                )
            elif analysis_request.analysis_type == "security":
                logger.info(f"=== ANALYZE REPOSITORY: Adding security analysis to background tasks ===")
                background_tasks.add_task(
                    _run_security_analysis, 
                    github_token, repo_owner, repo_name, analysis.id
                )
        
        # Get related data for response
        logger.info(f"=== ANALYZE REPOSITORY: Querying related data ===")
        dependencies = db.query(DependencyAnalysis).filter(
            DependencyAnalysis.analysis_id == analysis.id
        ).all()
        logger.info(f"=== ANALYZE REPOSITORY: Found {len(dependencies)} dependencies ===")
        
        technologies = db.query(TechnologyTrend).filter(
            TechnologyTrend.analysis_id == analysis.id
        ).all()
        logger.info(f"=== ANALYZE REPOSITORY: Found {len(technologies)} technologies ===")
        
        security_findings = db.query(SecurityFinding).filter(
            SecurityFinding.analysis_id == analysis.id
        ).all()
        logger.info(f"=== ANALYZE REPOSITORY: Found {len(security_findings)} security findings ===")
        
        logger.info(f"=== ANALYZE REPOSITORY: Creating response ===")
        response = schemas.RepositoryAnalysisResponse(
            analysis=analysis,
            dependencies=dependencies if dependencies else None,
            technologies=technologies if technologies else None,
            security_findings=security_findings if security_findings else None
        )
        logger.info(f"=== ANALYZE REPOSITORY: Analysis completed successfully ===")
        return response
        
    except HTTPException as he:
        logger.error(f"=== ANALYZE REPOSITORY: HTTP Exception: {he.status_code} - {he.detail} ===")
        raise
    except Exception as e:
        logger.error(f"=== ANALYZE REPOSITORY: Unexpected error: {type(e).__name__} - {str(e)} ===", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Analysis failed: {str(e)}"
        )

@router.get("/repositories/{repository_id}/analyses", response_model=List[schemas.RepositoryAnalysis])
async def get_repository_analyses(
    repository_id: int,
    skip: int = 0,
    limit: int = 100,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get all analyses for a repository.
    """
    logger.info(f"=== GET REPO ANALYSES: Starting for repository_id={repository_id}, skip={skip}, limit={limit} ===")
    
    try:
        logger.info(f"=== GET REPO ANALYSES: Getting current user from token ===")
        current_user = await get_current_user(token, db)
        logger.info(f"=== GET REPO ANALYSES: Got current user: {current_user.username} (ID: {current_user.id}) ===")
        
        logger.info(f"=== GET REPO ANALYSES: Getting current user profile ===")
        current_user_profile = await get_current_user_profile(current_user, db)
        logger.info(f"=== GET REPO ANALYSES: Got current user profile: {current_user_profile.github_username} (ID: {current_user_profile.id}) ===")
        
        # Check repository exists and user has access
        logger.info(f"=== GET REPO ANALYSES: Checking repository access ===")
        repository = await check_repository_access(repository_id, current_user_profile, db)
        logger.info(f"=== GET REPO ANALYSES: Repository access granted for {repository.full_name} ===")
        
        logger.info(f"=== GET REPO ANALYSES: Querying analyses for repository ===")
        analyses = db.query(RepositoryAnalysis).filter(
            RepositoryAnalysis.repository_id == repository_id
        ).order_by(RepositoryAnalysis.started_at.desc()).offset(skip).limit(limit).all()
        
        logger.info(f"=== GET REPO ANALYSES: Found {len(analyses)} analyses ===")
        return analyses
        
    except HTTPException as he:
        logger.error(f"=== GET REPO ANALYSES: HTTP Exception: {he.status_code} - {he.detail} ===")
        raise
    except Exception as e:
        logger.error(f"=== GET REPO ANALYSES: Unexpected error: {type(e).__name__} - {str(e)} ===", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get repository analyses: {str(e)}"
        )

@router.get("/analyses/{analysis_id}", response_model=schemas.RepositoryAnalysisResponse)
async def get_analysis_details(
    analysis_id: int,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get detailed analysis results including dependencies, technologies, and security findings.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    analysis = db.query(RepositoryAnalysis).filter(RepositoryAnalysis.id == analysis_id).first()
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )
    
    # Get related data
    dependencies = db.query(DependencyAnalysis).filter(
        DependencyAnalysis.analysis_id == analysis_id
    ).all()
    
    technologies = db.query(TechnologyTrend).filter(
        TechnologyTrend.analysis_id == analysis_id
    ).all()
    
    security_findings = db.query(SecurityFinding).filter(
        SecurityFinding.analysis_id == analysis_id
    ).all()
    
    return schemas.RepositoryAnalysisResponse(
        analysis=analysis,
        dependencies=dependencies if dependencies else None,
        technologies=technologies if technologies else None,
        security_findings=security_findings if security_findings else None
    )

@router.get("/analyses/{analysis_id}/dependencies", response_model=List[schemas.DependencyAnalysis])
async def get_analysis_dependencies(
    analysis_id: int,
    skip: int = 0,
    limit: int = 100,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get dependency analysis results for a specific analysis.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    dependencies = db.query(DependencyAnalysis).filter(
        DependencyAnalysis.analysis_id == analysis_id
    ).offset(skip).limit(limit).all()
    
    return dependencies

@router.get("/analyses/{analysis_id}/technologies", response_model=List[schemas.TechnologyTrend])
async def get_analysis_technologies(
    analysis_id: int,
    skip: int = 0,
    limit: int = 100,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get technology trend analysis results for a specific analysis.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    technologies = db.query(TechnologyTrend).filter(
        TechnologyTrend.analysis_id == analysis_id
    ).offset(skip).limit(limit).all()
    
    return technologies

@router.get("/analyses/{analysis_id}/security", response_model=List[schemas.SecurityFinding])
async def get_analysis_security_findings(
    analysis_id: int,
    skip: int = 0,
    limit: int = 100,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get security findings for a specific analysis.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    security_findings = db.query(SecurityFinding).filter(
        SecurityFinding.analysis_id == analysis_id
    ).offset(skip).limit(limit).all()
    
    return security_findings

@router.post("/repositories/{repository_id}/issues/{issue_number}/analyze", response_model=schemas.IssueAugmentation)
async def analyze_issue(
    repository_id: int,
    issue_number: int,
    issue_request: schemas.IssueAnalysisRequest,
    background_tasks: BackgroundTasks,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Analyze a GitHub issue using Perplexity Sonar to find similar issues and solutions.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    # Check repository exists
    repository = await check_repository_access(repository_id, current_user_profile, db)
    
    # Initialize analyzer
    analyzer = RepositoryAnalyzerService(github_token=current_user_profile.github_access_token)
    
    try:
        # Analyze issue with Sonar
        augmentation = await analyzer.analyze_issue_with_sonar(
            repository_id=repository_id,
            issue_number=issue_number,
            issue_title=issue_request.issue_title,
            issue_body=issue_request.issue_body or "",
            db=db
        )
        
        return augmentation
        
    except Exception as e:
        logger.error(f"Issue analysis failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Issue analysis failed: {str(e)}"
        )

@router.get("/repositories/{repository_id}/issues", response_model=List[schemas.IssueAugmentation])
async def get_repository_issue_augmentations(
    repository_id: int,
    skip: int = 0,
    limit: int = 100,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get all issue augmentations for a repository.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    augmentations = db.query(IssueAugmentation).filter(
        IssueAugmentation.repository_id == repository_id
    ).order_by(IssueAugmentation.created_at.desc()).offset(skip).limit(limit).all()
    
    return augmentations

@router.get("/issues/{augmentation_id}", response_model=schemas.IssueAugmentation)
async def get_issue_augmentation(
    augmentation_id: int,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get detailed issue augmentation results.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    augmentation = db.query(IssueAugmentation).filter(
        IssueAugmentation.id == augmentation_id
    ).first()
    
    if not augmentation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Issue augmentation not found"
        )
    
    return augmentation

# Background task functions
def _run_dependency_analysis(
    github_token: str,
    repo_owner: str, 
    repo_name: str, 
    analysis_id: int
):
    """Run dependency analysis in background."""
    async def _async_dependency_analysis():
        db = SessionLocal()
        try:
            logger.info(f"=== BACKGROUND DEPENDENCY: Starting for {repo_owner}/{repo_name}, analysis_id={analysis_id} ===")
            
            # Create new analyzer instance for this background task
            analyzer = RepositoryAnalyzerService(github_token=github_token)
            
            if not analyzer.github:
                raise Exception("GitHub client not available")
                
            github_repo = analyzer.github.get_repo(f"{repo_owner}/{repo_name}")
            logger.info(f"=== BACKGROUND DEPENDENCY: Got GitHub repo ===")
            
            await analyzer._analyze_dependencies(github_repo, analysis_id, db)
            logger.info(f"=== BACKGROUND DEPENDENCY: Dependencies analyzed ===")
            
            # Update analysis status
            analysis = db.query(RepositoryAnalysis).filter(RepositoryAnalysis.id == analysis_id).first()
            if analysis:
                analysis.status = "completed"
                db.commit()
                logger.info(f"=== BACKGROUND DEPENDENCY: Analysis marked as completed ===")
                
        except Exception as e:
            logger.error(f"=== BACKGROUND DEPENDENCY: Failed: {type(e).__name__} - {str(e)} ===", exc_info=True)
            analysis = db.query(RepositoryAnalysis).filter(RepositoryAnalysis.id == analysis_id).first()
            if analysis:
                analysis.status = "failed"
                analysis.error_message = str(e)
                db.commit()
        finally:
            db.close()
    
    # Run the async function
    asyncio.run(_async_dependency_analysis())

def _run_technology_analysis(
    github_token: str,
    repo_owner: str, 
    repo_name: str, 
    analysis_id: int
):
    """Run technology analysis in background."""
    async def _async_technology_analysis():
        db = SessionLocal()
        try:
            logger.info(f"=== BACKGROUND TECHNOLOGY: Starting for {repo_owner}/{repo_name}, analysis_id={analysis_id} ===")
            
            # Create new analyzer instance for this background task
            analyzer = RepositoryAnalyzerService(github_token=github_token)
            
            if not analyzer.github:
                raise Exception("GitHub client not available")
                
            github_repo = analyzer.github.get_repo(f"{repo_owner}/{repo_name}")
            logger.info(f"=== BACKGROUND TECHNOLOGY: Got GitHub repo ===")
            
            await analyzer._analyze_technologies(github_repo, analysis_id, db)
            logger.info(f"=== BACKGROUND TECHNOLOGY: Technologies analyzed ===")
            
            # Update analysis status
            analysis = db.query(RepositoryAnalysis).filter(RepositoryAnalysis.id == analysis_id).first()
            if analysis:
                analysis.status = "completed"
                db.commit()
                logger.info(f"=== BACKGROUND TECHNOLOGY: Analysis marked as completed ===")
                
        except Exception as e:
            logger.error(f"=== BACKGROUND TECHNOLOGY: Failed: {type(e).__name__} - {str(e)} ===", exc_info=True)
            analysis = db.query(RepositoryAnalysis).filter(RepositoryAnalysis.id == analysis_id).first()
            if analysis:
                analysis.status = "failed"
                analysis.error_message = str(e)
                db.commit()
        finally:
            db.close()
    
    # Run the async function
    asyncio.run(_async_technology_analysis())

def _run_security_analysis(
    github_token: str,
    repo_owner: str, 
    repo_name: str, 
    analysis_id: int
):
    """Run security analysis in background."""
    async def _async_security_analysis():
        db = SessionLocal()
        try:
            logger.info(f"=== BACKGROUND SECURITY: Starting for {repo_owner}/{repo_name}, analysis_id={analysis_id} ===")
            
            # Create new analyzer instance for this background task
            analyzer = RepositoryAnalyzerService(github_token=github_token)
            
            if not analyzer.github:
                raise Exception("GitHub client not available")
                
            github_repo = analyzer.github.get_repo(f"{repo_owner}/{repo_name}")
            logger.info(f"=== BACKGROUND SECURITY: Got GitHub repo ===")
            
            await analyzer._analyze_security(github_repo, analysis_id, db)
            logger.info(f"=== BACKGROUND SECURITY: Security analyzed ===")
            
            # Update analysis status
            analysis = db.query(RepositoryAnalysis).filter(RepositoryAnalysis.id == analysis_id).first()
            if analysis:
                analysis.status = "completed"
                db.commit()
                logger.info(f"=== BACKGROUND SECURITY: Analysis marked as completed ===")
                
        except Exception as e:
            logger.error(f"=== BACKGROUND SECURITY: Failed: {type(e).__name__} - {str(e)} ===", exc_info=True)
            analysis = db.query(RepositoryAnalysis).filter(RepositoryAnalysis.id == analysis_id).first()
            if analysis:
                analysis.status = "failed"
                analysis.error_message = str(e)
                db.commit()
        finally:
            db.close()
    
    # Run the async function
    asyncio.run(_async_security_analysis()) 