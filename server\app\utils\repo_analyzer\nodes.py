import os
from pocketflow import Node, BatchNode
from .github_api import (
    get_repository_dependencies,
    get_repository_technologies,
    get_repository_issues,
    post_issue_comment
)
from .call_sonar import (
    query_dependency_info,
    query_technology_trends,
    query_issue_insights
)
from ..tutor.crawl_github_files import crawl_github_files


class FetchRepo(Node):
    """Node to fetch repository content."""
    
    def prep(self, shared):
        repo_url = shared.get("repo_url")
        github_token = shared.get("github_token")
        include_patterns = shared.get("include_patterns")
        exclude_patterns = shared.get("exclude_patterns")
        max_file_size = shared.get("max_file_size", 100000)  # 100KB default
        
        return {
            "repo_url": repo_url,
            "token": github_token,
            "include_patterns": include_patterns,
            "exclude_patterns": exclude_patterns,
            "max_file_size": max_file_size,
            "use_relative_paths": True,
        }
    
    def exec(self, prep_res):
        print(f"Crawling repository: {prep_res['repo_url']}...")
        result = crawl_github_files(
            repo_url=prep_res["repo_url"],
            token=prep_res["token"],
            include_patterns=prep_res["include_patterns"],
            exclude_patterns=prep_res["exclude_patterns"],
            max_file_size=prep_res["max_file_size"],
            use_relative_paths=prep_res["use_relative_paths"],
        )
        files_list = list(result.get("files", {}).items())
        if len(files_list) == 0:
            print("Warning: No files fetched from repository.")
        print(f"Fetched {len(files_list)} files.")
        # Build directory structure as a nested dict
        dir_tree = {}
        for path, _ in files_list:
            parts = path.split("/")
            node = dir_tree
            for part in parts[:-1]:
                node = node.setdefault(part, {})
            node.setdefault("__files__", []).append(parts[-1])
        return {"files": files_list, "dir_tree": dir_tree}
    
    def post(self, shared, prep_res, exec_res):
        shared["files"] = exec_res["files"]  # List of (path, content) tuples
        shared["dir_tree"] = exec_res["dir_tree"]  # Directory structure


class DependencyAnalyzer(Node):
    """Node to analyze dependencies in a repository."""
    
    def prep(self, shared):
        repo_url = shared.get("repo_url")
        github_token = shared.get("github_token")
        
        return {
            "repo_url": repo_url,
            "token": github_token
        }
    
    def exec(self, prep_res):
        logger = None
        try:
            from .call_sonar import logger as sonar_logger
            logger = sonar_logger
        except Exception:
            import logging
            logger = logging.getLogger("debug_logger")
            logger.setLevel(logging.INFO)
        logger.info(f"Analyzing dependencies for {prep_res['repo_url']}")
        print("Analyzing dependencies...")
        print(f"Calling get_repository_dependencies for {prep_res['repo_url']}")
        dependencies = get_repository_dependencies(
            repo_url=prep_res["repo_url"],
            token=prep_res["token"]
        )
        logger.info(f"get_repository_dependencies returned {len(dependencies)} dependencies.")
        print(f"get_repository_dependencies returned {len(dependencies)} dependencies.")
        print(f"Found {len(dependencies)} dependencies.")
        return dependencies
    
    def post(self, shared, prep_res, exec_res):
        shared["dependencies"] = exec_res


class DependencyInsightsBatch(BatchNode):
    """BatchNode to get insights for each dependency."""
    
    def prep(self, shared):
        dependencies = shared.get("dependencies", [])
        
        # Prepare items for batch processing
        return [
            {"dependency": dep} for dep in dependencies
        ]
    
    def exec(self, item):
        dependency = item["dependency"]
        logger = None
        try:
            from .call_sonar import logger as sonar_logger
            logger = sonar_logger
        except Exception:
            import logging
            logger = logging.getLogger("debug_logger")
            logger.setLevel(logging.INFO)
        dependency = item["dependency"]
        logger.info(f"Analyzing dependency: {dependency['name']} ({dependency['type']})")
        print(f"Analyzing dependency: {dependency['name']} ({dependency['type']})")
        try:
            insights = query_dependency_info(dependency)
            logger.info(f"Dependency insights: {insights}")
            return {**dependency, **insights}
        except Exception as e:
            logger.error(f"Error analyzing dependency {dependency['name']}: {str(e)}")
            print(f"Error analyzing dependency {dependency['name']}: {str(e)}")
            return dependency
    
    def post(self, shared, prep_res, exec_res_list):
        # Store enhanced dependencies with insights
        shared["dependencies"] = exec_res_list
        
        # Store aggregated dependency insights
        shared["dependency_insights"] = {
            "total": len(exec_res_list),
            "outdated": sum(1 for dep in exec_res_list if dep.get("is_outdated", False)),
            "vulnerable": sum(1 for dep in exec_res_list if dep.get("vulnerabilities")),
            "by_type": {}
        }
        
        # Aggregate by type
        for dep in exec_res_list:
            dep_type = dep.get("type", "unknown")
            if dep_type not in shared["dependency_insights"]["by_type"]:
                shared["dependency_insights"]["by_type"][dep_type] = {
                    "total": 0,
                    "outdated": 0,
                    "vulnerable": 0
                }
            
            shared["dependency_insights"]["by_type"][dep_type]["total"] += 1
            if dep.get("is_outdated", False):
                shared["dependency_insights"]["by_type"][dep_type]["outdated"] += 1
            if dep.get("vulnerabilities"):
                shared["dependency_insights"]["by_type"][dep_type]["vulnerable"] += 1
        
        print(f"Completed dependency analysis for {len(exec_res_list)} dependencies.")


class TechnologyAnalyzer(Node):
    """Node to analyze technologies used in a repository."""
    
    def prep(self, shared):
        repo_url = shared.get("repo_url")
        github_token = shared.get("github_token")
        
        return {
            "repo_url": repo_url,
            "token": github_token
        }
    
    def exec(self, prep_res):
        logger = None
        try:
            from .call_sonar import logger as sonar_logger
            logger = sonar_logger
        except Exception:
            import logging
            logger = logging.getLogger("debug_logger")
            logger.setLevel(logging.INFO)
        logger.info(f"Analyzing technologies for {prep_res['repo_url']}")
        print("Analyzing technologies...")
        print(f"Calling get_repository_technologies for {prep_res['repo_url']}")
        technologies = get_repository_technologies(
            repo_url=prep_res["repo_url"],
            token=prep_res["token"]
        )
        logger.info(f"get_repository_technologies returned {len(technologies)} technologies.")
        print(f"get_repository_technologies returned {len(technologies)} technologies.")
        print(f"Found {len(technologies)} technologies.")
        return technologies
    
    def post(self, shared, prep_res, exec_res):
        shared["technologies"] = exec_res


class TechnologyInsightsBatch(BatchNode):
    """BatchNode to get insights for each technology."""
    
    def prep(self, shared):
        technologies = shared.get("technologies", [])
        
        # Filter out less relevant technologies
        main_technologies = []
        
        # Get top languages by bytes
        languages = [tech for tech in technologies if tech["type"] == "language"]
        languages.sort(key=lambda x: x.get("bytes", 0), reverse=True)
        top_languages = languages[:3]  # Get top 3 languages
        
        # Get all frameworks and libraries
        frameworks = [tech for tech in technologies if tech["type"] in ["framework", "library"]]
        
        # Combine top languages and all frameworks
        main_technologies = top_languages + frameworks
        
        # Prepare items for batch processing
        return [
            {"technology": tech} for tech in main_technologies
        ]
    
    def exec(self, item):
        technology = item["technology"]
        logger = None
        try:
            from .call_sonar import logger as sonar_logger
            logger = sonar_logger
        except Exception:
            import logging
            logger = logging.getLogger("debug_logger")
            logger.setLevel(logging.INFO)
        technology = item["technology"]
        logger.info(f"Analyzing technology: {technology['name']} ({technology['type']})")
        print(f"Analyzing technology: {technology['name']} ({technology['type']})")
        try:
            insights = query_technology_trends(technology)
            logger.info(f"Technology insights: {insights}")
            return {**technology, **insights}
        except Exception as e:
            logger.error(f"Error analyzing technology {technology['name']}: {str(e)}")
            print(f"Error analyzing technology {technology['name']}: {str(e)}")
            return technology
    
    def post(self, shared, prep_res, exec_res_list):
        # Store enhanced technologies with insights
        shared["technologies"] = exec_res_list
        
        # Store aggregated technology insights
        tech_names = [tech["name"] for tech in exec_res_list]
        
        shared["technology_insights"] = {
            "total": len(exec_res_list),
            "languages": [tech for tech in exec_res_list if tech["type"] == "language"],
            "frameworks": [tech for tech in exec_res_list if tech["type"] == "framework"],
            "libraries": [tech for tech in exec_res_list if tech["type"] == "library"],
            "technology_names": tech_names
        }
        
        print(f"Completed technology analysis for {len(exec_res_list)} technologies.")


class IssueAnalyzer(Node):
    """Node to fetch and analyze issues in a repository."""
    
    def prep(self, shared):
        repo_url = shared.get("repo_url")
        github_token = shared.get("github_token")
        issue_numbers = shared.get("issue_numbers")
        
        return {
            "repo_url": repo_url,
            "token": github_token,
            "issue_numbers": issue_numbers
        }
    
    def exec(self, prep_res):
        logger = None
        try:
            from .call_sonar import logger as sonar_logger
            logger = sonar_logger
        except Exception:
            import logging
            logger = logging.getLogger("debug_logger")
            logger.setLevel(logging.INFO)
        logger.info(f"Fetching repository issues for {prep_res['repo_url']}")
        print("Fetching repository issues...")
        issues = get_repository_issues(
            repo_url=prep_res["repo_url"],
            token=prep_res["token"],
            issue_numbers=prep_res["issue_numbers"]
        )
        logger.info(f"get_repository_issues returned {len(issues)} issues.")
        print(f"Found {len(issues)} issues.")
        return issues
    
    def post(self, shared, prep_res, exec_res):
        shared["issues"] = exec_res


class IssueInsightsBatch(BatchNode):
    """BatchNode to get insights for each issue."""
    
    def prep(self, shared):
        issues = shared.get("issues", [])
        
        # Limit to max 10 issues if not specifically requested
        if not shared.get("issue_numbers"):
            issues = issues[:10]
        
        # Prepare items for batch processing
        return [
            {"issue": issue} for issue in issues
        ]
    
    def exec(self, item):
        logger = None
        try:
            from .call_sonar import logger as sonar_logger
            logger = sonar_logger
        except Exception:
            import logging
            logger = logging.getLogger("debug_logger")
            logger.setLevel(logging.INFO)
        issue = item["issue"]
        logger.info(f"Analyzing issue #{issue['number']}: {issue['title']}")
        print(f"Analyzing issue #{issue['number']}: {issue['title']}")
        try:
            issue_data = {
                "number": issue["number"],
                "title": issue["title"],
                "body": issue.get("body", "")
            }
            insights = query_issue_insights(issue_data)
            logger.info(f"Issue insights: {insights}")
            return {**issue_data, **insights}
        except Exception as e:
            logger.error(f"Error analyzing issue #{issue['number']}: {str(e)}")
            print(f"Error analyzing issue #{issue['number']}: {str(e)}")
            return {
                "number": issue["number"],
                "title": issue["title"],
                "body": issue.get("body", "")
            }
    
    def post(self, shared, prep_res, exec_res_list):
        # Store enhanced issues with insights
        shared["issues"] = exec_res_list
        
        # Store aggregated issue insights
        shared["issue_insights"] = {
            "total": len(exec_res_list),
            "issues_analyzed": [issue["number"] for issue in exec_res_list]
        }
        
        print(f"Completed issue analysis for {len(exec_res_list)} issues.")


class IssueCommenter(BatchNode):
    """BatchNode to post comments with insights on GitHub issues."""
    
    def prep(self, shared):
        issues = shared.get("issues", [])
        github_token = shared.get("github_token")
        repo_url = shared.get("repo_url")
        
        # Only proceed if token is available
        if not github_token:
            print("GitHub token not provided. Skipping issue commenting.")
            return []
        
        # Prepare items for batch processing
        return [
            {
                "issue": issue,
                "token": github_token,
                "repo_url": repo_url
            } for issue in issues
        ]
    
    def exec(self, item):
        issue = item["issue"]
        token = item["token"]
        repo_url = item["repo_url"]
        
        issue_number = issue["number"]
        print(f"Posting comment on issue #{issue_number}")
        
        # Format the comment
        comment = f"""## Perplexity Repository Analyzer Insights

I've analyzed this issue and found some potentially helpful information:

### Similar Issues
{self._format_list(issue.get("similar_issues", []))}

### Potential Solutions
{self._format_list(issue.get("solutions", []))}

### Relevant Documentation
{self._format_list(issue.get("documentation_links", []))}

### Insights
{issue.get("insights", "No additional insights available.")}

---
*This comment was automatically generated by the Perplexity Repository Analyzer.*
"""
        
        # Post the comment
        success = post_issue_comment(
            repo_url=repo_url,
            issue_number=issue_number,
            comment=comment,
            token=token
        )
        
        return {
            "issue_number": issue_number,
            "comment_posted": success
        }
    
    def post(self, shared, prep_res, exec_res_list):
        # Store comment results
        shared["issue_comments"] = exec_res_list
        
        successful_comments = sum(1 for res in exec_res_list if res.get("comment_posted", False))
        print(f"Posted comments on {successful_comments} issues.")
    
    def _format_list(self, items):
        """Format a list of items for the comment."""
        if not items:
            return "None found."
        
        return "\n".join([f"- {item}" for item in items])


class ResultsAggregator(Node):
    """Node to aggregate all results."""
    
    def prep(self, shared):
        return {
            "dependencies": shared.get("dependencies", []),
            "technologies": shared.get("technologies", []),
            "issues": shared.get("issues", []),
            "dependency_insights": shared.get("dependency_insights", {}),
            "technology_insights": shared.get("technology_insights", {}),
            "issue_insights": shared.get("issue_insights", {})
        }
    
    def exec(self, prep_res):
        print("Aggregating analysis results...")
        
        # Create summary
        summary = {
            "dependencies": {
                "total": len(prep_res["dependencies"]),
                "outdated": sum(1 for dep in prep_res["dependencies"] if dep.get("is_outdated", False)),
                "vulnerable": sum(1 for dep in prep_res["dependencies"] if dep.get("vulnerabilities")),
                "by_type": prep_res["dependency_insights"].get("by_type", {})
            },
            "technologies": {
                "total": len(prep_res["technologies"]),
                "languages": [tech["name"] for tech in prep_res["technologies"] if tech["type"] == "language"],
                "frameworks": [tech["name"] for tech in prep_res["technologies"] if tech["type"] == "framework"],
                "libraries": [tech["name"] for tech in prep_res["technologies"] if tech["type"] == "library"]
            },
            "issues": {
                "total": len(prep_res["issues"]),
                "analyzed": prep_res["issue_insights"].get("total", 0)
            }
        }
        
        return {
            "summary": summary,
            "dependencies": prep_res["dependencies"],
            "technologies": prep_res["technologies"],
            "issues": prep_res["issues"]
        }
    
    def post(self, shared, prep_res, exec_res):
        shared["analysis_results"] = exec_res
        print("Analysis complete. Results aggregated.")