from sqlalchemy import Column, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, DateTime, Text, JSON, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..database import Base

class PullRequest(Base):
    """Pull request model."""
    __tablename__ = "pull_requests"
    
    id = Column(Integer, primary_key=True, index=True)
    repository_id = Column(Integer, ForeignKey("repositories.id"))
    github_id = Column(Integer, unique=True, index=True)
    number = Column(Integer, index=True)
    title = Column(String)
    description = Column(Text, nullable=True)
    state = Column(String)
    created_by_id = Column(Integer, ForeignKey("user_profiles.id"), nullable=True)
    base_branch = Column(String)
    head_branch = Column(String)
    is_draft = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    closed_at = Column(DateTime(timezone=True), nullable=True)
    merged_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    repository = relationship("Repository", backref="pull_requests")
    created_by = relationship("UserProfile", backref="created_pull_requests")
    comments = relationship("PRComment", back_populates="pull_request", cascade="all, delete-orphan")
    reviews = relationship("PRReview", back_populates="pull_request", cascade="all, delete-orphan")
    diff = relationship("PRDiff", back_populates="pull_request", uselist=False, cascade="all, delete-orphan")
    github_actions = relationship("GithubAction", back_populates="pull_request", cascade="all, delete-orphan")

class PRComment(Base):
    """Pull request comment model."""
    __tablename__ = "pr_comments"
    
    id = Column(Integer, primary_key=True, index=True)
    pull_request_id = Column(Integer, ForeignKey("pull_requests.id"))
    github_id = Column(Integer, unique=True, nullable=True, index=True)
    user_id = Column(Integer, ForeignKey("user_profiles.id"), nullable=True)
    body = Column(Text)
    path = Column(String, nullable=True)
    position = Column(Integer, nullable=True)
    is_ai_generated = Column(Boolean, default=False)
    ai_model = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    pull_request = relationship("PullRequest", back_populates="comments")
    user = relationship("UserProfile", backref="pr_comments")

class PRReview(Base):
    """Pull request review model."""
    __tablename__ = "pr_reviews"
    
    id = Column(Integer, primary_key=True, index=True)
    pull_request_id = Column(Integer, ForeignKey("pull_requests.id"))
    github_id = Column(Integer, unique=True, nullable=True, index=True)
    user_id = Column(Integer, ForeignKey("user_profiles.id"), nullable=True)
    status = Column(String)
    body = Column(Text, nullable=True)
    is_ai_generated = Column(Boolean, default=False)
    ai_status = Column(String, nullable=True)
    ai_model = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    submitted_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    pull_request = relationship("PullRequest", back_populates="reviews")
    user = relationship("UserProfile", backref="reviews")

class PRDiff(Base):
    """Pull request diff model."""
    __tablename__ = "pr_diffs"
    
    id = Column(Integer, primary_key=True, index=True)
    pull_request_id = Column(Integer, ForeignKey("pull_requests.id"), unique=True)
    diff_content = Column(Text)
    num_files = Column(Integer)
    additions = Column(Integer)
    deletions = Column(Integer)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    pull_request = relationship("PullRequest", back_populates="diff")

class GithubFile(Base):
    """GitHub file model."""
    __tablename__ = "github_files"
    
    id = Column(Integer, primary_key=True, index=True)
    repository_id = Column(Integer, ForeignKey("repositories.id"))
    path = Column(String)
    content = Column(Text, nullable=True)
    sha = Column(String)
    size = Column(Integer)
    last_modified = Column(DateTime(timezone=True))
    
    # Relationships
    repository = relationship("Repository", backref="files")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint("repository_id", "path", name="uix_repository_path"),
    )

class GithubAction(Base):
    """GitHub action model."""
    __tablename__ = "github_actions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("user_profiles.id"), nullable=True)
    repository_id = Column(Integer, ForeignKey("repositories.id"), nullable=True)
    pull_request_id = Column(Integer, ForeignKey("pull_requests.id"), nullable=True)
    action_type = Column(String)
    status = Column(String, default="queued")
    input_data = Column(JSON, nullable=True)
    result_data = Column(JSON, nullable=True)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("UserProfile", backref="github_actions")
    repository = relationship("Repository", backref="github_actions")
    pull_request = relationship("PullRequest", back_populates="github_actions")

class GithubWebhook(Base):
    """GitHub webhook model."""
    __tablename__ = "github_webhooks"
    
    id = Column(Integer, primary_key=True, index=True)
    repository_id = Column(Integer, ForeignKey("repositories.id"), nullable=True)
    event_type = Column(String)
    payload = Column(JSON)
    processed = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    repository = relationship("Repository", backref="webhooks")
