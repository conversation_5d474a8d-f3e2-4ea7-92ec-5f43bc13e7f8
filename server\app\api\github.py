from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import datetime
import logging

# Configure basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from ..database import get_db
from ..dependencies import get_current_user, get_current_user_profile
from ..models.core import User, UserProfile, Repository, UserRepositoryAccess
from ..models.github import PullRequest, PRComment, PRReview, GithubFile, GithubAction, GithubWebhook
from . import schemas
from .core import get_repositories as core_get_repositories
from ..auth.router import oauth2_scheme, get_current_user
from ..dependencies import get_current_user_profile

router = APIRouter()

@router.get("/pull-requests/", response_model=List[schemas.PullRequest])
async def get_pull_requests(
    repository_id: Optional[int] = None,
    state: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get pull requests.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    query = db.query(PullRequest)

    # Filter by repository
    if repository_id:
        query = query.filter(PullRequest.repository_id == repository_id)

    # Filter by state
    if state:
        query = query.filter(PullRequest.state == state)

    # Apply pagination
    pull_requests = query.offset(skip).limit(limit).all()

    return pull_requests

@router.get("/pull-requests/{pull_request_id}", response_model=schemas.PullRequest)
async def get_pull_request(
    pull_request_id: int,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get a specific pull request.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    pull_request = db.query(PullRequest).filter(PullRequest.id == pull_request_id).first()
    if not pull_request:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pull request not found"
        )

    return pull_request

@router.get("/comments/", response_model=List[schemas.PRComment])
async def get_comments(
    pull_request_id: Optional[int] = None,
    skip: int = 0,
    limit: int = 100,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get pull request comments.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    query = db.query(PRComment)

    # Filter by pull request
    if pull_request_id:
        query = query.filter(PRComment.pull_request_id == pull_request_id)

    # Apply pagination
    comments = query.offset(skip).limit(limit).all()

    return comments

@router.get("/reviews/", response_model=List[schemas.PRReview])
async def get_reviews(
    pull_request_id: Optional[int] = None,
    skip: int = 0,
    limit: int = 100,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get pull request reviews.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    query = db.query(PRReview)

    # Filter by pull request
    if pull_request_id:
        query = query.filter(PRReview.pull_request_id == pull_request_id)

    # Apply pagination
    reviews = query.offset(skip).limit(limit).all()

    return reviews

@router.get("/files/", response_model=List[schemas.GithubFile])
async def get_files(
    repository_id: Optional[int] = None,
    path: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get GitHub files.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    query = db.query(GithubFile)

    # Filter by repository
    if repository_id:
        query = query.filter(GithubFile.repository_id == repository_id)

    # Filter by path
    if path:
        query = query.filter(GithubFile.path.like(f"{path}%"))

    # Apply pagination
    files = query.offset(skip).limit(limit).all()

    return files

@router.get("/actions/", response_model=List[schemas.GithubAction])
async def get_actions(
    repository_id: Optional[int] = None,
    pull_request_id: Optional[int] = None,
    action_type: Optional[str] = None,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get GitHub actions.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    query = db.query(GithubAction)

    # Filter by repository
    if repository_id:
        query = query.filter(GithubAction.repository_id == repository_id)

    # Filter by pull request
    if pull_request_id:
        query = query.filter(GithubAction.pull_request_id == pull_request_id)

    # Filter by action type
    if action_type:
        query = query.filter(GithubAction.action_type == action_type)

    # Filter by status
    if status:
        query = query.filter(GithubAction.status == status)

    # Apply pagination
    actions = query.offset(skip).limit(limit).all()

    return actions

@router.get("/webhooks/", response_model=List[schemas.GithubWebhook])
async def get_webhooks(
    repository_id: Optional[int] = None,
    event_type: Optional[str] = None,
    processed: Optional[bool] = None,
    skip: int = 0,
    limit: int = 100,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get GitHub webhooks.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    query = db.query(GithubWebhook)

    # Filter by repository
    if repository_id:
        query = query.filter(GithubWebhook.repository_id == repository_id)

    # Filter by event type
    if event_type:
        query = query.filter(GithubWebhook.event_type == event_type)

    # Filter by processed
    if processed is not None:
        query = query.filter(GithubWebhook.processed == processed)

    # Apply pagination
    webhooks = query.offset(skip).limit(limit).all()

    return webhooks

@router.get("/repositories/", response_model=List[schemas.Repository])
async def get_repositories(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme) 
):
    logger.info(f"--- GITHUB_API (step 4 manual dependency): ENTERING. skip={skip}, limit={limit} ---")
    
    _current_user = await get_current_user(token=token, db=db)
    if not _current_user:
        logger.error("--- GITHUB_API (step 4 manual dependency): Could not get current_user from token ---")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token or user not found")
        
    _current_user_profile = await get_current_user_profile(current_user=_current_user, db=db)
    if not _current_user_profile:
        logger.error(f"--- GITHUB_API (step 4 manual dependency): Could not get user profile for user_id: {_current_user.id} ---")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User profile not found")

    logger.info(f"--- GITHUB_API (step 4 manual dependency): Manually resolved profile for user: {_current_user_profile.github_username} ---")

    try:
        logger.info("--- GITHUB_API (step 4 manual dependency): Attempting to call core_get_repositories ---")
        repositories_data = await core_get_repositories(skip, limit, token, db)
        logger.info(f"--- GITHUB_API (step 4 manual dependency): Call to core_get_repositories returned. Found {len(repositories_data) if repositories_data is not None else 'None'} repositories ---")

        if not repositories_data:
            logger.info(f"--- GITHUB_API (step 4 manual dependency): No repositories from core. User {_current_user_profile.user.username}. Preparing mock data. ---")
            mock_repo_db_instance = Repository(
                name="mock-repository",
                full_name=f"{_current_user_profile.github_username}/mock-repository",
                github_id="mock_github_id_12345", 
                github_url=f"https://github.com/{_current_user_profile.github_username}/mock-repository", 
                description="This is a mock repository for testing",
                is_private=False,
                owner_id=_current_user_profile.id,
                created_at=datetime.utcnow(), 
                updated_at=datetime.utcnow()
            )
            logger.info("--- GITHUB_API (step 4 manual dependency): Mock repo DB instance created. Adding to session. ---")
            db.add(mock_repo_db_instance)
            db.commit()
            db.refresh(mock_repo_db_instance)
            logger.info(f"--- GITHUB_API (step 4 manual dependency): Mock repo committed. ID: {mock_repo_db_instance.id} ---")
            
            mock_repo_schema = schemas.Repository.model_validate(mock_repo_db_instance)
            logger.info("--- GITHUB_API (step 4 manual dependency): EXITING with MOCK data (Pydantic model) ---")
            return [mock_repo_schema]

        logger.info("--- GITHUB_API (step 4 manual dependency): EXITING with REAL data from core ---")
        return repositories_data 
    except HTTPException: 
        raise
    except Exception as e_github:
        logger.error(f"!!! EXCEPTION in github.py get_repositories: {type(e_github).__name__} - {str(e_github)} !!!", exc_info=True)
        logger.info("--- GITHUB_API (step 4 manual dependency): EXITING due to exception, returning empty list ---")
        return []
