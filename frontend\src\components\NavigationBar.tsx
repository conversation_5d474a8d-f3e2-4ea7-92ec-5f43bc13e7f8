'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import {
  Github,
  Search,
  MessageSquare,
  FolderGit2,
  Home,
  Menu,
  User,
  Settings,
  LogOut
} from 'lucide-react';
import { useUser, UserButton, SignInButton } from '@clerk/nextjs';

interface NavigationBarProps {
  className?: string;
}

const NavigationBar = ({ className }: NavigationBarProps) => {
  const router = useRouter();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { user, isLoaded } = useUser();

  const navItems = [
    { text: 'Home', path: '/', icon: Home },
    { text: 'Repositories', path: '/repositories', icon: FolderGit2 },
    { text: 'Code Search', path: '/search', icon: Search },
    { text: 'AI Chat', path: '/chat', icon: MessageSquare },
  ];

  const NavItems = ({ mobile = false, onItemClick }: { mobile?: boolean; onItemClick?: () => void }) => (
    <>
      {navItems.map((item) => {
        const IconComponent = item.icon;
        return (
          <Link
            key={item.text}
            href={item.path}
            onClick={onItemClick}
            className={`
              flex items-center gap-2 transition-colors hover:text-primary
              ${mobile
                ? 'flex-col py-4 px-2 text-sm text-center'
                : 'px-3 py-2 text-sm font-medium'
              }
            `}
          >
            <IconComponent className={mobile ? 'h-5 w-5' : 'h-4 w-4'} />
            {item.text}
          </Link>
        );
      })}
    </>
  );

  return (
    <nav className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
            <Github className="h-5 w-5 text-primary-foreground" />
          </div>
          <span className="hidden font-bold sm:inline-block">
            GitHub + Perplexity
          </span>
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden md:flex md:items-center md:space-x-1">
          <NavItems />
        </div>

        {/* User Section */}
        <div className="flex items-center space-x-2">
          {isLoaded ? (
            user ? (
              <UserButton afterSignOutUrl="/sign-in" />
            ) : (
              <SignInButton mode="modal">
                <Button>Sign In</Button>
              </SignInButton>
            )
          ) : (
            <Button disabled>Loading...</Button>
          )}

          {/* Mobile Menu */}
          <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                className="mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden"
              >
                <Menu className="h-6 w-6" />
                <span className="sr-only">Toggle Menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="pr-0">
              <Link
                href="/"
                className="flex items-center space-x-2"
                onClick={() => setMobileMenuOpen(false)}
              >
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                  <Github className="h-5 w-5 text-primary-foreground" />
                </div>
                <span className="font-bold">GitHub + Perplexity</span>
              </Link>
              <div className="my-4 h-[calc(100vh-8rem)] pb-10 pl-6">
                <div className="flex flex-col space-y-2">
                  <NavItems mobile onItemClick={() => setMobileMenuOpen(false)} />
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </nav>
  );
};

export default NavigationBar;
