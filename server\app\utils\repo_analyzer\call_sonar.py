import requests
import os
import logging
import json
import hashlib
from typing import Dict, Any, List, Optional
from datetime import datetime

# Configure logging
log_directory = os.getenv("LOG_DIR", "logs")
os.makedirs(log_directory, exist_ok=True)
log_file = os.path.join(
    log_directory, f"sonar_calls_{datetime.now().strftime('%Y%m%d')}.log"
)

# Set up logger
logger = logging.getLogger("sonar_logger")
logger.setLevel(logging.INFO)
logger.propagate = False  # Prevent propagation to root logger
file_handler = logging.FileHandler(log_file, encoding='utf-8')
file_handler.setFormatter(
    logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
)
logger.addHandler(file_handler)

# Simple cache configuration
cache_file = "sonar_cache.json"

class SonarAPI:
    """Perplexity Sonar API client."""
    
    def __init__(self, api_key: str = None):
        """
        Initialize with an API key.
        
        Args:
            api_key: Perplexity API key
        """
        self.api_key = api_key or os.getenv("PERPLEXITY_API_KEY")
        self.base_url = "https://api.perplexity.ai"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def query_sonar(
        self, 
        prompt: str, 
        model: str = "sonar-medium-online", 
        use_cache: bool = True,
        search_context: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Send a query to the Perplexity Sonar API.
        
        Args:
            prompt: The prompt to send
            model: The model to use
            use_cache: Whether to use the cache
            search_context: Whether to enable search for recent information
            **kwargs: Additional parameters for the API
            
        Returns:
            API response with extracted text
        """
        # Log the prompt
        logger.info(f"PROMPT: {prompt}")
        
        # Check cache if enabled
        if use_cache:
            # Load cache from disk
            cache = {}
            if os.path.exists(cache_file):
                try:
                    with open(cache_file, "r", encoding="utf-8") as f:
                        cache = json.load(f)
                except:
                    logger.warning(f"Failed to load cache, starting with empty cache")
            
            # Generate cache key
            cache_key = hashlib.sha256(json.dumps({
                "prompt": prompt,
                "model": model,
                "search_context": search_context,
                **kwargs
            }, sort_keys=True).encode()).hexdigest()

            # Return from cache if exists
            if cache_key in cache:
                logger.info(f"RESPONSE: {cache[cache_key]['text']}")
                return cache[cache_key]
        
        # Prepare request data
        data = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "options": {"search_context": search_context}
        }
        
        # Add additional parameters
        for key, value in kwargs.items():
            if key != "options":
                data[key] = value
            else:
                data["options"].update(value)
        
        # Send request
        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=self.headers,
            json=data
        )
        
        # Check for errors
        if response.status_code != 200:
            logger.error(f"API error: {response.status_code} - {response.text}")
            raise Exception(f"Perplexity API error: {response.text}")
        
        # Parse response
        response_data = response.json()
        
        # Extract text from response
        response_text = ""
        if "choices" in response_data and len(response_data["choices"]) > 0:
            if "message" in response_data["choices"][0]:
                response_text = response_data["choices"][0]["message"].get("content", "")
        
        # Create simplified result
        result = {
            "text": response_text,
            "raw_response": response_data,
            "tokens_used": response_data.get("usage", {}).get("total_tokens", 0),
        }
        
        # Log the response
        logger.info(f"RESPONSE: {result['text']}")
        
        # Update cache if enabled
        if use_cache:
            # Load cache again to avoid overwrites
            cache = {}
            if os.path.exists(cache_file):
                try:
                    with open(cache_file, "r", encoding="utf-8") as f:
                        cache = json.load(f)
                except:
                    pass
            
            # Add to cache and save
            cache[cache_key] = result
            try:
                with open(cache_file, "w", encoding="utf-8") as f:
                    json.dump(cache, f)
            except Exception as e:
                logger.error(f"Failed to save cache: {e}")
        
        return result


def query_dependency_info(dependency: Dict[str, Any], use_cache: bool = True) -> Dict[str, Any]:
    """
    Query information about a dependency using Perplexity Sonar.
    
    Args:
        dependency: Dictionary with dependency information
        use_cache: Whether to use cache
        
    Returns:
        Dictionary with dependency information
    """
    logger.info(f"Querying dependency info for: {dependency.get('name')}") # Added log
    # Create client
    client = SonarAPI()
    
    name = dependency.get("name", "")
    version = dependency.get("version", "")
    dep_type = dependency.get("type", "")
    
    # Create prompt
    prompt = f"""
    I need detailed information about the {dep_type} package/library "{name}" version {version}.

    Please provide the following information:
    1. What is the latest version available?
    2. Are there any known security vulnerabilities in version {version}?
    3. Are there any critical issues or deprecation warnings for this version?
    4. What are the most recommended alternatives to this package, if any?
    5. Is this package actively maintained? When was the last update?
    
    Format your response as follows:

    Latest Version: [version number]
    Is Outdated: [Yes/No]
    Vulnerabilities: [List any CVEs or security issues, "None known" if none]
    Critical Issues: [List any critical issues or "None known"]
    Alternatives: [List recommended alternatives or "None needed"]
    Maintenance Status: [Active/Inactive, last update date]
    Summary: [Brief insights about using this dependency]
    """
    
    # Query Sonar
    response = client.query_sonar(prompt, use_cache=use_cache, search_context=True)
    
    # Extract structured information from response
    result = {
        "name": name,
        "version": version,
        "type": dep_type,
        "latest_version": _extract_field(response["text"], "Latest Version:"),
        "is_outdated": _extract_field(response["text"], "Is Outdated:").lower() == "yes",
        "vulnerabilities": _extract_list_field(response["text"], "Vulnerabilities:"),
        "critical_issues": _extract_list_field(response["text"], "Critical Issues:"),
        "alternatives": _extract_list_field(response["text"], "Alternatives:"),
        "maintenance_status": _extract_field(response["text"], "Maintenance Status:"),
        "summary": _extract_field(response["text"], "Summary:"),
        "tokens_used": response["tokens_used"]
    }
    
    return result


def query_technology_trends(technology: Dict[str, Any], use_cache: bool = True) -> Dict[str, Any]:
    """
    Query trends about a technology using Perplexity Sonar.
    
    Args:
        technology: Dictionary with technology information
        use_cache: Whether to use cache
        
    Returns:
        Dictionary with trend information
    """
    logger.info(f"Querying technology trends for: {technology.get('name')}") # Added log
    # Create client
    client = SonarAPI()
    
    name = technology.get("name", "")
    tech_type = technology.get("type", "")
    
    # Create prompt
    prompt = f"""
    I need detailed information about the current trends and future outlook for the {tech_type} "{name}".

    Please provide the following information:
    1. What are the latest developments or major changes in {name} in the past year?
    2. Is the popularity of {name} increasing or decreasing? Provide some evidence or metrics.
    3. What are the major competitors or alternatives to {name}, and how do they compare?
    4. Are there any concerning issues, deprecation plans, or major shifts that users of {name} should be aware of?
    5. What are 3-5 recent articles or discussions about {name} that would be valuable for users?
    
    Format your response as follows:

    Latest Developments: [List major updates, changes or new features]
    Popularity Trend: [Increasing/Steady/Declining with brief explanation]
    Competitors/Alternatives: [List major alternatives with brief comparisons]
    Issues or Concerns: [Any deprecation warnings, major shifts, or problems]
    Recent Articles: [List articles with titles, brief descriptions and URLs if available]
    Future Outlook: [Brief assessment of future prospects]
    """
    
    # Query Sonar
    response = client.query_sonar(prompt, use_cache=use_cache, search_context=True)
    
    # Extract structured information from response
    result = {
        "name": name,
        "type": tech_type,
        "latest_developments": _extract_list_field(response["text"], "Latest Developments:"),
        "popularity_trend": _extract_field(response["text"], "Popularity Trend:"),
        "alternatives": _extract_list_field(response["text"], "Competitors/Alternatives:"),
        "issues": _extract_list_field(response["text"], "Issues or Concerns:"),
        "articles": _extract_articles(response["text"], "Recent Articles:"),
        "future_outlook": _extract_field(response["text"], "Future Outlook:"),
        "tokens_used": response["tokens_used"]
    }
    
    return result


def query_issue_insights(issue: Dict[str, Any], use_cache: bool = True) -> Dict[str, Any]:
    """
    Query insights about a GitHub issue using Perplexity Sonar.
    
    Args:
        issue: Dictionary with issue information
        use_cache: Whether to use cache
        
    Returns:
        Dictionary with issue insights
    """
    logger.info(f"Querying issue insights for: #{issue.get('number')}") # Added log
    # Create client
    client = SonarAPI()
    
    issue_number = issue.get("number", 0)
    title = issue.get("title", "")
    body = issue.get("body", "")
    
    # Create prompt
    prompt = f"""
    I need help with a GitHub issue:

    Issue #{issue_number}: {title}
    
    Description:
    {body}

    Please analyze this issue and provide the following:
    1. Are there any similar issues or problems discussed online? Find examples.
    2. What are potential solutions to this issue based on public knowledge?
    3. Are there any documentation links, tutorials, or articles that might help solve this issue?
    4. What insights can you provide about the root cause of this issue?
    
    Format your response as follows:

    Similar Issues: [List similar issues found online with brief descriptions and links]
    Potential Solutions: [List possible solutions with explanations]
    Documentation Links: [List relevant documentation, tutorials, or articles with links]
    Insights: [Analysis of the likely root cause and any other observations]
    """
    
    # Query Sonar
    response = client.query_sonar(prompt, use_cache=use_cache, search_context=True)
    
    # Extract structured information from response
    result = {
        "issue_number": issue_number,
        "title": title,
        "similar_issues": _extract_list_field(response["text"], "Similar Issues:"),
        "solutions": _extract_list_field(response["text"], "Potential Solutions:"),
        "documentation_links": _extract_list_field(response["text"], "Documentation Links:"),
        "insights": _extract_field(response["text"], "Insights:"),
        "tokens_used": response["tokens_used"]
    }
    
    return result


def _extract_field(text: str, field_name: str) -> str:
    """Extract a field from a text response."""
    try:
        start_index = text.find(field_name)
        if start_index == -1:
            return ""
        
        # Get text after field name
        start_index += len(field_name)
        end_index = text.find("\n", start_index)
        
        if end_index == -1:
            return text[start_index:].strip()
        
        return text[start_index:end_index].strip()
    except:
        return ""


def _extract_list_field(text: str, field_name: str) -> List[str]:
    """Extract a list field from a text response."""
    try:
        field_value = _extract_field(text, field_name)
        if field_value.lower() in ["none", "none known", "none needed"]:
            return []
        
        # Check if the field spans multiple lines
        start_index = text.find(field_name)
        if start_index == -1:
            return []
        
        start_index += len(field_name)
        next_field_index = float('inf')
        
        # Find the next field
        for field in ["Latest Version:", "Is Outdated:", "Vulnerabilities:", "Critical Issues:", 
                     "Alternatives:", "Maintenance Status:", "Summary:", "Latest Developments:", 
                     "Popularity Trend:", "Competitors/Alternatives:", "Issues or Concerns:", 
                     "Recent Articles:", "Future Outlook:", "Similar Issues:", "Potential Solutions:", 
                     "Documentation Links:", "Insights:"]:
            if field == field_name:
                continue
            
            field_pos = text.find(field, start_index)
            if field_pos != -1 and field_pos < next_field_index:
                next_field_index = field_pos
        
        # Extract the entire field value
        if next_field_index < float('inf'):
            field_value = text[start_index:next_field_index].strip()
        else:
            field_value = text[start_index:].strip()
        
        # Split into list items
        items = []
        for line in field_value.split("\n"):
            line = line.strip()
            if line and (line.startswith("-") or line.startswith("*") or line.startswith("1.") or 
                        line.startswith("2.") or line.startswith("3.") or line.startswith("4.") or 
                        line.startswith("5.")):
                items.append(line[line.find(" ")+1:].strip())
            elif line and not any(f in line for f in ["Latest Version:", "Is Outdated:", "Vulnerabilities:", 
                                                      "Critical Issues:", "Alternatives:", "Maintenance Status:", 
                                                      "Summary:", "Latest Developments:", "Popularity Trend:", 
                                                      "Competitors/Alternatives:", "Issues or Concerns:", 
                                                      "Recent Articles:", "Future Outlook:", "Similar Issues:", 
                                                      "Potential Solutions:", "Documentation Links:", "Insights:"]):
                items.append(line)
        
        return items
    except:
        return []


def _extract_articles(text: str, field_name: str) -> List[Dict[str, str]]:
    """Extract articles from a text response."""
    try:
        articles_text = _extract_list_field(text, field_name)
        articles = []
        
        for article in articles_text:
            # Try to extract URL if present
            url = ""
            title = article
            
            # Look for URLs
            url_start = article.find("http")
            if url_start != -1:
                url_end = article.find(" ", url_start)
                if url_end == -1:
                    url_end = len(article)
                url = article[url_start:url_end].strip()
                title = article[:url_start].strip()
            
            # Look for titles in quotes or brackets
            title_match = None
            import re
            for pattern in [r'"(.*?)"', r"'(.*?)'", r"«(.*?)»", r"„(.*?)\"", r"\[(.*?)\]", r"\((.*?)\)"]:
                match = re.search(pattern, article)
                if match:
                    title_match = match.group(1)
                    break
            
            if title_match:
                title = title_match
            
            # Clean up title
            title = title.strip('"\'«»„"[]()<>')
            
            articles.append({
                "title": title,
                "url": url,
                "description": article
            })
        
        return articles
    except:
        return []
