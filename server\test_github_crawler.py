#!/usr/bin/env python3
"""
Test script for verifying the two-phase GitHub crawling implementation.
This script tests the new intelligent crawling approach and compares it with the old approach.
"""

import json
import time
import os
import sys
from typing import Dict, List, Any

# Add the parent directory to the path to import the modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.utils.tutor.crawl_github_files import (
    crawl_github_files,
    parse_github_url,
    get_repository_metadata,
    get_repository_tree,
    phase1_initial_analysis,
    phase2_adaptive_refinement,
    identify_abstractions_from_files
)


def print_section(title: str):
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}\n")


def test_parse_github_url():
    """Test the GitHub URL parsing functionality."""
    print_section("Testing GitHub URL Parsing")
    
    test_urls = [
        "https://github.com/octocat/Hello-World",
        "https://github.com/octocat/Hello-World/tree/main",
        "https://github.com/octocat/Hello-World/tree/main/src",
        "https://github.com/octocat/Hello-World/blob/main/README.md",
    ]
    
    for url in test_urls:
        try:
            owner, repo, ref, path = parse_github_url(url)
            print(f"URL: {url}")
            print(f"  Owner: {owner}, Repo: {repo}, Ref: {ref}, Path: {path}")
        except Exception as e:
            print(f"Error parsing {url}: {e}")
    
    return True


def test_repository_metadata(owner: str, repo: str, token: str = None):
    """Test fetching repository metadata."""
    print_section("Testing Repository Metadata Fetch")
    
    try:
        metadata = get_repository_metadata(owner, repo, token)
        print(f"Repository: {owner}/{repo}")
        print(f"  Language: {metadata.get('language', 'Unknown')}")
        print(f"  Stars: {metadata.get('stargazers_count', 0)}")
        print(f"  Size: {metadata.get('size', 0)} KB")
        print(f"  Default Branch: {metadata.get('default_branch', 'Unknown')}")
        return True
    except Exception as e:
        print(f"Error fetching metadata: {e}")
        return False


def test_two_phase_crawling(repo_url: str, token: str = None):
    """Test the two-phase crawling approach."""
    print_section("Testing Two-Phase Crawling")
    
    try:
        # Parse the URL
        owner, repo, ref, specific_path = parse_github_url(repo_url)
        print(f"Testing on: {owner}/{repo}")
        
        # Phase 1: Initial analysis
        print("\n--- Phase 1: Initial Structure Analysis ---")
        start_time = time.time()
        initial_files, phase1_skipped = phase1_initial_analysis(
            owner, repo, ref, token,
            max_file_size=100000,  # 100KB for phase 1
            specific_path=specific_path
        )
        phase1_time = time.time() - start_time
        
        print(f"Phase 1 Results:")
        print(f"  Files downloaded: {len(initial_files)}")
        print(f"  Files skipped: {len(phase1_skipped)}")
        print(f"  Time taken: {phase1_time:.2f} seconds")
        print(f"  Sample files:")
        for path in list(initial_files.keys())[:5]:
            print(f"    - {path}")
        
        # Identify abstractions
        print("\n--- Identifying Abstractions ---")
        abstractions = identify_abstractions_from_files(initial_files, repo)
        print(f"Found {len(abstractions)} abstractions:")
        for i, abstr in enumerate(abstractions):
            print(f"  {i+1}. {abstr.get('name', 'Unknown')}")
            print(f"     {abstr.get('description', 'No description')[:100]}...")
        
        # Phase 2: Adaptive refinement
        if abstractions:
            print("\n--- Phase 2: Adaptive Refinement ---")
            start_time = time.time()
            
            # Get the complete tree
            tree_items = get_repository_tree(owner, repo, ref or "main", token)
            
            phase2_files, phase2_skipped = phase2_adaptive_refinement(
                owner, repo, ref, token,
                abstractions=abstractions,
                already_downloaded=set(initial_files.keys()),
                tree_items=tree_items,
                max_additional_files=20,
                max_file_size=1024*1024  # 1MB for phase 2
            )
            phase2_time = time.time() - start_time
            
            print(f"Phase 2 Results:")
            print(f"  Additional files downloaded: {len(phase2_files)}")
            print(f"  Additional files skipped: {len(phase2_skipped)}")
            print(f"  Time taken: {phase2_time:.2f} seconds")
            print(f"  Sample additional files:")
            for path in list(phase2_files.keys())[:5]:
                print(f"    - {path}")
        
        # Total statistics
        print(f"\n--- Total Statistics ---")
        total_files = len(initial_files) + (len(phase2_files) if abstractions else 0)
        total_skipped = len(phase1_skipped) + (len(phase2_skipped) if abstractions else 0)
        total_time = phase1_time + (phase2_time if abstractions else 0)
        
        print(f"Total files downloaded: {total_files}")
        print(f"Total files skipped: {total_skipped}")
        print(f"Total time: {total_time:.2f} seconds")
        
        return True
        
    except Exception as e:
        print(f"Error in two-phase crawling: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_crawl_github_files_comparison(repo_url: str, token: str = None):
    """Compare the new two-phase approach with the old approach."""
    print_section("Comparing Two-Phase vs Single-Phase Crawling")
    
    try:
        # Test with two-phase enabled (new approach)
        print("\n--- Testing with Two-Phase Enabled ---")
        start_time = time.time()
        result_two_phase = crawl_github_files(
            repo_url=repo_url,
            token=token,
            max_file_size=1024*1024,  # 1MB
            enable_two_phase=True
        )
        two_phase_time = time.time() - start_time
        
        print(f"Two-Phase Results:")
        print(f"  Files: {result_two_phase['stats']['downloaded_count']}")
        print(f"  Phase 1 files: {result_two_phase['stats'].get('phase1_files', 0)}")
        print(f"  Phase 2 files: {result_two_phase['stats'].get('phase2_files', 0)}")
        print(f"  Abstractions found: {result_two_phase['stats'].get('abstractions_found', 0)}")
        print(f"  Time: {two_phase_time:.2f} seconds")
        
        # Test with two-phase disabled (old approach simulation)
        print("\n--- Testing with Two-Phase Disabled ---")
        start_time = time.time()
        result_single_phase = crawl_github_files(
            repo_url=repo_url,
            token=token,
            max_file_size=1024*1024,  # 1MB
            enable_two_phase=False
        )
        single_phase_time = time.time() - start_time
        
        print(f"Single-Phase Results:")
        print(f"  Files: {result_single_phase['stats']['downloaded_count']}")
        print(f"  Time: {single_phase_time:.2f} seconds")
        
        # Comparison
        print(f"\n--- Comparison ---")
        print(f"Time difference: {abs(two_phase_time - single_phase_time):.2f} seconds")
        print(f"File count difference: {abs(result_two_phase['stats']['downloaded_count'] - result_single_phase['stats']['downloaded_count'])}")
        
        # Estimate API calls (rough approximation)
        # Two-phase: 1 for metadata + 1 for tree + N for file downloads
        # Single-phase: 1 for tree + N for file downloads
        two_phase_api_calls = 2 + result_two_phase['stats']['downloaded_count']
        single_phase_api_calls = 1 + result_single_phase['stats']['downloaded_count']
        
        print(f"\nEstimated API calls:")
        print(f"  Two-phase: ~{two_phase_api_calls}")
        print(f"  Single-phase: ~{single_phase_api_calls}")
        print(f"  Difference: {abs(two_phase_api_calls - single_phase_api_calls)}")
        
        return True
        
    except Exception as e:
        print(f"Error in comparison test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_error_handling(token: str = None):
    """Test error handling in the crawler."""
    print_section("Testing Error Handling")
    
    # Test with non-existent repository
    print("\n--- Testing with non-existent repository ---")
    try:
        result = crawl_github_files(
            repo_url="https://github.com/nonexistent-user-12345/nonexistent-repo-67890",
            token=token,
            enable_two_phase=True
        )
        print(f"Result: {result['stats'].get('error', 'No error')}")
    except Exception as e:
        print(f"Caught expected error: {e}")
    
    # Test with invalid URL
    print("\n--- Testing with invalid URL ---")
    try:
        result = crawl_github_files(
            repo_url="https://invalid-url.com/repo",
            token=token,
            enable_two_phase=True
        )
        print(f"Result: {result['stats'].get('error', 'No error')}")
    except Exception as e:
        print(f"Caught expected error: {e}")
    
    return True


def test_pattern_filtering(repo_url: str, token: str = None):
    """Test include/exclude pattern filtering."""
    print_section("Testing Pattern Filtering")
    
    try:
        # Test with include patterns
        print("\n--- Testing with include patterns (*.py files only) ---")
        result = crawl_github_files(
            repo_url=repo_url,
            token=token,
            include_patterns="*.py",
            enable_two_phase=True
        )
        
        print(f"Files downloaded: {result['stats']['downloaded_count']}")
        print("Sample files:")
        for path in list(result['files'].keys())[:5]:
            print(f"  - {path}")
        
        # Verify all files are .py
        non_py_files = [f for f in result['files'].keys() if not f.endswith('.py')]
        if non_py_files:
            print(f"WARNING: Found {len(non_py_files)} non-Python files!")
        else:
            print("✓ All files are Python files")
        
        # Test with exclude patterns
        print("\n--- Testing with exclude patterns (exclude test files) ---")
        result = crawl_github_files(
            repo_url=repo_url,
            token=token,
            exclude_patterns={"*test*", "*Test*"},
            enable_two_phase=True
        )
        
        print(f"Files downloaded: {result['stats']['downloaded_count']}")
        
        # Check if any test files were included
        test_files = [f for f in result['files'].keys() if 'test' in f.lower()]
        if test_files:
            print(f"WARNING: Found {len(test_files)} test files!")
            for f in test_files[:3]:
                print(f"  - {f}")
        else:
            print("✓ No test files included")
        
        return True
        
    except Exception as e:
        print(f"Error in pattern filtering test: {e}")
        return False


def main():
    """Main test function."""
    print("GitHub Crawler Two-Phase Implementation Test Suite")
    print("=" * 60)
    
    # Configuration
    # Using a small public repository for testing
    test_repo_url = "https://github.com/octocat/Hello-World"
    
    # You can set a GitHub token here if needed for private repos or higher rate limits
    github_token = os.environ.get("GITHUB_TOKEN", None)
    
    if github_token:
        print("✓ GitHub token found in environment")
    else:
        print("ℹ No GitHub token found, using anonymous access (lower rate limits)")
    
    # Run tests
    all_passed = True
    
    # Test 1: URL parsing
    if not test_parse_github_url():
        all_passed = False
    
    # Test 2: Repository metadata
    owner, repo, _, _ = parse_github_url(test_repo_url)
    if not test_repository_metadata(owner, repo, github_token):
        all_passed = False
    
    # Test 3: Two-phase crawling
    if not test_two_phase_crawling(test_repo_url, github_token):
        all_passed = False
    
    # Test 4: Comparison between approaches
    if not test_crawl_github_files_comparison(test_repo_url, github_token):
        all_passed = False
    
    # Test 5: Error handling
    if not test_error_handling(github_token):
        all_passed = False
    
    # Test 6: Pattern filtering
    if not test_pattern_filtering(test_repo_url, github_token):
        all_passed = False
    
    # Summary
    print_section("Test Summary")
    if all_passed:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    return 0 if all_passed else 1


if __name__ == "__main__":
    sys.exit(main())