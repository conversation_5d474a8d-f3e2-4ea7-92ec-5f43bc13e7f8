from typing import Dict, Any
from fastapi import APIRouter, Request, HTTPException, status, BackgroundTasks, Depends
from sqlalchemy.orm import Session
import hmac
import hashlib
import json
import logging

from ..database import get_db
from ..config import settings
from ..models.core import Repository
from ..models.github import GithubWebhook
from ..models.repository_analysis import IssueAugmentation
from ..utils.repository_analyzer import RepositoryAnalyzerService

logger = logging.getLogger(__name__)
router = APIRouter()

def verify_github_signature(payload_body: bytes, signature_header: str, secret: str) -> bool:
    """Verify that the payload was sent from GitHub by validating SHA256 signature."""
    if not signature_header:
        return False
    
    hash_object = hmac.new(secret.encode('utf-8'), msg=payload_body, digestmod=hashlib.sha256)
    expected_signature = "sha256=" + hash_object.hexdigest()
    
    return hmac.compare_digest(expected_signature, signature_header)

@router.post("/github")
async def github_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Handle GitHub webhook events for repository analysis and issue augmentation.
    """
    # Get the raw payload
    payload_body = await request.body()
    signature_header = request.headers.get('X-Hub-Signature-256')
    event_type = request.headers.get('X-GitHub-Event')
    
    # Verify the webhook signature (if secret is configured)
    webhook_secret = getattr(settings, 'GITHUB_WEBHOOK_SECRET', None)
    if webhook_secret and not verify_github_signature(payload_body, signature_header, webhook_secret):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid signature"
        )
    
    try:
        payload = json.loads(payload_body.decode('utf-8'))
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid JSON payload"
        )
    
    # Store the webhook event
    webhook_event = GithubWebhook(
        event_type=event_type,
        payload=payload,
        processed=False
    )
    
    # Try to find the repository
    if 'repository' in payload:
        repo_data = payload['repository']
        repository = db.query(Repository).filter(
            Repository.github_id == str(repo_data['id'])
        ).first()
        
        if repository:
            webhook_event.repository_id = repository.id
    
    db.add(webhook_event)
    db.commit()
    db.refresh(webhook_event)
    
    # Process the webhook event in the background
    background_tasks.add_task(
        process_webhook_event,
        webhook_event.id,
        event_type,
        payload,
        db
    )
    
    return {"status": "received"}

async def process_webhook_event(
    webhook_id: int,
    event_type: str,
    payload: Dict[str, Any],
    db: Session
):
    """Process GitHub webhook events in the background."""
    
    try:
        if event_type == "issues":
            await handle_issues_event(payload, db)
        elif event_type == "issue_comment":
            await handle_issue_comment_event(payload, db)
        elif event_type == "push":
            await handle_push_event(payload, db)
        elif event_type == "pull_request":
            await handle_pull_request_event(payload, db)
        
        # Mark webhook as processed
        webhook = db.query(GithubWebhook).filter(GithubWebhook.id == webhook_id).first()
        if webhook:
            webhook.processed = True
            db.commit()
            
    except Exception as e:
        logger.error(f"Failed to process webhook {webhook_id}: {e}")

async def handle_issues_event(payload: Dict[str, Any], db: Session):
    """Handle GitHub issues events for automatic issue augmentation."""
    
    action = payload.get('action')
    issue = payload.get('issue', {})
    repository_data = payload.get('repository', {})
    
    # Only process opened issues
    if action != 'opened':
        return
    
    # Find the repository in our database
    repository = db.query(Repository).filter(
        Repository.github_id == str(repository_data['id'])
    ).first()
    
    if not repository:
        logger.warning(f"Repository {repository_data['full_name']} not found in database")
        return
    
    # Check if we already have an augmentation for this issue
    existing_augmentation = db.query(IssueAugmentation).filter(
        IssueAugmentation.repository_id == repository.id,
        IssueAugmentation.issue_number == issue['number']
    ).first()
    
    if existing_augmentation:
        logger.info(f"Issue augmentation already exists for issue #{issue['number']}")
        return
    
    # Initialize the repository analyzer
    analyzer = RepositoryAnalyzerService()
    
    try:
        # Analyze the issue with Perplexity Sonar
        augmentation = await analyzer.analyze_issue_with_sonar(
            repository_id=repository.id,
            issue_number=issue['number'],
            issue_title=issue['title'],
            issue_body=issue.get('body', ''),
            db=db
        )
        
        # Store the GitHub issue ID for reference
        augmentation.github_issue_id = issue['id']
        augmentation.issue_labels = [label['name'] for label in issue.get('labels', [])]
        db.commit()
        
        logger.info(f"Successfully analyzed issue #{issue['number']} for repository {repository.full_name}")
        
        # TODO: Optionally post the suggested comment to GitHub
        # This would require GitHub API integration with proper permissions
        
    except Exception as e:
        logger.error(f"Failed to analyze issue #{issue['number']}: {e}")

async def handle_issue_comment_event(payload: Dict[str, Any], db: Session):
    """Handle issue comment events."""
    # This could be used to trigger re-analysis or update existing augmentations
    pass

async def handle_push_event(payload: Dict[str, Any], db: Session):
    """Handle push events to trigger repository re-analysis."""
    
    repository_data = payload.get('repository', {})
    
    # Find the repository in our database
    repository = db.query(Repository).filter(
        Repository.github_id == str(repository_data['id'])
    ).first()
    
    if not repository:
        return
    
    # Check if this is a push to the main/master branch
    ref = payload.get('ref', '')
    if not (ref.endswith('/main') or ref.endswith('/master')):
        return
    
    # TODO: Trigger automatic repository re-analysis
    # This could be implemented to automatically update dependency analysis
    # when new commits are pushed to the main branch
    
    logger.info(f"Push to main branch detected for repository {repository.full_name}")

async def handle_pull_request_event(payload: Dict[str, Any], db: Session):
    """Handle pull request events."""
    # This could be used to analyze PRs for security issues or dependency changes
    pass

@router.get("/github/events")
async def get_webhook_events(
    skip: int = 0,
    limit: int = 100,
    processed: bool = None,
    db: Session = Depends(get_db)
):
    """Get GitHub webhook events."""
    
    query = db.query(GithubWebhook)
    
    if processed is not None:
        query = query.filter(GithubWebhook.processed == processed)
    
    events = query.order_by(GithubWebhook.created_at.desc()).offset(skip).limit(limit).all()
    
    return events

@router.post("/github/events/{event_id}/reprocess")
async def reprocess_webhook_event(
    event_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Reprocess a specific webhook event."""
    
    webhook = db.query(GithubWebhook).filter(GithubWebhook.id == event_id).first()
    if not webhook:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Webhook event not found"
        )
    
    # Reset processed status
    webhook.processed = False
    db.commit()
    
    # Reprocess the event
    background_tasks.add_task(
        process_webhook_event,
        webhook.id,
        webhook.event_type,
        webhook.payload,
        db
    )
    
    return {"status": "reprocessing"} 