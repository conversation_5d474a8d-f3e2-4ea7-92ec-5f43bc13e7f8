from typing import Optional, Dict, Any
from pydantic import BaseModel, Field

class TokenAuth(BaseModel):
    """Schema for token authentication."""
    token: str


class Token(BaseModel):
    """Schema for JWT token response."""
    access_token: str
    refresh_token: str
    token_type: str
    user: Dict[str, Any]

class TokenRefresh(BaseModel):
    """Schema for token refresh."""
    token: str
    message: Optional[str] = None

class UserBase(BaseModel):
    """Base schema for user."""
    username: str
    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: bool = True

class User(UserBase):
    """Schema for user response."""
    id: int

    class Config:
        from_attributes = True

class UserProfile(BaseModel):
    """Schema for user profile."""
    id: int
    user_id: int
    github_id: str
    github_username: str
    github_avatar_url: Optional[str] = None
    is_active: bool = True

    class Config:
        from_attributes = True
