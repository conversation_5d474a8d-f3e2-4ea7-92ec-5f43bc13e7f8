import json
import re
import os
import asyncio
import aiohttp
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import logging
from datetime import datetime
from github import Github, Repository as GithubRepo
from sqlalchemy.orm import Session

from ..config import settings
from ..models.repository_analysis import (
    RepositoryAnalysis, DependencyAnalysis, TechnologyTrend, 
    SecurityFinding, IssueAugmentation
)
from ..api.perplexity import PerplexityAPI

logger = logging.getLogger(__name__)

class RepositoryAnalyzerService:
    """Enhanced repository analyzer with Perplexity Sonar integration."""
    
    def __init__(self, github_token: str = None):
        # Check if Perplexity API key is configured
        if not settings.PERPLEXITY_API_KEY:
            logger.warning("Perplexity API key not configured. Sonar analysis will be disabled.")
            self.perplexity = None
        else:
            self.perplexity = PerplexityAPI()
        
        # Initialize GitHub client (handle None token gracefully)
        if github_token:
            try:
                self.github = Github(github_token)
                logger.info(f"GitHub client initialized with token")
            except Exception as e:
                logger.error(f"Failed to initialize GitHub client: {e}")
                self.github = None
        else:
            logger.warning("No GitHub token provided. GitHub API access will be limited.")
            self.github = None
        
        # Dependency file patterns
        self.dependency_files = {
            'package.json': 'npm',
            'package-lock.json': 'npm',
            'yarn.lock': 'yarn',
            'requirements.txt': 'pip',
            'Pipfile': 'pipenv',
            'poetry.lock': 'poetry',
            'Gemfile': 'bundle',
            'pom.xml': 'maven',
            'build.gradle': 'gradle',
            'composer.json': 'composer',
            'go.mod': 'go',
            'Cargo.toml': 'cargo'
        }
        
        # Technology patterns for file extensions
        self.tech_patterns = {
            '.js': 'JavaScript',
            '.jsx': 'React',
            '.ts': 'TypeScript',
            '.tsx': 'React TypeScript',
            '.py': 'Python',
            '.java': 'Java',
            '.go': 'Go',
            '.rs': 'Rust',
            '.php': 'PHP',
            '.rb': 'Ruby',
            '.cs': 'C#',
            '.cpp': 'C++',
            '.c': 'C',
            '.swift': 'Swift',
            '.kt': 'Kotlin',
            '.scala': 'Scala',
            '.html': 'HTML',
            '.css': 'CSS',
            '.scss': 'SCSS',
            '.sass': 'SASS',
            '.vue': 'Vue.js',
            '.svelte': 'Svelte'
        }

    async def analyze_repository_full(
        self, 
        repo_owner: str, 
        repo_name: str, 
        db: Session,
        repository_id: int
    ) -> RepositoryAnalysis:
        """Perform comprehensive repository analysis."""
        
        logger.info(f"=== ANALYZER: Starting full analysis for {repo_owner}/{repo_name} ===")
        
        # Create analysis record
        analysis = RepositoryAnalysis(
            repository_id=repository_id,
            analysis_type="full",
            status="in_progress"
        )
        db.add(analysis)
        db.commit()
        db.refresh(analysis)
        logger.info(f"=== ANALYZER: Analysis record created with ID: {analysis.id} ===")
        
        try:
            # Check if GitHub client is available
            if not self.github:
                logger.error(f"=== ANALYZER: GitHub client not available ===")
                raise Exception("GitHub client not initialized. Cannot access repository.")
            
            # Get GitHub repository
            logger.info(f"=== ANALYZER: Getting GitHub repository: {repo_owner}/{repo_name} ===")
            github_repo = self.github.get_repo(f"{repo_owner}/{repo_name}")
            logger.info(f"=== ANALYZER: Got repository: {github_repo.full_name} ===")
            
            # Parallel analysis tasks
            logger.info(f"=== ANALYZER: Starting parallel analysis tasks ===")
            tasks = [
                self._analyze_dependencies(github_repo, analysis.id, db),
                self._analyze_technologies(github_repo, analysis.id, db),
                self._analyze_security(github_repo, analysis.id, db)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            logger.info(f"=== ANALYZER: Parallel tasks completed ===")
            
            # Process results
            dependencies_count = 0
            vulnerabilities_count = 0
            tech_score = 0.0
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Analysis task {i} failed: {result}")
                    continue
                    
                if i == 0 and isinstance(result, dict):  # Dependencies
                    dependencies_count = result.get('count', 0)
                    vulnerabilities_count = result.get('vulnerabilities', 0)
                elif i == 1 and isinstance(result, dict):  # Technologies
                    tech_score = result.get('score', 0.0)
            
            # Update analysis
            analysis.status = "completed"
            analysis.completed_at = datetime.utcnow()
            analysis.dependencies_count = dependencies_count
            analysis.security_vulnerabilities_count = vulnerabilities_count
            analysis.technology_score = tech_score
            
            logger.info(f"=== ANALYZER: Analysis completed successfully ===")
            db.commit()
            return analysis
            
        except Exception as e:
            logger.error(f"=== ANALYZER: Repository analysis failed: {type(e).__name__} - {str(e)} ===", exc_info=True)
            analysis.status = "failed"
            analysis.error_message = str(e)
            analysis.completed_at = datetime.utcnow()
            db.commit()
            raise

    async def _analyze_dependencies(
        self, 
        github_repo: GithubRepo, 
        analysis_id: int, 
        db: Session
    ) -> Dict[str, Any]:
        """Analyze repository dependencies using Perplexity Sonar."""
        
        dependencies = []
        vulnerabilities_count = 0
        
        # Find dependency files
        for filename, package_manager in self.dependency_files.items():
            try:
                file_content = github_repo.get_contents(filename)
                if file_content.type == 'file':
                    content = file_content.decoded_content.decode('utf-8')
                    deps = await self._parse_dependencies(content, package_manager, filename)
                    
                    for dep in deps:
                        # Use Perplexity Sonar to analyze each dependency
                        sonar_analysis = await self._analyze_dependency_with_sonar(
                            dep['name'], dep['version'], package_manager
                        )
                        
                        # Create dependency analysis record
                        dep_analysis = DependencyAnalysis(
                            analysis_id=analysis_id,
                            package_name=dep['name'],
                            current_version=dep['version'],
                            latest_version=sonar_analysis.get('latest_version'),
                            package_manager=package_manager,
                            is_outdated=sonar_analysis.get('is_outdated', False),
                            versions_behind=sonar_analysis.get('versions_behind', 0),
                            has_vulnerabilities=sonar_analysis.get('has_vulnerabilities', False),
                            vulnerability_count=sonar_analysis.get('vulnerability_count', 0),
                            sonar_analysis=sonar_analysis,
                            alternative_packages=sonar_analysis.get('alternatives'),
                            recent_discussions=sonar_analysis.get('discussions')
                        )
                        
                        db.add(dep_analysis)
                        dependencies.append(dep_analysis)
                        
                        if dep_analysis.has_vulnerabilities:
                            vulnerabilities_count += dep_analysis.vulnerability_count
                            
            except Exception as e:
                logger.warning(f"Could not analyze {filename}: {e}")
                continue
        
        db.commit()
        return {
            'count': len(dependencies),
            'vulnerabilities': vulnerabilities_count
        }

    async def _analyze_technologies(
        self, 
        github_repo: GithubRepo, 
        analysis_id: int, 
        db: Session
    ) -> Dict[str, Any]:
        """Analyze technologies used in the repository."""
        
        # Get repository contents
        tech_usage = {}
        total_files = 0
        
        try:
            contents = github_repo.get_contents("")
            await self._scan_directory_for_tech(contents, tech_usage, github_repo)
            
            total_files = sum(tech_usage.values())
            
            # Analyze each technology with Perplexity Sonar
            tech_score = 0.0
            tech_analyses = []
            
            # Limit the number of technologies to analyze to prevent long-running tasks
            max_technologies = 5
            analyzed_count = 0
            
            for tech, file_count in tech_usage.items():
                if file_count > 0 and analyzed_count < max_technologies:
                    analyzed_count += 1
                    usage_percentage = (file_count / total_files) * 100
                    
                    logger.info(f"=== TECH ANALYSIS: Analyzing technology {analyzed_count}/{min(max_technologies, len(tech_usage))}: {tech} ===")
                    
                    # Use Sonar to analyze technology trends
                    trend_analysis = await self._analyze_technology_trends(tech)
                    
                    tech_analysis = TechnologyTrend(
                        analysis_id=analysis_id,
                        technology_name=tech,
                        technology_type=self._get_technology_type(tech),
                        usage_percentage=usage_percentage,
                        trend_direction=trend_analysis.get('direction', 'stable'),
                        trend_score=trend_analysis.get('score', 0.0),
                        popularity_rank=trend_analysis.get('rank'),
                        recent_articles=trend_analysis.get('articles'),
                        comparisons=trend_analysis.get('comparisons'),
                        migration_guides=trend_analysis.get('migration_guides'),
                        community_sentiment=trend_analysis.get('sentiment')
                    )
                    
                    db.add(tech_analysis)
                    tech_analyses.append(tech_analysis)
                    
                    # Calculate weighted technology score
                    tech_score += (usage_percentage / 100) * trend_analysis.get('score', 0.0)
                    
                    logger.info(f"=== TECH ANALYSIS: Completed {tech} analysis ===")
                elif analyzed_count >= max_technologies:
                    logger.info(f"=== TECH ANALYSIS: Skipping {tech} - reached maximum of {max_technologies} technologies ===")
            
            logger.info(f"=== TECH ANALYSIS: Analyzed {analyzed_count} technologies out of {len(tech_usage)} total ===")
            db.commit()
            return {'score': tech_score}
            
        except Exception as e:
            logger.error(f"Technology analysis failed: {e}")
            return {'score': 0.0}

    async def _scan_directory_for_tech(
        self, 
        contents, 
        tech_usage: Dict[str, int], 
        github_repo: GithubRepo,
        max_depth: int = 3,
        current_depth: int = 0
    ):
        """Recursively scan directory for technology usage."""
        
        if current_depth >= max_depth:
            return
            
        for content in contents:
            if content.type == "file":
                file_path = content.path
                extension = Path(file_path).suffix.lower()
                
                if extension in self.tech_patterns:
                    tech = self.tech_patterns[extension]
                    tech_usage[tech] = tech_usage.get(tech, 0) + 1
                    
            elif content.type == "dir" and not self._should_skip_directory(content.path):
                try:
                    sub_contents = github_repo.get_contents(content.path)
                    await self._scan_directory_for_tech(
                        sub_contents, tech_usage, github_repo, max_depth, current_depth + 1
                    )
                except Exception as e:
                    logger.warning(f"Could not scan directory {content.path}: {e}")

    def _should_skip_directory(self, path: str) -> bool:
        """Check if directory should be skipped during analysis."""
        skip_dirs = {
            'node_modules', '.git', '__pycache__', '.next', 'dist', 
            'build', 'target', 'vendor', '.venv', 'venv'
        }
        return any(skip_dir in path for skip_dir in skip_dirs)

    async def _analyze_dependency_with_sonar(
        self, 
        package_name: str, 
        current_version: str, 
        package_manager: str
    ) -> Dict[str, Any]:
        """Use Perplexity Sonar to analyze a specific dependency."""
        
        # If Perplexity is not available, return basic analysis
        if not self.perplexity:
            logger.warning(f"Skipping Sonar analysis for {package_name} - Perplexity API not configured")
            return {
                'latest_version': None,
                'is_outdated': False,
                'has_vulnerabilities': False,
                'vulnerability_count': 0,
                'alternatives': None,
                'discussions': None
            }
        
        try:
            logger.info(f"=== DEPENDENCY SONAR: Analyzing {package_name} ({package_manager}) ===")
            
            # Query for latest version and vulnerabilities with simpler timeout
            version_query = f"What is the latest version of {package_name} {package_manager} package? Are there any known security vulnerabilities in version {current_version}?"
            
            try:
                version_response = self.perplexity.query(
                    version_query,
                    model="sonar-pro",
                    use_cache=True
                )
            except Exception as e:
                logger.warning(f"=== DEPENDENCY SONAR: Version query failed for {package_name}: {e} ===")
                version_response = {}
            
            # Query for alternatives and discussions with simpler timeout
            alternatives_query = f"What are popular alternatives to {package_name} {package_manager} package? Recent community discussions about issues or migration?"
            
            try:
                alternatives_response = self.perplexity.query(
                    alternatives_query,
                    model="sonar-pro",
                    use_cache=True
                )
            except Exception as e:
                logger.warning(f"=== DEPENDENCY SONAR: Alternatives query failed for {package_name}: {e} ===")
                alternatives_response = {}
            
            # Parse responses (simplified - would need more sophisticated parsing)
            analysis = {
                'latest_version': self._extract_version_from_response(version_response),
                'is_outdated': self._check_if_outdated(current_version, version_response),
                'has_vulnerabilities': 'vulnerability' in version_response.get('choices', [{}])[0].get('message', {}).get('content', '').lower() if version_response else False,
                'vulnerability_count': self._count_vulnerabilities_in_response(version_response),
                'alternatives': self._extract_alternatives_from_response(alternatives_response),
                'discussions': self._extract_discussions_from_response(alternatives_response)
            }
            
            logger.info(f"=== DEPENDENCY SONAR: Completed analysis for {package_name} ===")
            return analysis
            
        except Exception as e:
            logger.error(f"=== DEPENDENCY SONAR: Failed for {package_name}: {type(e).__name__} - {str(e)} ===")
            return {
                'latest_version': None,
                'is_outdated': False,
                'has_vulnerabilities': False,
                'vulnerability_count': 0,
                'alternatives': None,
                'discussions': None
            }

    async def _analyze_technology_trends(self, technology: str) -> Dict[str, Any]:
        """Use Perplexity Sonar to analyze technology trends."""
        
        # If Perplexity is not available, return basic analysis
        if not self.perplexity:
            logger.warning(f"Skipping Sonar trend analysis for {technology} - Perplexity API not configured")
            return {
                'direction': 'stable',
                'score': 0.0,
                'rank': None,
                'articles': None,
                'comparisons': None,
                'migration_guides': None,
                'sentiment': None
            }
        
        try:
            logger.info(f"=== TECHNOLOGY SONAR: Analyzing trends for {technology} ===")
            
            # Query for technology trends and comparisons with simpler timeout
            trends_query = f"What are the current trends for {technology} in 2024? Is it rising, stable, or declining in popularity? Recent articles and comparisons with alternatives?"
            
            try:
                trends_response = self.perplexity.query(
                    trends_query,
                    model="sonar-pro",
                    use_cache=True
                )
            except Exception as e:
                logger.warning(f"=== TECHNOLOGY SONAR: Trends query failed for {technology}: {e} ===")
                trends_response = {}
            
            # Query for migration and community sentiment with simpler timeout
            community_query = f"What is the community sentiment about {technology}? Any migration guides or major updates in 2024?"
            
            try:
                community_response = self.perplexity.query(
                    community_query,
                    model="sonar-pro",
                    use_cache=True
                )
            except Exception as e:
                logger.warning(f"=== TECHNOLOGY SONAR: Community query failed for {technology}: {e} ===")
                community_response = {}
            
            analysis = {
                'direction': self._extract_trend_direction(trends_response),
                'score': self._calculate_trend_score(trends_response),
                'rank': self._extract_popularity_rank(trends_response),
                'articles': self._extract_articles_from_response(trends_response),
                'comparisons': self._extract_comparisons_from_response(trends_response),
                'migration_guides': self._extract_migration_guides(community_response),
                'sentiment': self._extract_sentiment(community_response)
            }
            
            logger.info(f"=== TECHNOLOGY SONAR: Completed analysis for {technology} ===")
            return analysis
            
        except Exception as e:
            logger.error(f"=== TECHNOLOGY SONAR: Failed for {technology}: {type(e).__name__} - {str(e)} ===")
            return {
                'direction': 'stable',
                'score': 0.0,
                'rank': None,
                'articles': None,
                'comparisons': None,
                'migration_guides': None,
                'sentiment': None
            }

    async def _analyze_security(
        self, 
        github_repo: GithubRepo, 
        analysis_id: int, 
        db: Session
    ) -> Dict[str, Any]:
        """Analyze security issues using Perplexity Sonar."""
        
        # If Perplexity is not available, return basic analysis
        if not self.perplexity:
            logger.warning(f"Skipping Sonar security analysis for {github_repo.name} - Perplexity API not configured")
            return {'findings_count': 0}
        
        try:
            logger.info(f"=== SECURITY SONAR: Analyzing security for {github_repo.name} ===")
            
            # Get repository information for security analysis
            repo_info = {
                'name': github_repo.name,
                'language': github_repo.language,
                'description': github_repo.description
            }
            
            # Query for general security recommendations with simpler timeout
            security_query = f"What are common security vulnerabilities and best practices for {repo_info['language']} projects? Recent security advisories and recommendations for 2024?"
            
            try:
                security_response = self.perplexity.query(
                    security_query,
                    model="sonar-pro",
                    use_cache=True
                )
            except Exception as e:
                logger.warning(f"=== SECURITY SONAR: Security query failed for {github_repo.name}: {e} ===")
                security_response = {}
            
            # Create general security findings
            findings = self._extract_security_findings(security_response, analysis_id, db)
            
            logger.info(f"=== SECURITY SONAR: Completed security analysis for {github_repo.name} ===")
            return {'findings_count': len(findings)}
            
        except Exception as e:
            logger.error(f"=== SECURITY SONAR: Failed for {github_repo.name}: {type(e).__name__} - {str(e)} ===")
            return {'findings_count': 0}

    async def analyze_issue_with_sonar(
        self, 
        repository_id: int, 
        issue_number: int, 
        issue_title: str, 
        issue_body: str,
        db: Session
    ) -> IssueAugmentation:
        """Analyze a GitHub issue using Perplexity Sonar."""
        
        # Create issue augmentation record
        augmentation = IssueAugmentation(
            repository_id=repository_id,
            issue_number=issue_number,
            issue_title=issue_title,
            issue_body=issue_body,
            analysis_status="pending"
        )
        db.add(augmentation)
        db.commit()
        db.refresh(augmentation)
        
        # If Perplexity is not available, mark as completed but with no analysis
        if not self.perplexity:
            logger.warning(f"Skipping Sonar issue analysis - Perplexity API not configured")
            augmentation.analysis_status = "completed"
            augmentation.suggested_comment = "Automated analysis is not available - Perplexity API not configured."
            db.commit()
            return augmentation
        
        try:
            # Query for similar issues and solutions with simpler timeout
            similar_issues_query = f"GitHub issues similar to: '{issue_title}'. {issue_body[:500]}... Find similar problems and solutions from other repositories."
            
            try:
                similar_response = self.perplexity.query(
                    similar_issues_query,
                    model="sonar-pro",
                    use_cache=True
                )
            except Exception as e:
                logger.warning(f"=== ISSUE SONAR: Similar issues query failed: {e} ===")
                similar_response = {}
            
            # Query for documentation and Stack Overflow with simpler timeout
            docs_query = f"Documentation and Stack Overflow solutions for: '{issue_title}'. {issue_body[:500]}..."
            
            try:
                docs_response = self.perplexity.query(
                    docs_query,
                    model="sonar-pro",
                    use_cache=True
                )
            except Exception as e:
                logger.warning(f"=== ISSUE SONAR: Documentation query failed: {e} ===")
                docs_response = {}
            
            # Extract insights
            augmentation.similar_issues = self._extract_similar_issues(similar_response)
            augmentation.potential_solutions = self._extract_potential_solutions(similar_response)
            augmentation.relevant_documentation = self._extract_documentation_links(docs_response)
            augmentation.stack_overflow_links = self._extract_stackoverflow_links(docs_response)
            
            # Generate suggested comment
            augmentation.suggested_comment = self._generate_helpful_comment(
                issue_title, issue_body, similar_response, docs_response
            )
            
            augmentation.analysis_status = "completed"
            db.commit()
            
            return augmentation
            
        except Exception as e:
            logger.error(f"Issue analysis failed: {e}")
            augmentation.analysis_status = "failed"
            db.commit()
            raise

    # Utility methods for parsing responses
    def _extract_version_from_response(self, response: Dict) -> Optional[str]:
        """Extract version information from Perplexity response."""
        # Implementation would parse the response content for version numbers
        return None

    def _check_if_outdated(self, current_version: str, response: Dict) -> bool:
        """Check if current version is outdated based on response."""
        # Implementation would compare versions
        return False

    def _count_vulnerabilities_in_response(self, response: Dict) -> int:
        """Count vulnerabilities mentioned in response."""
        # Implementation would count vulnerability mentions
        return 0

    def _extract_trend_direction(self, response: Dict) -> str:
        """Extract trend direction from response."""
        content = response.get('choices', [{}])[0].get('message', {}).get('content', '').lower()
        if 'declining' in content or 'decreasing' in content:
            return 'declining'
        elif 'rising' in content or 'growing' in content or 'increasing' in content:
            return 'rising'
        return 'stable'

    def _calculate_trend_score(self, response: Dict) -> float:
        """Calculate trend score from response."""
        # Implementation would analyze sentiment and keywords
        return 0.0

    def _get_technology_type(self, tech: str) -> str:
        """Determine technology type."""
        frameworks = ['React', 'Vue.js', 'Svelte']
        languages = ['JavaScript', 'Python', 'Java', 'Go', 'Rust']
        
        if tech in frameworks:
            return 'framework'
        elif tech in languages:
            return 'language'
        else:
            return 'library'

    async def _parse_dependencies(
        self, 
        content: str, 
        package_manager: str, 
        filename: str
    ) -> List[Dict[str, str]]:
        """Parse dependencies from file content."""
        dependencies = []
        
        try:
            if package_manager == 'npm' and filename == 'package.json':
                data = json.loads(content)
                deps = data.get('dependencies', {})
                dev_deps = data.get('devDependencies', {})
                
                for name, version in {**deps, **dev_deps}.items():
                    dependencies.append({
                        'name': name,
                        'version': version.replace('^', '').replace('~', '').replace('>=', '').replace('>', '').replace('<', '').replace('=', '')
                    })
                    
            elif package_manager == 'pip' and filename == 'requirements.txt':
                lines = content.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if '==' in line:
                            name, version = line.split('==', 1)
                            dependencies.append({
                                'name': name.strip(),
                                'version': version.strip()
                            })
                        else:
                            dependencies.append({
                                'name': line.strip(),
                                'version': 'unknown'
                            })
            
            # Add more parsers for other package managers as needed
            
        except Exception as e:
            logger.error(f"Failed to parse dependencies from {filename}: {e}")
            
        return dependencies

    # Additional utility methods for response parsing would be implemented here
    def _extract_alternatives_from_response(self, response: Dict) -> Optional[List]:
        """Extract alternative packages from response."""
        return None

    def _extract_discussions_from_response(self, response: Dict) -> Optional[List]:
        """Extract discussion links from response."""
        return None

    def _extract_articles_from_response(self, response: Dict) -> Optional[List]:
        """Extract article links from response."""
        return None

    def _extract_comparisons_from_response(self, response: Dict) -> Optional[List]:
        """Extract comparison articles from response."""
        return None

    def _extract_migration_guides(self, response: Dict) -> Optional[List]:
        """Extract migration guides from response."""
        return None

    def _extract_sentiment(self, response: Dict) -> Optional[Dict]:
        """Extract community sentiment from response."""
        return None

    def _extract_security_findings(self, response: Dict, analysis_id: int, db: Session) -> List:
        """Extract security findings and save to database."""
        return []

    def _extract_similar_issues(self, response: Dict) -> Optional[List]:
        """Extract similar issues from response."""
        return None

    def _extract_potential_solutions(self, response: Dict) -> Optional[List]:
        """Extract potential solutions from response."""
        return None

    def _extract_documentation_links(self, response: Dict) -> Optional[List]:
        """Extract documentation links from response."""
        return None

    def _extract_stackoverflow_links(self, response: Dict) -> Optional[List]:
        """Extract Stack Overflow links from response."""
        return None

    def _generate_helpful_comment(
        self, 
        issue_title: str, 
        issue_body: str, 
        similar_response: Dict, 
        docs_response: Dict
    ) -> str:
        """Generate a helpful comment for the issue."""
        return f"Based on analysis of similar issues and available documentation, here are some potential solutions for '{issue_title}'..."

    def _extract_popularity_rank(self, response: Dict) -> Optional[int]:
        """Extract popularity rank from response."""
        return None 