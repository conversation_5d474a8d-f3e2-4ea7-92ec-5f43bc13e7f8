{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.20.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/typography": "^0.5.16", "@types/prismjs": "^1.26.5", "@types/react-syntax-highlighter": "^15.5.13", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "highlight.js": "^11.11.1", "lucide-react": "^0.511.0", "mermaid": "^11.6.0", "moment": "^2.30.1", "next": "15.3.2", "next-themes": "^0.4.6", "prismjs": "^1.30.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}