'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import NavigationBar from '@/components/NavigationBar';
import { githubApi } from '@/lib/api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Search,
  GitBranch,
  ExternalLink,
  Loader2,
  Eye,
  Lock,
  Globe,
  AlertCircle,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

interface Repository {
  id: string;
  name: string;
  full_name: string;
  description?: string;
  is_private: boolean;
  github_url: string;
  [key: string]: any;
}

export default function Repositories() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const itemsPerPage = 9;

  useEffect(() => {
    // Redirect to sign-in if not authenticated
    console.log('Auth state in repositories page:', { user, isLoaded });
    if (isLoaded && !user) {
      console.log('Not authenticated, redirecting to sign-in');
      router.push('/sign-in');
    }
  }, [user, isLoaded, router]);

  useEffect(() => {
    // Fetch repositories
    const fetchRepositories = async () => {
      try {
        console.log('Fetching repositories for user:', user?.username);
        setLoading(true);
        setError('');

        // Check if auth token exists
        const token = localStorage.getItem('auth_token');
        console.log('Auth token exists:', !!token);

        const response = await githubApi.getRepositories();
        console.log('Repositories response:', response.data);
        setRepositories(response.data);
      } catch (err: any) {
        console.error('Error fetching repositories:', err);
        // Log more details about the error
        if (err.response) {
          console.error('Error response:', {
            status: err.response.status,
            data: err.response.data,
            headers: err.response.headers
          });
        }
        setError('Failed to load repositories');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchRepositories();
    } else {
      console.log('Skipping repository fetch - no user');
    }
  }, [user]);

  // Filter repositories based on search term
  const filteredRepositories = repositories.filter(repo =>
    repo.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    repo.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (repo.description && repo.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Paginate repositories
  const paginatedRepositories = filteredRepositories.slice(
    (page - 1) * itemsPerPage,
    page * itemsPerPage
  );

  const totalPages = Math.ceil(filteredRepositories.length / itemsPerPage);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  if (!isLoaded) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-lg text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to sign-in
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/50">
      <NavigationBar />

      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight mb-2">
            Your Repositories
          </h1>
          <p className="text-muted-foreground">
            Manage and explore your GitHub repositories
          </p>
        </div>

        {/* Search */}
        <div className="mb-8">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search repositories..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setPage(1); // Reset to first page on search
              }}
              className="pl-10"
            />
          </div>
        </div>

        {/* Content */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-6 w-6 animate-spin text-primary" />
              <p className="text-muted-foreground">Loading repositories...</p>
            </div>
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : paginatedRepositories.length > 0 ? (
          <>
            {/* Repository Grid */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
              {paginatedRepositories.map((repo) => (
                <Card key={repo.id} className="group hover:shadow-lg transition-shadow duration-200">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <CardTitle className="text-lg font-semibold group-hover:text-primary transition-colors">
                        {repo.name}
                      </CardTitle>
                      <Badge variant={repo.is_private ? "secondary" : "outline"} className="ml-2">
                        {repo.is_private ? (
                          <>
                            <Lock className="mr-1 h-3 w-3" />
                            Private
                          </>
                        ) : (
                          <>
                            <Globe className="mr-1 h-3 w-3" />
                            Public
                          </>
                        )}
                      </Badge>
                    </div>
                    <CardDescription className="text-sm">
                      {repo.full_name}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {repo.description && (
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {repo.description}
                      </p>
                    )}

                    <div className="flex flex-wrap gap-2">
                      <Button asChild variant="outline" size="sm">
                        <Link href={`/repositories/${repo.id}`}>
                          <Eye className="mr-1 h-3 w-3" />
                          View Details
                        </Link>
                      </Button>

                      <Button asChild variant="outline" size="sm">
                        <Link href={`/repositories/${repo.id}/analyzer`}>
                          <GitBranch className="mr-1 h-3 w-3" />
                          Analyze
                        </Link>
                      </Button>

                      <Button asChild variant="ghost" size="sm">
                        <a
                          href={repo.github_url}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <ExternalLink className="mr-1 h-3 w-3" />
                          GitHub
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(page - 1)}
                  disabled={page === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>

                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }).map((_, index) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = index + 1;
                    } else if (page <= 3) {
                      pageNum = index + 1;
                    } else if (page >= totalPages - 2) {
                      pageNum = totalPages - 4 + index;
                    } else {
                      pageNum = page - 2 + index;
                    }

                    return (
                      <Button
                        key={pageNum}
                        variant={page === pageNum ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(pageNum)}
                        className="w-10"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(page + 1)}
                  disabled={page === totalPages}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )}
          </>
        ) : (
          <Card className="text-center py-12">
            <CardContent>
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted">
                <GitBranch className="h-6 w-6 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">No repositories found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm
                  ? 'No repositories match your search criteria.'
                  : 'No repositories found. Connect your GitHub account to see your repositories.'}
              </p>
              {!searchTerm && (
                <Button asChild>
                  <a
                    href="https://github.com/new"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <ExternalLink className="mr-2 h-4 w-4" />
                    Create Repository on GitHub
                  </a>
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </main>
    </div>
  );
}
