'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import NavigationBar from '@/components/NavigationBar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Github,
  Search,
  MessageSquare,
  FolderGit2,
  Zap,
  Code2,
  Brain,
  Rocket,
  ArrowRight,
  Loader2
} from 'lucide-react';

interface Repository {
  id: string;
  name: string;
  full_name: string;
  description?: string;
  is_private: boolean;
  github_url: string;
  [key: string]: any;
}

export default function Home() {
  const { user, isLoaded } = useUser();
  const router = useRouter();

  console.log("Auth state:", { user, isLoaded });

  useEffect(() => {
    // Redirect to login if not authenticated
    if (isLoaded && !user) {
      console.log("Not authenticated, redirecting to sign-in");
      router.push('/sign-in');
    }
  }, [user, isLoaded, router]);

  if (!isLoaded) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-lg text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          <p className="text-muted-foreground">Not authenticated. Redirecting to sign-in...</p>
        </div>
      </div>
    );
  }

  const features = [
    {
      icon: FolderGit2,
      title: "Repository Management",
      description: "Browse and manage your GitHub repositories with enhanced insights",
      href: "/repositories",
      color: "text-blue-500"
    },
    {
      icon: Search,
      title: "Intelligent Code Search",
      description: "AI-powered semantic search across your codebase",
      href: "/search",
      color: "text-green-500"
    },
    {
      icon: MessageSquare,
      title: "AI Assistant",
      description: "Chat with AI about your code, get explanations and suggestions",
      href: "/chat",
      color: "text-purple-500"
    },
    {
      icon: Brain,
      title: "Code Analysis",
      description: "Deep insights and analytics for your projects",
      href: "/repositories",
      color: "text-orange-500"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/50">
      <NavigationBar />

      <main className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="mb-12 text-center">
          <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-primary/10">
            <Github className="h-10 w-10 text-primary" />
          </div>
          <h1 className="mb-4 text-4xl font-bold tracking-tight">
            Welcome back, {user.firstName || user.username || user.emailAddresses[0]?.emailAddress.split('@')[0]}!
          </h1>
          <p className="mx-auto max-w-2xl text-xl text-muted-foreground">
            Your AI-powered development workspace is ready. Explore your repositories, search code intelligently, and get insights from our AI assistant.
          </p>

          <div className="mt-6 flex items-center justify-center gap-2">
            <Badge variant="secondary" className="px-3 py-1">
              <Github className="mr-1 h-3 w-3" />
              {user.username || user.emailAddresses[0]?.emailAddress}
            </Badge>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mb-12">
          <h2 className="mb-6 text-2xl font-semibold">Quick Actions</h2>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <Card key={index} className="group cursor-pointer transition-all hover:shadow-lg hover:-translate-y-1">
                  <Link href={feature.href}>
                    <CardHeader className="pb-4">
                      <div className={`mb-2 flex h-12 w-12 items-center justify-center rounded-lg bg-muted ${feature.color}`}>
                        <IconComponent className="h-6 w-6" />
                      </div>
                      <CardTitle className="text-lg">{feature.title}</CardTitle>
                      <CardDescription className="text-sm">
                        {feature.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center text-sm text-primary group-hover:text-primary/80">
                        Get started
                        <ArrowRight className="ml-1 h-4 w-4 transition-transform group-hover:translate-x-1" />
                      </div>
                    </CardContent>
                  </Link>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Features Overview */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-yellow-500" />
              Platform Features
            </CardTitle>
            <CardDescription>
              Supercharge your development workflow with these powerful tools
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <h4 className="flex items-center gap-2 font-medium">
                  <Code2 className="h-4 w-4 text-blue-500" />
                  Enhanced Repository Browsing
                </h4>
                <p className="text-sm text-muted-foreground">
                  Navigate your GitHub repositories with advanced filtering, search, and organization features.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="flex items-center gap-2 font-medium">
                  <Brain className="h-4 w-4 text-purple-500" />
                  AI-Powered Insights
                </h4>
                <p className="text-sm text-muted-foreground">
                  Get intelligent code analysis, suggestions, and explanations powered by advanced AI models.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="flex items-center gap-2 font-medium">
                  <Search className="h-4 w-4 text-green-500" />
                  Semantic Code Search
                </h4>
                <p className="text-sm text-muted-foreground">
                  Find code not just by keywords, but by meaning and context across your entire codebase.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="flex items-center gap-2 font-medium">
                  <Rocket className="h-4 w-4 text-orange-500" />
                  Productivity Tools
                </h4>
                <p className="text-sm text-muted-foreground">
                  Streamline your workflow with integrated tools for code review, documentation, and collaboration.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Getting Started */}
        <Card>
          <CardHeader>
            <CardTitle>Getting Started</CardTitle>
            <CardDescription>
              New to the platform? Here's how to make the most of your experience
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-xs font-medium">
                  1
                </div>
                <div>
                  <h4 className="font-medium">Explore Your Repositories</h4>
                  <p className="text-sm text-muted-foreground">
                    Start by browsing your GitHub repositories and see what insights we can provide.
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-xs font-medium">
                  2
                </div>
                <div>
                  <h4 className="font-medium">Try Semantic Search</h4>
                  <p className="text-sm text-muted-foreground">
                    Use our AI-powered search to find code by describing what you're looking for.
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-xs font-medium">
                  3
                </div>
                <div>
                  <h4 className="font-medium">Chat with AI</h4>
                  <p className="text-sm text-muted-foreground">
                    Ask questions about your code, get explanations, and receive suggestions for improvements.
                  </p>
                </div>
              </div>
            </div>
            <Separator className="my-6" />
            <div className="flex gap-4">
              <Button asChild>
                <Link href="/repositories">
                  <FolderGit2 className="mr-2 h-4 w-4" />
                  View Repositories
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="/chat">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Start AI Chat
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
