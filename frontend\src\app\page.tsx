'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import NavigationBar from '@/components/NavigationBar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowRight, Loader2 } from 'lucide-react';

export default function Home() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [repositoryUrl, setRepositoryUrl] = useState('');
  const [repositoryType, setRepositoryType] = useState('public');
  const [githubToken, setGithubToken] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  console.log("Auth state:", { user, isLoaded });

  useEffect(() => {
    // Redirect to login if not authenticated
    if (isLoaded && !user) {
      console.log("Not authenticated, redirecting to sign-in");
      router.push('/sign-in');
    }
  }, [user, isLoaded, router]);

  const handleGenerateTutorial = async () => {
    if (!repositoryUrl.trim()) {
      alert('Please enter a repository URL');
      return;
    }

    if (repositoryType === 'private' && !githubToken.trim()) {
      alert('Please enter a GitHub token for private repositories');
      return;
    }

    // Validate GitHub URL format
    if (!repositoryUrl.match(/^https:\/\/github\.com\/[\w\-\.]+\/[\w\-\.]+/)) {
      alert('Please enter a valid GitHub repository URL (e.g., https://github.com/owner/repo)');
      return;
    }

    setIsGenerating(true);

    try {
      // Get the session token from Clerk
      const token = await user?.getToken?.();

      if (!token) {
        alert('Authentication error. Please try signing in again.');
        return;
      }

      // Call the backend API
      const response = await fetch('http://localhost:8000/api/tutor/generate-tutorial-from-url/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          repository_url: repositoryUrl,
          github_token: repositoryType === 'private' ? githubToken : null,
          include_patterns: ['*.py', '*.js', '*.ts', '*.jsx', '*.tsx', '*.java', '*.cpp', '*.c', '*.h'],
          exclude_patterns: ['node_modules/*', '*.min.js', '*.bundle.js', 'dist/*', 'build/*'],
          language: 'english',
          max_abstractions: 10,
          use_cache: true
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to generate tutorial');
      }

      const result = await response.json();

      // Redirect to tutorial status page or show success message
      alert(`Tutorial generation started! Tutorial ID: ${result.github_action_id}. You can check the status in your tutorials.`);

      // Optionally redirect to a status page
      // router.push(`/tutorials/${result.github_action_id}`);

    } catch (error) {
      console.error('Error generating tutorial:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      alert(`Failed to generate tutorial: ${errorMessage}`);
    } finally {
      setIsGenerating(false);
    }
  };

  if (!isLoaded) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-lg text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          <p className="text-muted-foreground">Not authenticated. Redirecting to sign-in...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <NavigationBar />

      <main className="container mx-auto px-4 py-16">
        {/* Main Content Area */}
        <div className="mx-auto max-w-4xl">
          <div className="relative rounded-3xl border-2 border-muted p-8 md:p-12">
            {/* Main Input Section */}
            <div className="mb-8 space-y-6">
              <div className="flex items-center gap-2 rounded-xl border-2 border-muted bg-muted/30 p-2">
                {/* Repository Type Selector */}
                <div className="flex-shrink-0">
                  <Select value={repositoryType} onValueChange={setRepositoryType}>
                    <SelectTrigger className="h-10 w-24 border-0 bg-transparent text-sm font-medium">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="public">PUBLIC</SelectItem>
                      <SelectItem value="private">PRIVATE</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* URL Input */}
                <Input
                  type="text"
                  placeholder="Enter URL Here"
                  value={repositoryUrl}
                  onChange={(e) => setRepositoryUrl(e.target.value)}
                  className="h-10 flex-1 border-0 bg-transparent text-lg placeholder:text-muted-foreground/60 focus-visible:ring-0 focus-visible:ring-offset-0"
                />

                {/* Submit Button */}
                <Button
                  onClick={handleGenerateTutorial}
                  disabled={isGenerating}
                  className="h-10 w-10 flex-shrink-0 rounded-lg p-0"
                >
                  {isGenerating ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <ArrowRight className="h-4 w-4" />
                  )}
                </Button>
              </div>

              {/* GitHub Token Input for Private Repos */}
              {repositoryType === 'private' && (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground">
                    GitHub Personal Access Token (required for private repositories)
                  </label>
                  <Input
                    type="password"
                    placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
                    value={githubToken}
                    onChange={(e) => setGithubToken(e.target.value)}
                    className="h-12 rounded-xl border-2 border-muted bg-muted/30"
                  />
                </div>
              )}
            </div>

            {/* Explanatory Text */}
            <div className="absolute right-8 top-1/2 hidden -translate-y-1/2 lg:block">
              <div className="max-w-xs space-y-2">
                <p className="text-sm text-muted-foreground leading-relaxed">
                  This Page will just fetch the repository and generate an AI-powered tutorial to help you understand the codebase structure and functionality.
                </p>
              </div>
            </div>
          </div>

          {/* Instructions for Mobile */}
          <div className="mt-8 lg:hidden">
            <div className="rounded-xl border bg-muted/30 p-6">
              <h3 className="mb-3 font-semibold">How it works</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">
                This page will fetch the repository and generate an AI-powered tutorial to help you understand the codebase structure and functionality.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
