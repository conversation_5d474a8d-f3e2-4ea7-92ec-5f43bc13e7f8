from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from sqlalchemy.orm import Session

from ..database import get_db
from ..dependencies import get_current_user, get_current_user_profile
from ..auth.router import oauth2_scheme
from ..models.core import User, UserProfile, Organization, Repository, UserRepositoryAccess, Setting
from . import schemas

router = APIRouter()

@router.get("/users/", response_model=List[schemas.User])
async def get_users(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all users.
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    users = db.query(User).offset(skip).limit(limit).all()
    return users

@router.get("/users/me/", response_model=schemas.User)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """
    Get current user information.
    """
    return current_user

@router.get("/profiles/me/", response_model=schemas.UserProfile)
async def get_current_user_profile_info(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get current user profile information.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    return current_user_profile

@router.get("/organizations/", response_model=List[schemas.Organization])
async def get_organizations(
    skip: int = 0,
    limit: int = 100,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get all organizations for the current user.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    organizations = current_user_profile.organizations
    return organizations

@router.get("/repositories/", response_model=List[schemas.Repository])
async def get_repositories(
    skip: int = 0,
    limit: int = 100,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    try:
        current_user = await get_current_user(token, db)
        current_user_profile = await get_current_user_profile(current_user, db)
        
        all_repo_ids = set()

        user_owned_repos_query = db.query(Repository.id).filter(Repository.owner_id == current_user_profile.id)
        user_owned_repo_ids = {repo_id for (repo_id,) in user_owned_repos_query.all()}
        all_repo_ids.update(user_owned_repo_ids)

        org_ids = []
        if current_user_profile.organizations:
            org_ids = [org.id for org in current_user_profile.organizations]
        if org_ids:
            org_repos_query = db.query(Repository.id).filter(Repository.organization_id.in_(org_ids))
            org_repo_ids = {repo_id for (repo_id,) in org_repos_query.all()}
            all_repo_ids.update(org_repo_ids)

        access_repository_ids_query = db.query(UserRepositoryAccess.repository_id).filter(
            UserRepositoryAccess.user_id == current_user_profile.id
        )
        explicit_access_repo_ids = {repo_id for (repo_id,) in access_repository_ids_query.all()}
        all_repo_ids.update(explicit_access_repo_ids)
        
        if not all_repo_ids:
            return []

        final_repositories_query = db.query(Repository).filter(Repository.id.in_(list(all_repo_ids)))
        repositories_result = final_repositories_query.offset(skip).limit(limit).all()
        
        return repositories_result

    except Exception as e_core:
        raise

@router.get("/repositories/{repository_id}", response_model=schemas.Repository)
async def get_repository(
    repository_id: int,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get a specific repository by ID.
    """
    # Get all repositories the user has access to
    try:
        current_user = await get_current_user(token, db)
        current_user_profile = await get_current_user_profile(current_user, db)
        
        all_repo_ids = set()

        # User owned repositories
        user_owned_repos_query = db.query(Repository.id).filter(Repository.owner_id == current_user_profile.id)
        user_owned_repo_ids = {repo_id for (repo_id,) in user_owned_repos_query.all()}
        all_repo_ids.update(user_owned_repo_ids)

        # Organization repositories
        org_ids = []
        if current_user_profile.organizations:
            org_ids = [org.id for org in current_user_profile.organizations]
        if org_ids:
            org_repos_query = db.query(Repository.id).filter(Repository.organization_id.in_(org_ids))
            org_repo_ids = {repo_id for (repo_id,) in org_repos_query.all()}
            all_repo_ids.update(org_repo_ids)

        # Explicitly granted access
        access_repository_ids_query = db.query(UserRepositoryAccess.repository_id).filter(
            UserRepositoryAccess.user_id == current_user_profile.id
        )
        explicit_access_repo_ids = {repo_id for (repo_id,) in access_repository_ids_query.all()}
        all_repo_ids.update(explicit_access_repo_ids)
        
        # Check if user has access to this specific repository
        if repository_id not in all_repo_ids:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Repository not found or access denied"
            )
        
        # Get the repository
        repository = db.query(Repository).filter(Repository.id == repository_id).first()
        if not repository:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Repository not found"
            )
        
        return repository
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch repository"
        )

@router.get("/settings/", response_model=List[schemas.Setting])
async def get_settings(
    scope: Optional[str] = None,
    key: Optional[str] = None,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get settings for the current user.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    query = db.query(Setting)
    
    if scope:
        query = query.filter(Setting.scope == scope)
    
    if key:
        query = query.filter(Setting.key == key)
    
    user_settings = query.filter(Setting.user_id == current_user_profile.id)
    
    global_settings = query.filter(Setting.scope == "global")
    
    org_ids = [org.id for org in current_user_profile.organizations]
    org_settings = query.filter(Setting.organization_id.in_(org_ids) if org_ids else False)
    
    try:
        user_accessible_repos = await get_repositories(0, 10000, token, db)
        repo_ids = [repo.id for repo in user_accessible_repos]
    except Exception:
        repo_ids = []

    repo_settings = query.filter(Setting.repository_id.in_(repo_ids) if repo_ids else False)
    
    combined_settings_results = []
    combined_settings_results.extend(user_settings.all())
    combined_settings_results.extend(global_settings.all())
    combined_settings_results.extend(org_settings.all())
    combined_settings_results.extend(repo_settings.all())

    return combined_settings_results
