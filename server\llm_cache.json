{"\nFor the project `Simple-AI-Image-Enhancer`:\n\nCodebase Context:\n--- File Index 0: .gitignore ---\n# Logs\nlogs\n*.log\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*\npnpm-debug.log*\nlerna-debug.log*\n\nnode_modules\ndist\ndist-ssr\n*.local\n\n# Editor directories and files\n.vscode/*\n!.vscode/extensions.json\n.idea\n.DS_Store\n*.suo\n*.ntvs*\n*.njsproj\n*.sln\n*.sw?\n\n# Backend specific ignores\nbackend/node_modules\nbackend/.env\n\n\n--- File Index 1: README.md ---\n# AI Image Enhancer\n\nA simple web application that allows users to upload an image and enhance its quality using an external AI-powered image enhancement service.\n\n## Features\n\n*   **Image Upload:** Drag-and-drop or click to select an image file (JPG, PNG, WEBP).\n*   **AI Enhancement:** Sends the uploaded image to an external API for enhancement.\n*   **Polling Mechanism:** Checks the status of the enhancement task periodically.\n*   **Image Preview:** Displays the original and enhanced images side-by-side.\n*   **Download:** Allows downloading the enhanced image.\n*   **Comparison View:** Opens a new tab to compare the original and enhanced images directly.\n\n## How It Works\n\n1.  **Frontend (React + Vite):**\n    *   The user uploads an image via the web interface.\n    *   The frontend sends the image file to the backend server.\n    *   After receiving a task ID from the backend, the frontend polls the backend's status endpoint until the enhancement is complete.\n    *   Displays the original and enhanced images.\n2.  **Backend (Node.js + Express):**\n    *   Acts as a proxy server.\n    *   Receives the image file from the frontend.\n    *   Forwards the image file to the external AI enhancement API using an API key stored securely in environment variables.\n    *   Returns the task ID received from the external API to the frontend.\n    *   Provides an endpoint for the frontend to check the status of the enhancement task by querying the external API.\n3.  **External AI API (e.g., Clipdrop, TechHK):**\n    *   Receives the image and API key from the backend.\n    *   Processes the image enhancement task asynchronously.\n    *   Provides endpoints to submit tasks and check their status/results.\n\n## Setup and Installation\n\n### Prerequisites\n\n*   [Node.js](https://nodejs.org/) (LTS version recommended)\n*   [npm](https://www.npmjs.com/) or [yarn](https://yarnpkg.com/)\n\n### Backend Setup\n\n1.  **Navigate to the backend directory:**\n    ```bash\n    cd backend\n    ```\n2.  **Install dependencies:**\n    ```bash\n    npm install\n    # or\n    yarn install\n    ```\n3.  **Create environment file:**\n    *   Create a file named `.env` in the `backend` directory.\n    *   Add your external API key to this file:\n        ```env\n        EXTERNAL_API_KEY=YOUR_ACTUAL_API_KEY_HERE\n        PORT=3001 # Optional: Define a port for the backend server\n        ```\n    *   **Important:** Replace `YOUR_ACTUAL_API_KEY_HERE` with your real API key. Add `.env` to your `.gitignore` file if it's not already there to avoid committing your key.\n4.  **Start the backend server:**\n    ```bash\n    npm start\n    # or\n    yarn start\n    ```\n    The backend server should now be running (typically at `http://localhost:3001`).\n\n### Frontend Setup\n\n1.  **Navigate to the project root directory (if you were in `backend`):**\n    ```bash\n    cd ..\n    ```\n2.  **Install dependencies:**\n    ```bash\n    npm install\n    # or\n    yarn install\n    ```\n3.  **Start the frontend development server:**\n    ```bash\n    npm run dev\n    # or\n    yarn dev\n    ```\n    The frontend application should now be running (typically at `http://localhost:5174`).\n\n## Usage\n\n1.  Ensure both the backend and frontend servers are running.\n2.  Open your web browser and navigate to the frontend URL (e.g., `http://localhost:5174`).\n3.  Click the upload area or drag an image file onto it.\n4.  Wait for the enhancement process to complete. The enhanced image will appear alongside the original.\n5.  Use the \"Download Enhanced\" or \"Compare Images\" buttons as needed.\n\n## Technology Stack\n\n*   **Frontend:** React, Vite, Tailwind CSS, Axios\n*   **Backend:** Node.js, Express, Axios, Multer, dotenv\n*   **External Service:** AI Image Enhancement API (configured via `EXTERNAL_API_KEY`)\n\n---\n\n--- File Index 2: backend/.env.example ---\n# API Key for the external image enhancement service\nEXTERNAL_API_KEY=YOUR_API_KEY_HERE\n\n# Optional: Port for the backend server (defaults to 3001 if not set)\nPORT=3001\n\n--- File Index 3: backend/package-lock.json ---\n{\n  \"name\": \"backend\",\n  \"version\": \"1.0.0\",\n  \"lockfileVersion\": 3,\n  \"requires\": true,\n  \"packages\": {\n    \"\": {\n      \"name\": \"backend\",\n      \"version\": \"1.0.0\",\n      \"license\": \"ISC\",\n      \"dependencies\": {\n        \"axios\": \"^1.8.4\",\n        \"cors\": \"^2.8.5\",\n        \"dotenv\": \"^16.4.7\",\n        \"express\": \"^5.1.0\",\n        \"multer\": \"^1.4.5-lts.2\"\n      }\n    },\n    \"node_modules/accepts\": {\n      \"version\": \"2.0.0\",\n      \"resolved\": \"https://registry.npmjs.org/accepts/-/accepts-2.0.0.tgz\",\n      \"integrity\": \"sha512-5cvg6CtKwfgdmVqY1WIiXKc3Q1bkRqGLi+2W/6ao+6Y7gu/RCwRuAhGEzh5B4KlszSuTLgZYuqFqo5bImjNKng==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"mime-types\": \"^3.0.0\",\n        \"negotiator\": \"^1.0.0\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/append-field\": {\n      \"version\": \"1.0.0\",\n      \"resolved\": \"https://registry.npmjs.org/append-field/-/append-field-1.0.0.tgz\",\n      \"integrity\": \"sha512-klpgFSWLW1ZEs8svjfb7g4qWY0YS5imI82dTg+QahUvJ8YqAY0P10Uk8tTyh9ZGuYEZEMaeJYCF5BFuX552hsw==\",\n      \"license\": \"MIT\"\n    },\n    \"node_modules/asynckit\": {\n      \"version\": \"0.4.0\",\n      \"resolved\": \"https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz\",\n      \"integrity\": \"sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==\",\n      \"license\": \"MIT\"\n    },\n    \"node_modules/axios\": {\n      \"version\": \"1.8.4\",\n      \"resolved\": \"https://registry.npmjs.org/axios/-/axios-1.8.4.tgz\",\n      \"integrity\": \"sha512-eBSYY4Y68NNlHbHBMdeDmKNtDgXWhQsJcGqzO3iLUM0GraQFSS9cVgPX5I9b3lbdFKyYoAEGAZF1DwhTaljNAw==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"follow-redirects\": \"^1.15.6\",\n        \"form-data\": \"^4.0.0\",\n        \"proxy-from-env\": \"^1.1.0\"\n      }\n    },\n    \"node_modules/body-parser\": {\n      \"version\": \"2.2.0\",\n      \"resolved\": \"https://registry.npmjs.org/body-parser/-/body-parser-2.2.0.tgz\",\n      \"integrity\": \"sha512-02qvAaxv8tp7fBa/mw1ga98OGm+eCbqzJOKoRt70sLmfEEi+jyBYVTDGfCL/k06/4EMk/z01gCe7HoCH/f2LTg==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"bytes\": \"^3.1.2\",\n        \"content-type\": \"^1.0.5\",\n        \"debug\": \"^4.4.0\",\n        \"http-errors\": \"^2.0.0\",\n        \"iconv-lite\": \"^0.6.3\",\n        \"on-finished\": \"^2.4.1\",\n        \"qs\": \"^6.14.0\",\n        \"raw-body\": \"^3.0.0\",\n        \"type-is\": \"^2.0.0\"\n      },\n      \"engines\": {\n        \"node\": \">=18\"\n      }\n    },\n    \"node_modules/buffer-from\": {\n      \"version\": \"1.1.2\",\n      \"resolved\": \"https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz\",\n      \"integrity\": \"sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==\",\n      \"license\": \"MIT\"\n    },\n    \"node_modules/busboy\": {\n      \"version\": \"1.6.0\",\n      \"resolved\": \"https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz\",\n      \"integrity\": \"sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==\",\n      \"dependencies\": {\n        \"streamsearch\": \"^1.1.0\"\n      },\n      \"engines\": {\n        \"node\": \">=10.16.0\"\n      }\n    },\n    \"node_modules/bytes\": {\n      \"version\": \"3.1.2\",\n      \"resolved\": \"https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz\",\n      \"integrity\": \"sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.8\"\n      }\n    },\n    \"node_modules/call-bind-apply-helpers\": {\n      \"version\": \"1.0.2\",\n      \"resolved\": \"https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz\",\n      \"integrity\": \"sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"es-errors\": \"^1.3.0\",\n        \"function-bind\": \"^1.1.2\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      }\n    },\n    \"node_modules/call-bound\": {\n      \"version\": \"1.0.4\",\n      \"resolved\": \"https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz\",\n      \"integrity\": \"sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"call-bind-apply-helpers\": \"^1.0.2\",\n        \"get-intrinsic\": \"^1.3.0\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      },\n      \"funding\": {\n        \"url\": \"https://github.com/sponsors/ljharb\"\n      }\n    },\n    \"node_modules/combined-stream\": {\n      \"version\": \"1.0.8\",\n      \"resolved\": \"https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz\",\n      \"integrity\": \"sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"delayed-stream\": \"~1.0.0\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.8\"\n      }\n    },\n    \"node_modules/concat-stream\": {\n      \"version\": \"1.6.2\",\n      \"resolved\": \"https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz\",\n      \"integrity\": \"sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==\",\n      \"engines\": [\n        \"node >= 0.8\"\n      ],\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"buffer-from\": \"^1.0.0\",\n        \"inherits\": \"^2.0.3\",\n        \"readable-stream\": \"^2.2.2\",\n        \"typedarray\": \"^0.0.6\"\n      }\n    },\n    \"node_modules/content-disposition\": {\n      \"version\": \"1.0.0\",\n      \"resolved\": \"https://registry.npmjs.org/content-disposition/-/content-disposition-1.0.0.tgz\",\n      \"integrity\": \"sha512-Au9nRL8VNUut/XSzbQA38+M78dzP4D+eqg3gfJHMIHHYa3bg067xj1KxMUWj+VULbiZMowKngFFbKczUrNJ1mg==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"safe-buffer\": \"5.2.1\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/content-type\": {\n      \"version\": \"1.0.5\",\n      \"resolved\": \"https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz\",\n      \"integrity\": \"sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/cookie\": {\n      \"version\": \"0.7.2\",\n      \"resolved\": \"https://registry.npmjs.org/cookie/-/cookie-0.7.2.tgz\",\n      \"integrity\": \"sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/cookie-signature\": {\n      \"version\": \"1.2.2\",\n      \"resolved\": \"https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.2.2.tgz\",\n      \"integrity\": \"sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">=6.6.0\"\n      }\n    },\n    \"node_modules/core-util-is\": {\n      \"version\": \"1.0.3\",\n      \"resolved\": \"https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz\",\n      \"integrity\": \"sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==\",\n      \"license\": \"MIT\"\n    },\n    \"node_modules/cors\": {\n      \"version\": \"2.8.5\",\n      \"resolved\": \"https://registry.npmjs.org/cors/-/cors-2.8.5.tgz\",\n      \"integrity\": \"sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"object-assign\": \"^4\",\n        \"vary\": \"^1\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.10\"\n      }\n    },\n    \"node_modules/debug\": {\n      \"version\": \"4.4.0\",\n      \"resolved\": \"https://registry.npmjs.org/debug/-/debug-4.4.0.tgz\",\n      \"integrity\": \"sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"ms\": \"^2.1.3\"\n      },\n      \"engines\": {\n        \"node\": \">=6.0\"\n      },\n      \"peerDependenciesMeta\": {\n        \"supports-color\": {\n          \"optional\": true\n        }\n      }\n    },\n    \"node_modules/delayed-stream\": {\n      \"version\": \"1.0.0\",\n      \"resolved\": \"https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz\",\n      \"integrity\": \"sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">=0.4.0\"\n      }\n    },\n    \"node_modules/depd\": {\n      \"version\": \"2.0.0\",\n      \"resolved\": \"https://registry.npmjs.org/depd/-/depd-2.0.0.tgz\",\n      \"integrity\": \"sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.8\"\n      }\n    },\n    \"node_modules/dotenv\": {\n      \"version\": \"16.4.7\",\n      \"resolved\": \"https://registry.npmjs.org/dotenv/-/dotenv-16.4.7.tgz\",\n      \"integrity\": \"sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ==\",\n      \"license\": \"BSD-2-Clause\",\n      \"engines\": {\n        \"node\": \">=12\"\n      },\n      \"funding\": {\n        \"url\": \"https://dotenvx.com\"\n      }\n    },\n    \"node_modules/dunder-proto\": {\n      \"version\": \"1.0.1\",\n      \"resolved\": \"https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz\",\n      \"integrity\": \"sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"call-bind-apply-helpers\": \"^1.0.1\",\n        \"es-errors\": \"^1.3.0\",\n        \"gopd\": \"^1.2.0\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      }\n    },\n    \"node_modules/ee-first\": {\n      \"version\": \"1.1.1\",\n      \"resolved\": \"https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz\",\n      \"integrity\": \"sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==\",\n      \"license\": \"MIT\"\n    },\n    \"node_modules/encodeurl\": {\n      \"version\": \"2.0.0\",\n      \"resolved\": \"https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz\",\n      \"integrity\": \"sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.8\"\n      }\n    },\n    \"node_modules/es-define-property\": {\n      \"version\": \"1.0.1\",\n      \"resolved\": \"https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz\",\n      \"integrity\": \"sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      }\n    },\n    \"node_modules/es-errors\": {\n      \"version\": \"1.3.0\",\n      \"resolved\": \"https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz\",\n      \"integrity\": \"sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      }\n    },\n    \"node_modules/es-object-atoms\": {\n      \"version\": \"1.1.1\",\n      \"resolved\": \"https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz\",\n      \"integrity\": \"sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"es-errors\": \"^1.3.0\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      }\n    },\n    \"node_modules/es-set-tostringtag\": {\n      \"version\": \"2.1.0\",\n      \"resolved\": \"https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz\",\n      \"integrity\": \"sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"es-errors\": \"^1.3.0\",\n        \"get-intrinsic\": \"^1.2.6\",\n        \"has-tostringtag\": \"^1.0.2\",\n        \"hasown\": \"^2.0.2\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      }\n    },\n    \"node_modules/escape-html\": {\n      \"version\": \"1.0.3\",\n      \"resolved\": \"https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz\",\n      \"integrity\": \"sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==\",\n      \"license\": \"MIT\"\n    },\n    \"node_modules/etag\": {\n      \"version\": \"1.8.1\",\n      \"resolved\": \"https://registry.npmjs.org/etag/-/etag-1.8.1.tgz\",\n      \"integrity\": \"sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/express\": {\n      \"version\": \"5.1.0\",\n      \"resolved\": \"https://registry.npmjs.org/express/-/express-5.1.0.tgz\",\n      \"integrity\": \"sha512-DT9ck5YIRU+8GYzzU5kT3eHGA5iL+1Zd0EutOmTE9Dtk+Tvuzd23VBU+ec7HPNSTxXYO55gPV/hq4pSBJDjFpA==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"accepts\": \"^2.0.0\",\n        \"body-parser\": \"^2.2.0\",\n        \"content-disposition\": \"^1.0.0\",\n        \"content-type\": \"^1.0.5\",\n        \"cookie\": \"^0.7.1\",\n        \"cookie-signature\": \"^1.2.1\",\n        \"debug\": \"^4.4.0\",\n        \"encodeurl\": \"^2.0.0\",\n        \"escape-html\": \"^1.0.3\",\n        \"etag\": \"^1.8.1\",\n        \"finalhandler\": \"^2.1.0\",\n        \"fresh\": \"^2.0.0\",\n        \"http-errors\": \"^2.0.0\",\n        \"merge-descriptors\": \"^2.0.0\",\n        \"mime-types\": \"^3.0.0\",\n        \"on-finished\": \"^2.4.1\",\n        \"once\": \"^1.4.0\",\n        \"parseurl\": \"^1.3.3\",\n        \"proxy-addr\": \"^2.0.7\",\n        \"qs\": \"^6.14.0\",\n        \"range-parser\": \"^1.2.1\",\n        \"router\": \"^2.2.0\",\n        \"send\": \"^1.1.0\",\n        \"serve-static\": \"^2.2.0\",\n        \"statuses\": \"^2.0.1\",\n        \"type-is\": \"^2.0.1\",\n        \"vary\": \"^1.1.2\"\n      },\n      \"engines\": {\n        \"node\": \">= 18\"\n      },\n      \"funding\": {\n        \"type\": \"opencollective\",\n        \"url\": \"https://opencollective.com/express\"\n      }\n    },\n    \"node_modules/finalhandler\": {\n      \"version\": \"2.1.0\",\n      \"resolved\": \"https://registry.npmjs.org/finalhandler/-/finalhandler-2.1.0.tgz\",\n      \"integrity\": \"sha512-/t88Ty3d5JWQbWYgaOGCCYfXRwV1+be02WqYYlL6h0lEiUAMPM8o8qKGO01YIkOHzka2up08wvgYD0mDiI+q3Q==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"debug\": \"^4.4.0\",\n        \"encodeurl\": \"^2.0.0\",\n        \"escape-html\": \"^1.0.3\",\n        \"on-finished\": \"^2.4.1\",\n        \"parseurl\": \"^1.3.3\",\n        \"statuses\": \"^2.0.1\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.8\"\n      }\n    },\n    \"node_modules/follow-redirects\": {\n      \"version\": \"1.15.9\",\n      \"resolved\": \"https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz\",\n      \"integrity\": \"sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==\",\n      \"funding\": [\n        {\n          \"type\": \"individual\",\n          \"url\": \"https://github.com/sponsors/RubenVerborgh\"\n        }\n      ],\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">=4.0\"\n      },\n      \"peerDependenciesMeta\": {\n        \"debug\": {\n          \"optional\": true\n        }\n      }\n    },\n    \"node_modules/form-data\": {\n      \"version\": \"4.0.2\",\n      \"resolved\": \"https://registry.npmjs.org/form-data/-/form-data-4.0.2.tgz\",\n      \"integrity\": \"sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"asynckit\": \"^0.4.0\",\n        \"combined-stream\": \"^1.0.8\",\n        \"es-set-tostringtag\": \"^2.1.0\",\n        \"mime-types\": \"^2.1.12\"\n      },\n      \"engines\": {\n        \"node\": \">= 6\"\n      }\n    },\n    \"node_modules/form-data/node_modules/mime-db\": {\n      \"version\": \"1.52.0\",\n      \"resolved\": \"https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz\",\n      \"integrity\": \"sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/form-data/node_modules/mime-types\": {\n      \"version\": \"2.1.35\",\n      \"resolved\": \"https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz\",\n      \"integrity\": \"sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"mime-db\": \"1.52.0\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/forwarded\": {\n      \"version\": \"0.2.0\",\n      \"resolved\": \"https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz\",\n      \"integrity\": \"sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/fresh\": {\n      \"version\": \"2.0.0\",\n      \"resolved\": \"https://registry.npmjs.org/fresh/-/fresh-2.0.0.tgz\",\n      \"integrity\": \"sha512-Rx/WycZ60HOaqLKAi6cHRKKI7zxWbJ31MhntmtwMoaTeF7XFH9hhBp8vITaMidfljRQ6eYWCKkaTK+ykVJHP2A==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.8\"\n      }\n    },\n    \"node_modules/function-bind\": {\n      \"version\": \"1.1.2\",\n      \"resolved\": \"https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz\",\n      \"integrity\": \"sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==\",\n      \"license\": \"MIT\",\n      \"funding\": {\n        \"url\": \"https://github.com/sponsors/ljharb\"\n      }\n    },\n    \"node_modules/get-intrinsic\": {\n      \"version\": \"1.3.0\",\n      \"resolved\": \"https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz\",\n      \"integrity\": \"sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"call-bind-apply-helpers\": \"^1.0.2\",\n        \"es-define-property\": \"^1.0.1\",\n        \"es-errors\": \"^1.3.0\",\n        \"es-object-atoms\": \"^1.1.1\",\n        \"function-bind\": \"^1.1.2\",\n        \"get-proto\": \"^1.0.1\",\n        \"gopd\": \"^1.2.0\",\n        \"has-symbols\": \"^1.1.0\",\n        \"hasown\": \"^2.0.2\",\n        \"math-intrinsics\": \"^1.1.0\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      },\n      \"funding\": {\n        \"url\": \"https://github.com/sponsors/ljharb\"\n      }\n    },\n    \"node_modules/get-proto\": {\n      \"version\": \"1.0.1\",\n      \"resolved\": \"https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz\",\n      \"integrity\": \"sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"dunder-proto\": \"^1.0.1\",\n        \"es-object-atoms\": \"^1.0.0\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      }\n    },\n    \"node_modules/gopd\": {\n      \"version\": \"1.2.0\",\n      \"resolved\": \"https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz\",\n      \"integrity\": \"sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      },\n      \"funding\": {\n        \"url\": \"https://github.com/sponsors/ljharb\"\n      }\n    },\n    \"node_modules/has-symbols\": {\n      \"version\": \"1.1.0\",\n      \"resolved\": \"https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz\",\n      \"integrity\": \"sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      },\n      \"funding\": {\n        \"url\": \"https://github.com/sponsors/ljharb\"\n      }\n    },\n    \"node_modules/has-tostringtag\": {\n      \"version\": \"1.0.2\",\n      \"resolved\": \"https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz\",\n      \"integrity\": \"sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"has-symbols\": \"^1.0.3\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      },\n      \"funding\": {\n        \"url\": \"https://github.com/sponsors/ljharb\"\n      }\n    },\n    \"node_modules/hasown\": {\n      \"version\": \"2.0.2\",\n      \"resolved\": \"https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz\",\n      \"integrity\": \"sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"function-bind\": \"^1.1.2\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      }\n    },\n    \"node_modules/http-errors\": {\n      \"version\": \"2.0.0\",\n      \"resolved\": \"https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz\",\n      \"integrity\": \"sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"depd\": \"2.0.0\",\n        \"inherits\": \"2.0.4\",\n        \"setprototypeof\": \"1.2.0\",\n        \"statuses\": \"2.0.1\",\n        \"toidentifier\": \"1.0.1\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.8\"\n      }\n    },\n    \"node_modules/iconv-lite\": {\n      \"version\": \"0.6.3\",\n      \"resolved\": \"https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz\",\n      \"integrity\": \"sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"safer-buffer\": \">= 2.1.2 < 3.0.0\"\n      },\n      \"engines\": {\n        \"node\": \">=0.10.0\"\n      }\n    },\n    \"node_modules/inherits\": {\n      \"version\": \"2.0.4\",\n      \"resolved\": \"https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz\",\n      \"integrity\": \"sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==\",\n      \"license\": \"ISC\"\n    },\n    \"node_modules/ipaddr.js\": {\n      \"version\": \"1.9.1\",\n      \"resolved\": \"https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz\",\n      \"integrity\": \"sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.10\"\n      }\n    },\n    \"node_modules/is-promise\": {\n      \"version\": \"4.0.0\",\n      \"resolved\": \"https://registry.npmjs.org/is-promise/-/is-promise-4.0.0.tgz\",\n      \"integrity\": \"sha512-hvpoI6korhJMnej285dSg6nu1+e6uxs7zG3BYAm5byqDsgJNWwxzM6z6iZiAgQR4TJ30JmBTOwqZUw3WlyH3AQ==\",\n      \"license\": \"MIT\"\n    },\n    \"node_modules/isarray\": {\n      \"version\": \"1.0.0\",\n      \"resolved\": \"https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz\",\n      \"integrity\": \"sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==\",\n      \"license\": \"MIT\"\n    },\n    \"node_modules/math-intrinsics\": {\n      \"version\": \"1.1.0\",\n      \"resolved\": \"https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz\",\n      \"integrity\": \"sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      }\n    },\n    \"node_modules/media-typer\": {\n      \"version\": \"1.1.0\",\n      \"resolved\": \"https://registry.npmjs.org/media-typer/-/media-typer-1.1.0.tgz\",\n      \"integrity\": \"sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.8\"\n      }\n    },\n    \"node_modules/merge-descriptors\": {\n      \"version\": \"2.0.0\",\n      \"resolved\": \"https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-2.0.0.tgz\",\n      \"integrity\": \"sha512-Snk314V5ayFLhp3fkUREub6WtjBfPdCPY1Ln8/8munuLuiYhsABgBVWsozAG+MWMbVEvcdcpbi9R7ww22l9Q3g==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">=18\"\n      },\n      \"funding\": {\n        \"url\": \"https://github.com/sponsors/sindresorhus\"\n      }\n    },\n    \"node_modules/mime-db\": {\n      \"version\": \"1.54.0\",\n      \"resolved\": \"https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz\",\n      \"integrity\": \"sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/mime-types\": {\n      \"version\": \"3.0.1\",\n      \"resolved\": \"https://registry.npmjs.org/mime-types/-/mime-types-3.0.1.tgz\",\n      \"integrity\": \"sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"mime-db\": \"^1.54.0\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/minimist\": {\n      \"version\": \"1.2.8\",\n      \"resolved\": \"https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz\",\n      \"integrity\": \"sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==\",\n      \"license\": \"MIT\",\n      \"funding\": {\n        \"url\": \"https://github.com/sponsors/ljharb\"\n      }\n    },\n    \"node_modules/mkdirp\": {\n      \"version\": \"0.5.6\",\n      \"resolved\": \"https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz\",\n      \"integrity\": \"sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"minimist\": \"^1.2.6\"\n      },\n      \"bin\": {\n        \"mkdirp\": \"bin/cmd.js\"\n      }\n    },\n    \"node_modules/ms\": {\n      \"version\": \"2.1.3\",\n      \"resolved\": \"https://registry.npmjs.org/ms/-/ms-2.1.3.tgz\",\n      \"integrity\": \"sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==\",\n      \"license\": \"MIT\"\n    },\n    \"node_modules/multer\": {\n      \"version\": \"1.4.5-lts.2\",\n      \"resolved\": \"https://registry.npmjs.org/multer/-/multer-1.4.5-lts.2.tgz\",\n      \"integrity\": \"sha512-VzGiVigcG9zUAoCNU+xShztrlr1auZOlurXynNvO9GiWD1/mTBbUljOKY+qMeazBqXgRnjzeEgJI/wyjJUHg9A==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"append-field\": \"^1.0.0\",\n        \"busboy\": \"^1.0.0\",\n        \"concat-stream\": \"^1.5.2\",\n        \"mkdirp\": \"^0.5.4\",\n        \"object-assign\": \"^4.1.1\",\n        \"type-is\": \"^1.6.4\",\n        \"xtend\": \"^4.0.0\"\n      },\n      \"engines\": {\n        \"node\": \">= 6.0.0\"\n      }\n    },\n    \"node_modules/multer/node_modules/media-typer\": {\n      \"version\": \"0.3.0\",\n      \"resolved\": \"https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz\",\n      \"integrity\": \"sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/multer/node_modules/mime-db\": {\n      \"version\": \"1.52.0\",\n      \"resolved\": \"https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz\",\n      \"integrity\": \"sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/multer/node_modules/mime-types\": {\n      \"version\": \"2.1.35\",\n      \"resolved\": \"https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz\",\n      \"integrity\": \"sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"mime-db\": \"1.52.0\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/multer/node_modules/type-is\": {\n      \"version\": \"1.6.18\",\n      \"resolved\": \"https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz\",\n      \"integrity\": \"sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"media-typer\": \"0.3.0\",\n        \"mime-types\": \"~2.1.24\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/negotiator\": {\n      \"version\": \"1.0.0\",\n      \"resolved\": \"https://registry.npmjs.org/negotiator/-/negotiator-1.0.0.tgz\",\n      \"integrity\": \"sha512-8Ofs/AUQh8MaEcrlq5xOX0CQ9ypTF5dl78mjlMNfOK08fzpgTHQRQPBxcPlEtIw0yRpws+Zo/3r+5WRby7u3Gg==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/object-assign\": {\n      \"version\": \"4.1.1\",\n      \"resolved\": \"https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz\",\n      \"integrity\": \"sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">=0.10.0\"\n      }\n    },\n    \"node_modules/object-inspect\": {\n      \"version\": \"1.13.4\",\n      \"resolved\": \"https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz\",\n      \"integrity\": \"sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      },\n      \"funding\": {\n        \"url\": \"https://github.com/sponsors/ljharb\"\n      }\n    },\n    \"node_modules/on-finished\": {\n      \"version\": \"2.4.1\",\n      \"resolved\": \"https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz\",\n      \"integrity\": \"sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"ee-first\": \"1.1.1\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.8\"\n      }\n    },\n    \"node_modules/once\": {\n      \"version\": \"1.4.0\",\n      \"resolved\": \"https://registry.npmjs.org/once/-/once-1.4.0.tgz\",\n      \"integrity\": \"sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==\",\n      \"license\": \"ISC\",\n      \"dependencies\": {\n        \"wrappy\": \"1\"\n      }\n    },\n    \"node_modules/parseurl\": {\n      \"version\": \"1.3.3\",\n      \"resolved\": \"https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz\",\n      \"integrity\": \"sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.8\"\n      }\n    },\n    \"node_modules/path-to-regexp\": {\n      \"version\": \"8.2.0\",\n      \"resolved\": \"https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-8.2.0.tgz\",\n      \"integrity\": \"sha512-TdrF7fW9Rphjq4RjrW0Kp2AW0Ahwu9sRGTkS6bvDi0SCwZlEZYmcfDbEsTz8RVk0EHIS/Vd1bv3JhG+1xZuAyQ==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">=16\"\n      }\n    },\n    \"node_modules/process-nextick-args\": {\n      \"version\": \"2.0.1\",\n      \"resolved\": \"https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz\",\n      \"integrity\": \"sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==\",\n      \"license\": \"MIT\"\n    },\n    \"node_modules/proxy-addr\": {\n      \"version\": \"2.0.7\",\n      \"resolved\": \"https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz\",\n      \"integrity\": \"sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"forwarded\": \"0.2.0\",\n        \"ipaddr.js\": \"1.9.1\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.10\"\n      }\n    },\n    \"node_modules/proxy-from-env\": {\n      \"version\": \"1.1.0\",\n      \"resolved\": \"https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz\",\n      \"integrity\": \"sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==\",\n      \"license\": \"MIT\"\n    },\n    \"node_modules/qs\": {\n      \"version\": \"6.14.0\",\n      \"resolved\": \"https://registry.npmjs.org/qs/-/qs-6.14.0.tgz\",\n      \"integrity\": \"sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==\",\n      \"license\": \"BSD-3-Clause\",\n      \"dependencies\": {\n        \"side-channel\": \"^1.1.0\"\n      },\n      \"engines\": {\n        \"node\": \">=0.6\"\n      },\n      \"funding\": {\n        \"url\": \"https://github.com/sponsors/ljharb\"\n      }\n    },\n    \"node_modules/range-parser\": {\n      \"version\": \"1.2.1\",\n      \"resolved\": \"https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz\",\n      \"integrity\": \"sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/raw-body\": {\n      \"version\": \"3.0.0\",\n      \"resolved\": \"https://registry.npmjs.org/raw-body/-/raw-body-3.0.0.tgz\",\n      \"integrity\": \"sha512-RmkhL8CAyCRPXCE28MMH0z2PNWQBNk2Q09ZdxM9IOOXwxwZbN+qbWaatPkdkWIKL2ZVDImrN/pK5HTRz2PcS4g==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"bytes\": \"3.1.2\",\n        \"http-errors\": \"2.0.0\",\n        \"iconv-lite\": \"0.6.3\",\n        \"unpipe\": \"1.0.0\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.8\"\n      }\n    },\n    \"node_modules/readable-stream\": {\n      \"version\": \"2.3.8\",\n      \"resolved\": \"https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz\",\n      \"integrity\": \"sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"core-util-is\": \"~1.0.0\",\n        \"inherits\": \"~2.0.3\",\n        \"isarray\": \"~1.0.0\",\n        \"process-nextick-args\": \"~2.0.0\",\n        \"safe-buffer\": \"~5.1.1\",\n        \"string_decoder\": \"~1.1.1\",\n        \"util-deprecate\": \"~1.0.1\"\n      }\n    },\n    \"node_modules/readable-stream/node_modules/safe-buffer\": {\n      \"version\": \"5.1.2\",\n      \"resolved\": \"https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz\",\n      \"integrity\": \"sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==\",\n      \"license\": \"MIT\"\n    },\n    \"node_modules/router\": {\n      \"version\": \"2.2.0\",\n      \"resolved\": \"https://registry.npmjs.org/router/-/router-2.2.0.tgz\",\n      \"integrity\": \"sha512-nLTrUKm2UyiL7rlhapu/Zl45FwNgkZGaCpZbIHajDYgwlJCOzLSk+cIPAnsEqV955GjILJnKbdQC1nVPz+gAYQ==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"debug\": \"^4.4.0\",\n        \"depd\": \"^2.0.0\",\n        \"is-promise\": \"^4.0.0\",\n        \"parseurl\": \"^1.3.3\",\n        \"path-to-regexp\": \"^8.0.0\"\n      },\n      \"engines\": {\n        \"node\": \">= 18\"\n      }\n    },\n    \"node_modules/safe-buffer\": {\n      \"version\": \"5.2.1\",\n      \"resolved\": \"https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz\",\n      \"integrity\": \"sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==\",\n      \"funding\": [\n        {\n          \"type\": \"github\",\n          \"url\": \"https://github.com/sponsors/feross\"\n        },\n        {\n          \"type\": \"patreon\",\n          \"url\": \"https://www.patreon.com/feross\"\n        },\n        {\n          \"type\": \"consulting\",\n          \"url\": \"https://feross.org/support\"\n        }\n      ],\n      \"license\": \"MIT\"\n    },\n    \"node_modules/safer-buffer\": {\n      \"version\": \"2.1.2\",\n      \"resolved\": \"https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz\",\n      \"integrity\": \"sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==\",\n      \"license\": \"MIT\"\n    },\n    \"node_modules/send\": {\n      \"version\": \"1.2.0\",\n      \"resolved\": \"https://registry.npmjs.org/send/-/send-1.2.0.tgz\",\n      \"integrity\": \"sha512-uaW0WwXKpL9blXE2o0bRhoL2EGXIrZxQ2ZQ4mgcfoBxdFmQold+qWsD2jLrfZ0trjKL6vOw0j//eAwcALFjKSw==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"debug\": \"^4.3.5\",\n        \"encodeurl\": \"^2.0.0\",\n        \"escape-html\": \"^1.0.3\",\n        \"etag\": \"^1.8.1\",\n        \"fresh\": \"^2.0.0\",\n        \"http-errors\": \"^2.0.0\",\n        \"mime-types\": \"^3.0.1\",\n        \"ms\": \"^2.1.3\",\n        \"on-finished\": \"^2.4.1\",\n        \"range-parser\": \"^1.2.1\",\n        \"statuses\": \"^2.0.1\"\n      },\n      \"engines\": {\n        \"node\": \">= 18\"\n      }\n    },\n    \"node_modules/serve-static\": {\n      \"version\": \"2.2.0\",\n      \"resolved\": \"https://registry.npmjs.org/serve-static/-/serve-static-2.2.0.tgz\",\n      \"integrity\": \"sha512-61g9pCh0Vnh7IutZjtLGGpTA355+OPn2TyDv/6ivP2h/AdAVX9azsoxmg2/M6nZeQZNYBEwIcsne1mJd9oQItQ==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"encodeurl\": \"^2.0.0\",\n        \"escape-html\": \"^1.0.3\",\n        \"parseurl\": \"^1.3.3\",\n        \"send\": \"^1.2.0\"\n      },\n      \"engines\": {\n        \"node\": \">= 18\"\n      }\n    },\n    \"node_modules/setprototypeof\": {\n      \"version\": \"1.2.0\",\n      \"resolved\": \"https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz\",\n      \"integrity\": \"sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==\",\n      \"license\": \"ISC\"\n    },\n    \"node_modules/side-channel\": {\n      \"version\": \"1.1.0\",\n      \"resolved\": \"https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz\",\n      \"integrity\": \"sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"es-errors\": \"^1.3.0\",\n        \"object-inspect\": \"^1.13.3\",\n        \"side-channel-list\": \"^1.0.0\",\n        \"side-channel-map\": \"^1.0.1\",\n        \"side-channel-weakmap\": \"^1.0.2\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      },\n      \"funding\": {\n        \"url\": \"https://github.com/sponsors/ljharb\"\n      }\n    },\n    \"node_modules/side-channel-list\": {\n      \"version\": \"1.0.0\",\n      \"resolved\": \"https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz\",\n      \"integrity\": \"sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"es-errors\": \"^1.3.0\",\n        \"object-inspect\": \"^1.13.3\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      },\n      \"funding\": {\n        \"url\": \"https://github.com/sponsors/ljharb\"\n      }\n    },\n    \"node_modules/side-channel-map\": {\n      \"version\": \"1.0.1\",\n      \"resolved\": \"https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz\",\n      \"integrity\": \"sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"call-bound\": \"^1.0.2\",\n        \"es-errors\": \"^1.3.0\",\n        \"get-intrinsic\": \"^1.2.5\",\n        \"object-inspect\": \"^1.13.3\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      },\n      \"funding\": {\n        \"url\": \"https://github.com/sponsors/ljharb\"\n      }\n    },\n    \"node_modules/side-channel-weakmap\": {\n      \"version\": \"1.0.2\",\n      \"resolved\": \"https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz\",\n      \"integrity\": \"sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"call-bound\": \"^1.0.2\",\n        \"es-errors\": \"^1.3.0\",\n        \"get-intrinsic\": \"^1.2.5\",\n        \"object-inspect\": \"^1.13.3\",\n        \"side-channel-map\": \"^1.0.1\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.4\"\n      },\n      \"funding\": {\n        \"url\": \"https://github.com/sponsors/ljharb\"\n      }\n    },\n    \"node_modules/statuses\": {\n      \"version\": \"2.0.1\",\n      \"resolved\": \"https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz\",\n      \"integrity\": \"sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.8\"\n      }\n    },\n    \"node_modules/streamsearch\": {\n      \"version\": \"1.1.0\",\n      \"resolved\": \"https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz\",\n      \"integrity\": \"sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==\",\n      \"engines\": {\n        \"node\": \">=10.0.0\"\n      }\n    },\n    \"node_modules/string_decoder\": {\n      \"version\": \"1.1.1\",\n      \"resolved\": \"https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz\",\n      \"integrity\": \"sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"safe-buffer\": \"~5.1.0\"\n      }\n    },\n    \"node_modules/string_decoder/node_modules/safe-buffer\": {\n      \"version\": \"5.1.2\",\n      \"resolved\": \"https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz\",\n      \"integrity\": \"sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==\",\n      \"license\": \"MIT\"\n    },\n    \"node_modules/toidentifier\": {\n      \"version\": \"1.0.1\",\n      \"resolved\": \"https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz\",\n      \"integrity\": \"sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">=0.6\"\n      }\n    },\n    \"node_modules/type-is\": {\n      \"version\": \"2.0.1\",\n      \"resolved\": \"https://registry.npmjs.org/type-is/-/type-is-2.0.1.tgz\",\n      \"integrity\": \"sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw==\",\n      \"license\": \"MIT\",\n      \"dependencies\": {\n        \"content-type\": \"^1.0.5\",\n        \"media-typer\": \"^1.1.0\",\n        \"mime-types\": \"^3.0.0\"\n      },\n      \"engines\": {\n        \"node\": \">= 0.6\"\n      }\n    },\n    \"node_modules/typedarray\": {\n      \"version\": \"0.0.6\",\n      \"resolved\": \"https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz\",\n      \"integrity\": \"sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA==\",\n      \"license\": \"MIT\"\n    },\n    \"node_modules/unpipe\": {\n      \"version\": \"1.0.0\",\n      \"resolved\": \"https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz\",\n      \"integrity\": \"sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.8\"\n      }\n    },\n    \"node_modules/util-deprecate\": {\n      \"version\": \"1.0.2\",\n      \"resolved\": \"https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz\",\n      \"integrity\": \"sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==\",\n      \"license\": \"MIT\"\n    },\n    \"node_modules/vary\": {\n      \"version\": \"1.1.2\",\n      \"resolved\": \"https://registry.npmjs.org/vary/-/vary-1.1.2.tgz\",\n      \"integrity\": \"sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">= 0.8\"\n      }\n    },\n    \"node_modules/wrappy\": {\n      \"version\": \"1.0.2\",\n      \"resolved\": \"https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz\",\n      \"integrity\": \"sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==\",\n      \"license\": \"ISC\"\n    },\n    \"node_modules/xtend\": {\n      \"version\": \"4.0.2\",\n      \"resolved\": \"https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz\",\n      \"integrity\": \"sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==\",\n      \"license\": \"MIT\",\n      \"engines\": {\n        \"node\": \">=0.4\"\n      }\n    }\n  }\n}\n\n\n--- File Index 4: backend/package.json ---\n{\n  \"name\": \"backend\",\n  \"version\": \"1.0.0\",\n  \"main\": \"index.js\",\n  \"scripts\": {\n    \"test\": \"echo \\\"Error: no test specified\\\" && exit 1\",\n    \"start\": \"node server.js\"\n  },\n  \"keywords\": [],\n  \"author\": \"\",\n  \"license\": \"ISC\",\n  \"description\": \"\",\n  \"dependencies\": {\n    \"axios\": \"^1.8.4\",\n    \"cors\": \"^2.8.5\",\n    \"dotenv\": \"^16.4.7\",\n    \"express\": \"^5.1.0\",\n    \"multer\": \"^1.4.5-lts.2\"\n  }\n}\n\n\n--- File Index 5: backend/server.js ---\nrequire('dotenv').config();\nconst express = require('express');\nconst axios = require('axios');\nconst multer = require('multer');\nconst cors = require('cors');\n\nconst app = express();\nconst port = process.env.PORT || 3001; // Use port from env or default to 3001\n\n// --- External API Configuration ---\nconst EXTERNAL_API_KEY = process.env.EXTERNAL_API_KEY;\nconst EXTERNAL_BASE_URL = \"https://techhk.aoscdn.com/\";\nconst MAXIMUM_RETRIES = 20; // As defined in the original frontend code\n\nif (!EXTERNAL_API_KEY) {\n    console.error(\"FATAL ERROR: EXTERNAL_API_KEY is not defined in the environment variables.\");\n}\n\n\n// --- Middleware ---\napp.use(cors());\napp.use(express.json());\n\n// Configure Multer for file uploads (using memory storage for simplicity)\nconst storage = multer.memoryStorage();\nconst upload = multer({ storage: storage });\n\n// --- API Endpoints ---\n\n\n// POST /api/enhance - Receives image, uploads to external API, returns task ID\napp.post('/api/enhance', upload.single('image_file'), async (req, res, next) => {\n    console.log(\"Received file:\", req.file?.originalname);\n\n    if (!req.file) {\n        return res.status(400).json({ message: 'No image file uploaded.' });\n    }\n\n    if (!EXTERNAL_API_KEY) {\n        console.error(\"API Key missing in backend configuration.\");\n        return res.status(500).json({ message: 'Server configuration error.' });\n    }\n\n    const formData = new FormData();\n    // Convert buffer to Blob before appending\n    const imageBlob = new Blob([req.file.buffer], { type: req.file.mimetype });\n    formData.append('image_file', imageBlob, req.file.originalname);\n\n    try {\n        console.log(`Uploading ${req.file.originalname} to external API...`);\n        const { data } = await axios.post(\n            `${EXTERNAL_BASE_URL}/api/tasks/visual/scale`,\n            formData,\n            {\n                headers: {\n                    'X-API-KEY': EXTERNAL_API_KEY,\n                    ...formData.getHeaders?.() // Necessary for Axios with FormData in Node.js\n                },\n                maxBodyLength: Infinity, // Handle large file uploads\n                maxContentLength: Infinity\n            }\n        );\n\n        console.log(\"External API Response:\", data);\n\n        if (!data?.data?.task_id) {\n            console.error(\"Failed to get task_id from external API response:\", data);\n            throw new Error(\"Failed to upload image to external service! Task ID not found.\");\n        }\n\n        const taskId = data.data.task_id;\n        console.log(\"Image Uploaded Successfully, Task ID:\", taskId);\n        res.json({ taskId: taskId });\n\n    } catch (error) {\n        console.error(\"Error in /api/enhance:\", error.response?.data || error.message);\n        // Forward specific error message if available, otherwise generic\n        const errorMessage = error.response?.data?.message || error.message || 'Failed to process image enhancement request.';\n        const statusCode = error.response?.status || 500;\n        res.status(statusCode).json({ message: errorMessage });\n    }\n});\n\n// GET /api/status/:taskId - Polls external API for enhancement status/result\napp.get('/api/status/:taskId', async (req, res) => {\n    const { taskId } = req.params;\n    console.log(\"Checking status for task:\", taskId);\n\n    if (!EXTERNAL_API_KEY) {\n        console.error(\"API Key missing in backend configuration.\");\n        return res.status(500).json({ message: 'Server configuration error.' });\n    }\n\n    try {\n        const { data } = await axios.get(\n            `${EXTERNAL_BASE_URL}/api/tasks/visual/scale/${taskId}`,\n            {\n                headers: {\n                    'X-API-KEY': EXTERNAL_API_KEY,\n                },\n            }\n        );\n\n        console.log(`Status for task ${taskId}:`, data.data?.state, data.data?.image_url);\n\n        if (!data?.data) {\n             console.error(\"No data found for task:\", taskId, \"Response:\", data);\n            // It's possible the task isn't ready or doesn't exist, return appropriate status\n             return res.status(404).json({ message: 'Task status not found or task not yet processed.' });\n        }\n\n        // Return the relevant data from the external API response\n        // The frontend will handle the polling logic based on the 'state'\n        res.json(data.data);\n\n    } catch (error) {\n        console.error(`Error fetching status for task ${taskId}:`, error.response?.data || error.message);\n        const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch task status.';\n        const statusCode = error.response?.status || 500;\n        res.status(statusCode).json({ message: errorMessage });\n    }\n});\n\n// --- Basic Error Handling ---\napp.use((err, req, res, next) => {\n    console.error(err.stack);\n    res.status(500).send('Something broke!');\n});\n\n// --- Start Server ---\napp.listen(port, () => {\n    console.log(`Backend server listening at http://localhost:${port}`);\n});\n\n--- File Index 6: eslint.config.js ---\nimport js from '@eslint/js'\nimport globals from 'globals'\nimport reactHooks from 'eslint-plugin-react-hooks'\nimport reactRefresh from 'eslint-plugin-react-refresh'\n\nexport default [\n  { ignores: ['dist'] },\n  {\n    files: ['**/*.{js,jsx}'],\n    languageOptions: {\n      ecmaVersion: 2020,\n      globals: globals.browser,\n      parserOptions: {\n        ecmaVersion: 'latest',\n        ecmaFeatures: { jsx: true },\n        sourceType: 'module',\n      },\n    },\n    plugins: {\n      'react-hooks': reactHooks,\n      'react-refresh': reactRefresh,\n    },\n    rules: {\n      ...js.configs.recommended.rules,\n      ...reactHooks.configs.recommended.rules,\n      'no-unused-vars': ['error', { varsIgnorePattern: '^[A-Z_]' }],\n      'react-refresh/only-export-components': [\n        'warn',\n        { allowConstantExport: true },\n      ],\n    },\n  },\n]\n\n\n--- File Index 7: index.html ---\n<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <link rel=\"icon\" type=\"image/svg+xml\" href=\"/vite.svg\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>Image Enhancer</title>\n  </head>\n  <body>\n    <div id=\"root\"></div>\n    <script type=\"module\" src=\"/src/main.jsx\"></script>\n  </body>\n</html>\n\n\n\n--- File Index 8: package.json ---\n{\n  \"name\": \"image-enhancer\",\n  \"private\": true,\n  \"version\": \"0.0.0\",\n  \"type\": \"module\",\n  \"scripts\": {\n    \"dev\": \"vite\",\n    \"build\": \"vite build\",\n    \"lint\": \"eslint .\",\n    \"preview\": \"vite preview\"\n  },\n  \"dependencies\": {\n    \"@tailwindcss/vite\": \"^4.0.17\",\n    \"axios\": \"^1.8.4\",\n    \"react\": \"^19.0.0\",\n    \"react-compare-slider\": \"^3.1.0\",\n    \"react-dom\": \"^19.0.0\",\n    \"tailwindcss\": \"^4.0.17\"\n  },\n  \"devDependencies\": {\n    \"@eslint/js\": \"^9.21.0\",\n    \"@types/react\": \"^19.0.10\",\n    \"@types/react-dom\": \"^19.0.4\",\n    \"@vitejs/plugin-react\": \"^4.3.4\",\n    \"eslint\": \"^9.21.0\",\n    \"eslint-plugin-react-hooks\": \"^5.1.0\",\n    \"eslint-plugin-react-refresh\": \"^0.4.19\",\n    \"globals\": \"^15.15.0\",\n    \"vite\": \"^6.2.0\"\n  }\n}\n\n\n--- File Index 9: public/vite.svg ---\n<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" aria-hidden=\"true\" role=\"img\" class=\"iconify iconify--logos\" width=\"31.88\" height=\"32\" preserveAspectRatio=\"xMidYMid meet\" viewBox=\"0 0 256 257\"><defs><linearGradient id=\"IconifyId1813088fe1fbc01fb466\" x1=\"-.828%\" x2=\"57.636%\" y1=\"7.652%\" y2=\"78.411%\"><stop offset=\"0%\" stop-color=\"#41D1FF\"></stop><stop offset=\"100%\" stop-color=\"#BD34FE\"></stop></linearGradient><linearGradient id=\"IconifyId1813088fe1fbc01fb467\" x1=\"43.376%\" x2=\"50.316%\" y1=\"2.242%\" y2=\"89.03%\"><stop offset=\"0%\" stop-color=\"#FFEA83\"></stop><stop offset=\"8.333%\" stop-color=\"#FFDD35\"></stop><stop offset=\"100%\" stop-color=\"#FFA800\"></stop></linearGradient></defs><path fill=\"url(#IconifyId1813088fe1fbc01fb466)\" d=\"M255.153 37.938L134.897 252.976c-2.483 4.44-8.862 4.466-11.382.048L.875 37.958c-2.746-4.814 1.371-10.646 6.827-9.67l120.385 21.517a6.537 6.537 0 0 0 2.322-.004l117.867-21.483c5.438-.991 9.574 4.796 6.877 9.62Z\"></path><path fill=\"url(#IconifyId1813088fe1fbc01fb467)\" d=\"M185.432.063L96.44 17.501a3.268 3.268 0 0 0-2.634 3.014l-5.474 92.456a3.268 3.268 0 0 0 3.997 3.378l24.777-5.718c2.318-.535 4.413 1.507 3.936 3.838l-7.361 36.047c-.495 2.426 1.782 4.5 4.151 3.78l15.304-4.649c2.372-.72 4.652 1.36 4.15 3.788l-11.698 56.621c-.732 3.542 3.979 5.473 5.943 2.437l1.313-2.028l72.516-144.72c1.215-2.423-.88-5.186-3.54-4.672l-25.505 4.922c-2.396.462-4.435-1.77-3.759-4.114l16.646-57.705c.677-2.35-1.37-4.583-3.769-4.113Z\"></path></svg>\n\n--- File Index 10: src/App.jsx ---\nimport React, { useContext } from 'react';\nimport Home from './components/Home';\nimport { ThemeContext } from './contexts/ThemeContext';\n\nconst App = () => {\n  const { theme, toggleTheme } = useContext(ThemeContext);\n\n  return (\n    <div className='flex flex-col items-center justify-center min-h-screen bg-gray-200 dark:bg-gray-900 py-8 px-4 relative'>\n      <button\n        onClick={toggleTheme}\n        className=\"absolute top-4 right-4 p-2 rounded-md bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-gray-200\"\n      >\n        {theme === 'light' ? 'Switch to Dark Mode' : 'Switch to Light Mode'}\n      </button>\n      <div className='text-center mb-8'>\n        <h1 className='text-5xl font-bold text-gray-800 dark:text-gray-100 mb-2'>AI Image Enhancer</h1>\n        <p className='text-lg text-gray-500 dark:text-gray-400'>Upload an image and let AI enhance it in seconds.</p>\n      </div>\n      <Home/>\n      <div className='text-lg text-gray-500 dark:text-gray-400 mt-6'>\n        Powered by @Learning-On-Peak\n      </div>\n    </div>\n  );\n};\n\nexport default App;\n\n--- File Index 11: src/App.test.jsx ---\nimport React from 'react';\nimport { render, screen, fireEvent } from '@testing-library/react';\nimport '@testing-library/jest-dom';\nimport App from './App';\nimport { ThemeProvider } from './contexts/ThemeContext'; // ThemeProvider is needed to wrap App\n\n// Mock localStorage\nconst localStorageMock = (() => {\n  let store = {};\n  return {\n    getItem: (key) => store[key] || null,\n    setItem: (key, value) => {\n      store[key] = value.toString();\n    },\n    removeItem: (key) => {\n      delete store[key];\n    },\n    clear: () => {\n      store = {};\n    },\n  };\n})();\nObject.defineProperty(window, 'localStorage', { value: localStorageMock });\n\n// Helper function to render App with ThemeProvider\nconst renderApp = () => {\n  return render(\n    <ThemeProvider>\n      <App />\n    </ThemeProvider>\n  );\n};\n\ndescribe('Theme Toggle Functionality', () => {\n  beforeEach(() => {\n    // Clear localStorage and reset document class before each test\n    localStorage.clear();\n    document.documentElement.classList.remove('dark');\n    // Set default theme to light for consistent testing\n    localStorage.setItem('theme', 'light');\n  });\n\n  test('renders the theme toggle button', () => {\n    renderApp();\n    const buttonElement = screen.getByRole('button', { name: /switch to dark mode/i });\n    expect(buttonElement).toBeInTheDocument();\n  });\n\n  test('clicking the button toggles the theme and button text', () => {\n    renderApp();\n    const buttonElement = screen.getByRole('button', { name: /switch to dark mode/i });\n\n    // Initial state: light mode\n    expect(document.documentElement.classList.contains('dark')).toBe(false);\n\n    // Click to switch to dark mode\n    fireEvent.click(buttonElement);\n    expect(document.documentElement.classList.contains('dark')).toBe(true);\n    expect(buttonElement).toHaveTextContent(/switch to light mode/i);\n    expect(localStorage.getItem('theme')).toBe('dark');\n\n    // Click to switch back to light mode\n    fireEvent.click(buttonElement);\n    expect(document.documentElement.classList.contains('dark')).toBe(false);\n    expect(buttonElement).toHaveTextContent(/switch to dark mode/i);\n    expect(localStorage.getItem('theme')).toBe('light');\n  });\n\n  test('applies dark mode styles to the App container', () => {\n    renderApp();\n    const appContainer = screen.getByRole('button', { name: /switch to dark mode/i }).parentElement; // Get the parent div of the button\n    \n    // Initial state: light mode (bg-gray-200)\n    // Note: Testing exact Tailwind classes can be brittle.\n    // It's better to test computed styles if possible, but that's more complex.\n    // For now, we check for the presence of dark mode specific classes applied by ThemeContext.\n    expect(appContainer).toHaveClass('bg-gray-200'); // Light mode background\n    expect(appContainer).not.toHaveClass('dark:bg-gray-900'); // Ensure dark mode class for App.jsx div is not misinterpreted as active style\n\n    // Click to switch to dark mode\n    const buttonElement = screen.getByRole('button', { name: /switch to dark mode/i });\n    fireEvent.click(buttonElement);\n    \n    // Dark mode (dark:bg-gray-900)\n    // The class 'dark:bg-gray-900' is applied, and the 'dark' class on html enables it.\n    // We already tested document.documentElement.classList.contains('dark')\n    // Here we check if the component has the correct dark mode class defined.\n    expect(appContainer).toHaveClass('dark:bg-gray-900');\n  });\n\n  test('loads theme from localStorage on initial render', () => {\n    localStorage.setItem('theme', 'dark'); // Set initial theme to dark in localStorage\n    \n    // Re-render the app\n    const { getByRole, unmount } = render(\n      <ThemeProvider>\n        <App />\n      </ThemeProvider>\n    );\n    \n    expect(document.documentElement.classList.contains('dark')).toBe(true);\n    const buttonElement = getByRole('button', { name: /switch to light mode/i });\n    expect(buttonElement).toBeInTheDocument();\n\n    // Clean up\n    unmount(); // Unmount to allow beforeEach to reset correctly for subsequent tests\n  });\n});\n\n\n--- File Index 12: src/components/Home.jsx ---\nimport React, { useState } from 'react'\nimport ImageUpload from './ImageUpload'\nimport ImagePreview from './ImagePreview'\nimport { enhanceImage } from '../services/imageEnhancer'\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  const [enhancedImage, setEnhancedImage] = useState(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      const imageUrl = URL.createObjectURL(imageFile)\n      setOriginalImage(imageUrl)\n      \n      setIsLoading(true)\n      setEnhancedImage(null)\n      \n      try {\n        const enhancedImageData = await enhanceImage(imageFile)\n        // Extract the image URL from the response object\n        setEnhancedImage(enhancedImageData.image)\n        setIsLoading(false)\n      } catch (error) {\n        console.log(error);\n        alert('Failed to enhance image. Please try again later.')\n        setIsLoading(false)\n      }\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      <ImageUpload onImageUpload={handleImageUpload} />\n      <ImagePreview \n        originalImage={originalImage} \n        enhancedImage={enhancedImage}\n        isLoading={isLoading}\n      />\n    </div>\n  )\n}\n\nexport default Home\n\n\n--- File Index 13: src/components/ImagePreview.jsx ---\nimport React from 'react';\nimport { ReactCompareSlider, ReactCompareSliderImage } from 'react-compare-slider';\nimport Loading from './Loading';\n\nconst ImagePreview = ({ originalImage, enhancedImage, isLoading }) => {\n    // If loading, show the loading indicator\n    if (isLoading) {\n        return (\n            <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl flex justify-center items-center min-h-[300px]\">\n                <Loading />\n                {/* Optionally show original image dimmed while loading */}\n                {/* originalImage && <img src={originalImage} alt=\"Original Uploaded\" className=\"mt-4 max-w-full h-auto rounded opacity-50\" /> */}\n            </div>\n        );\n    }\n\n    // If not loading but no original image, show nothing or a placeholder\n    if (!originalImage) {\n        return null; // Or a placeholder message like <p>Upload an image to see the preview.</p>\n    }\n\n    // If not loading and original image exists, but no enhanced image yet (after upload, before enhancement finishes or if enhancement failed)\n    // We only show the original image in this case.\n    if (originalImage && !enhancedImage && !isLoading) {\n         return (\n             <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl\">\n                <h2 className=\"text-xl font-semibold text-center text-gray-700 dark:text-gray-300 mb-4\">Original Image</h2>\n                <img src={originalImage} alt=\"Original Uploaded\" className=\"max-w-full h-auto rounded mx-auto\" style={{ maxHeight: '60vh' }} />\n            </div>\n         );\n    }\n\n\n    // If both images are available, show the comparison slider\n    if (originalImage && enhancedImage) {\n        // Define common styles to ensure images fit within their container and align\n        const imageStyle = {\n            width: '100%',\n            height: '100%',\n            objectFit: 'contain', // Fit entire image within the container, preserving aspect ratio\n            display: 'block', // Prevents potential extra space below the image\n        };\n\n        return (\n             <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl\">\n                 <h2 className=\"text-xl font-semibold text-center text-gray-700 dark:text-gray-300 mb-4\">Compare Images</h2>\n                 {/* --- Start of ReactCompareSlider code --- */}\n                 <ReactCompareSlider\n                     style={{ height: '70vh', width: '100%', margin: '0 auto' }}\n                    itemOne={\n                        <div style={{ width: '100%', height: '100%', overflow: 'hidden' }}>\n                             <ReactCompareSliderImage\n                                src={originalImage}\n                                alt=\"Original Image\"\n                                style={imageStyle} // Apply styles\n                             />\n                        </div>\n                     }\n                    itemTwo={\n                        <div style={{ width: '100%', height: '100%', overflow: 'hidden' }}>\n                            <ReactCompareSliderImage\n                                src={enhancedImage}\n                                alt=\"Enhanced Image\"\n                                style={imageStyle} // Apply styles\n                             />\n                        </div>\n                     }\n                />\n                {/* --- End of ReactCompareSlider code --- */}\n                 {/* --- EDIT START: Restore the download button --- */}\n                 <div className=\"text-center mt-4\">\n                        <a\n                          href={enhancedImage} // Use the enhanced image URL\n                          download=\"enhanced-image.png\" // Suggest a filename for download\n                          className=\"inline-block bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-bold py-2 px-4 rounded transition duration-300\"\n                        >\n                            Download Enhanced Image\n                        </a>\n                 </div>\n                 {/* --- EDIT END --- */}\n            </div>\n        );\n        // The closing bracket for this block might look duplicated due to the previous comment format, it's correct.\n    }\n\n    // Fallback case (shouldn't normally be reached)\n    return null;\n};\n\nexport default ImagePreview;\n\n\n--- File Index 14: src/components/ImageUpload.jsx ---\nimport React, { useState, useRef } from 'react'\n\nconst ImageUpload = ({ onImageUpload }) => {\n  const [isDragging, setIsDragging] = useState(false)\n  const [fileName, setFileName] = useState('')\n  const fileInputRef = useRef(null)\n\n  const handleDragOver = (e) => {\n    e.preventDefault()\n    setIsDragging(true)\n  }\n\n  const handleDragLeave = () => {\n    setIsDragging(false)\n  }\n\n  const handleDrop = (e) => {\n    e.preventDefault()\n    setIsDragging(false)\n    \n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      const file = e.dataTransfer.files[0]\n      processFile(file)\n    }\n  }\n\n  const handleFileChange = (e) => {\n    if (e.target.files && e.target.files[0]) {\n      const file = e.target.files[0]\n      processFile(file)\n    }\n  }\n\n  const processFile = (file) => {\n    if (!file.type.match('image.*')) {\n      alert('Please select an image file')\n      return\n    }\n    \n    setFileName(file.name)\n    onImageUpload(file)\n  }\n\n  const handleClick = () => {\n    fileInputRef.current.click()\n  }\n\n  return (\n    <div className='bg-white dark:bg-gray-800 shadow-lg rounded-2xl w-full max-w-2xl p-6'>\n      <div \n        className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-all cursor-pointer\n          ${isDragging ? 'border-blue-500 bg-blue-50 dark:bg-blue-900' : 'border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400'}`}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onDrop={handleDrop}\n        onClick={handleClick}\n      >\n        <input \n          type=\"file\" \n          id=\"fileInput\" \n          ref={fileInputRef}\n          className='hidden' \n          accept=\"image/*\"\n          onChange={handleFileChange}\n        />\n        \n        <div className=\"flex flex-col items-center justify-center py-4\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-12 w-12 text-gray-400 dark:text-gray-500 mb-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n          </svg>\n          \n          <p className='text-lg font-medium text-gray-600 dark:text-gray-300 mb-1'>\n            {fileName ? `Selected: ${fileName}` : 'Click or drag to upload an image'}\n          </p>\n          <p className='text-sm text-gray-500 dark:text-gray-400'>\n            Supports JPG, PNG, WEBP (Max 10MB)\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport default ImageUpload\n\n\n--- File Index 15: src/components/Loading.jsx ---\nimport React from 'react';\n\nconst Loading = () => {\n  return (\n    <div className=\"flex flex-col items-center justify-center p-4\">\n      {/* Optional: Add a simple spinner animation if desired */}\n      {/* <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-gray-100 mb-4\"></div> */}\n      <p className=\"text-lg font-semibold text-gray-700 dark:text-gray-300\">\n        Enhancing your image, please wait...\n      </p>\n    </div>\n  );\n};\n\nexport default Loading;\n\n--- File Index 16: src/contexts/ThemeContext.jsx ---\nimport React, { createContext, useState, useEffect } from 'react';\n\n// Create the context\nexport const ThemeContext = createContext();\n\n// Create the provider component\nexport const ThemeProvider = ({ children }) => {\n  const [theme, setTheme] = useState('light');\n\n  // Function to toggle theme\n  const toggleTheme = () => {\n    setTheme((prevTheme) => (prevTheme === 'light' ? 'dark' : 'light'));\n  };\n\n  // Effect to load theme from localStorage and apply it\n  useEffect(() => {\n    const storedTheme = localStorage.getItem('theme');\n    if (storedTheme) {\n      setTheme(storedTheme);\n    }\n  }, []);\n\n  // Effect to update localStorage and HTML element class when theme changes\n  useEffect(() => {\n    localStorage.setItem('theme', theme);\n    if (theme === 'dark') {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n  }, [theme]);\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\n\n--- File Index 17: src/index.css ---\n@import \"tailwindcss\";\n\n--- File Index 18: src/main.jsx ---\nimport { StrictMode } from 'react'\nimport { createRoot } from 'react-dom/client'\nimport './index.css'\nimport App from './App.jsx'\nimport { ThemeProvider } from './contexts/ThemeContext';\n\ncreateRoot(document.getElementById('root')).render(\n  <StrictMode>\n    <ThemeProvider>\n      <App />\n    </ThemeProvider>\n  </StrictMode>,\n)\n\n\n--- File Index 19: src/services/imageEnhancer.js ---\nimport axios from \"axios\";\n\nconst BASE_URL = \"http://localhost:3001\"; // URL of our backend proxy\nconst MAXIMUM_RETRIES = 20; // Max polling attempts\n\nexport const enhanceImage = async (file) => {\n    try {\n        const taskId = await uploadImage(file);\n        console.log(\"Image Uploaded Successfully, Task ID:\", taskId);\n\n        const enhancedImageData = await PollForEnhancedImage(taskId);\n        console.log(\"Enhanced Image Data:\", enhancedImageData);\n\n        return enhancedImageData;\n    } catch (error) {\n        // Log the error potentially coming from the backend or network issues\n        console.error(\"Error enhancing image:\", error.response?.data?.message || error.message);\n        // Optionally re-throw or return an error indicator to the UI\n        throw error; // Re-throw the error so the component can handle it (e.g., show message)\n    }\n};\n\nconst uploadImage = async (file) => {\n    const formData = new FormData();\n    formData.append(\"image_file\", file);\n\n    // Send image to our backend proxy\n    const { data } = await axios.post(\n        `${BASE_URL}/api/enhance`, // Endpoint on our backend\n        formData,\n        {\n            headers: {\n                // Content-Type is set automatically by browser for FormData\n                // No API Key needed here\n            },\n        }\n    );\n\n    // Our backend returns { taskId: '...' } directly\n    if (!data?.taskId) {\n        throw new Error(\"Failed to upload image! Task ID not received from backend.\");\n    }\n    return data.taskId;\n};\n\nconst PollForEnhancedImage = async (taskId, retries = 0) => {\n    const result = await fetchEnhancedImage(taskId);\n\n    // Check if the task state indicates it's still processing (state 4 means processing)\n    if (result.state === 4) {\n        console.log(`Processing...(${retries}/${MAXIMUM_RETRIES})`);\n\n        if (retries >= MAXIMUM_RETRIES) {\n            throw new Error(\"Max retries reached. Please try again later.\");\n        }\n\n        // wait for 2 second\n        await new Promise((resolve) => setTimeout(resolve, 2000));\n\n        return PollForEnhancedImage(taskId, retries + 1);\n    }\n\n    console.log(\"Enhanced Image URL:\", result);\n    return result;\n};\n\nconst fetchEnhancedImage = async (taskId) => {\n    // Fetch status from our backend proxy\n    const { data } = await axios.get(\n        `${BASE_URL}/api/status/${taskId}`, // Status endpoint on our backend\n        {\n            // No headers needed here\n        }\n    );\n    // Backend forwards the external API's data structure, which might be directly the data object\n    // or nested under 'data'. Adjust based on backend's response structure.\n    // Assuming backend returns the external API's data directly:\n    if (!data) { // Check if data object itself exists\n        throw new Error(\"Failed to fetch enhanced image status from backend!\");\n    }\n    // The polling logic expects the object containing 'state', etc.\n    return data;\n};\n\n\n--- File Index 20: tailwind.config.js ---\n/** @type {import('tailwindcss').Config} */\nexport default {\n  content: [\n    \"./index.html\",\n    \"./src/**/*.{js,ts,jsx,tsx}\",\n  ],\n  theme: {\n    extend: {},\n  },\n  plugins: [],\n  darkMode: 'class',\n}\n\n\n--- File Index 21: vite.config.js ---\nimport { defineConfig } from 'vite'\nimport react from '@vitejs/plugin-react'\nimport tailwindcss from '@tailwindcss/vite'\n// https://vite.dev/config/\nexport default defineConfig({\n  plugins: [react(),\n    tailwindcss(),\n  ],\n})\n\n\n\n\nAnalyze the codebase context.\nIdentify the top 5-10 core most important abstractions to help those new to the codebase.\n\nFor each abstraction, provide:\n1. A concise `name`.\n2. A beginner-friendly `description` explaining what it is with a simple analogy, in around 100 words.\n3. A list of relevant `file_indices` (integers) using the format `idx # path/comment`.\n\nList of file indices and paths present in the context:\n- 0 # .gitignore\n- 1 # README.md\n- 2 # backend/.env.example\n- 3 # backend/package-lock.json\n- 4 # backend/package.json\n- 5 # backend/server.js\n- 6 # eslint.config.js\n- 7 # index.html\n- 8 # package.json\n- 9 # public/vite.svg\n- 10 # src/App.jsx\n- 11 # src/App.test.jsx\n- 12 # src/components/Home.jsx\n- 13 # src/components/ImagePreview.jsx\n- 14 # src/components/ImageUpload.jsx\n- 15 # src/components/Loading.jsx\n- 16 # src/contexts/ThemeContext.jsx\n- 17 # src/index.css\n- 18 # src/main.jsx\n- 19 # src/services/imageEnhancer.js\n- 20 # tailwind.config.js\n- 21 # vite.config.js\n\nFormat the output as a YAML list of dictionaries:\n\n```yaml\n- name: |\n    Query Processing\n  description: |\n    Explains what the abstraction does.\n    It's like a central dispatcher routing requests.\n  file_indices:\n    - 0 # path/to/file1.py\n    - 3 # path/to/related.py\n- name: |\n    Query Optimization\n  description: |\n    Another core concept, similar to a blueprint for objects.\n  file_indices:\n    - 5 # path/to/another.js\n# ... up to 10 abstractions\n```": "```yaml\n- name: |\n    Frontend Application Core\n  description: |\n    This is the main brain of the user-facing part of the application. It orchestrates how users interact with the image enhancement process, from uploading a photo to displaying the final enhanced version. Think of it as the control center of the whole UI, managing different sections like image upload, preview, and overall layout. It ties together smaller UI components to create the complete user experience.\n  file_indices:\n    - 10 # src/App.jsx\n    - 12 # src/components/Home.jsx\n    - 18 # src/main.jsx\n    - 8 # package.json\n- name: |\n    Backend Proxy & API Gateway\n  description: |\n    This acts as a secure intermediary, or a \"bouncer,\" between the user-facing frontend and the external AI image enhancement service. When you upload an image, the frontend sends it here first. The backend then securely adds your secret API key (which the frontend doesn't see) and forwards the image to the external AI service. It also handles polling the external service for the enhancement status and sends the results back to the frontend.\n  file_indices:\n    - 5 # backend/server.js\n    - 2 # backend/.env.example\n    - 4 # backend/package.json\n- name: |\n    Image Enhancement Client Service\n  description: |\n    This is the dedicated \"messenger\" on the frontend side that talks to our backend server to get images enhanced. It handles the full client-side workflow for enhancement: first, it sends your image to the backend, then it repeatedly asks (or \"polls\") the backend for updates until the enhancement is complete and the final enhanced image URL is ready. It abstracts away the complex communication details from the main UI components.\n  file_indices:\n    - 19 # src/services/imageEnhancer.js\n- name: |\n    Image Upload Interface\n  description: |\n    This is the specific part of the user interface where you interact to bring your image into the application. It's like a special \"mailbox\" where you can either click to browse your files or simply drag and drop your image. It ensures that only valid image files are accepted and then prepares them to be sent off for enhancement.\n  file_indices:\n    - 14 # src/components/ImageUpload.jsx\n- name: |\n    Image Preview & Comparison Interface\n  description: |\n    Once an image is uploaded, this component acts as a \"photo album\" that displays your pictures. Initially, it shows your original image. Once enhanced, it transforms into a \"compare tool,\" allowing you to slide a bar across to see the before-and-after versions side-by-side. It also provides an easy way to download the new, improved image.\n  file_indices:\n    - 13 # src/components/ImagePreview.jsx\n- name: |\n    Theme Management System\n  description: |\n    This system is responsible for controlling the application's visual style, specifically switching between a \"light\" mode (bright colors) and a \"dark\" mode (darker colors). It acts like a \"light switch\" for the entire app. It remembers your preferred theme (even if you close and reopen the app) and applies the correct colors and backgrounds across the interface, enhancing readability and user comfort.\n  file_indices:\n    - 16 # src/contexts/ThemeContext.jsx\n    - 10 # src/App.jsx\n    - 20 # tailwind.config.js\n```", "\nBased on the following abstractions and relevant code snippets from the project `Simple-AI-Image-Enhancer`:\n\nList of Abstraction Indices and Names:\n0 # Frontend Application Core\n\n1 # Backend Proxy & API Gateway\n\n2 # Image Enhancement Client Service\n\n3 # Image Upload Interface\n\n4 # Image Preview & Comparison Interface\n\n5 # Theme Management System\n\n\nContext (Abstractions, Descriptions, Code):\nIdentified Abstractions:\\n- Index 0: Frontend Application Core\n (Relevant file indices: [8, 10, 12, 18])\\n  Description: This is the main brain of the user-facing part of the application. It orchestrates how users interact with the image enhancement process, from uploading a photo to displaying the final enhanced version. Think of it as the control center of the whole UI, managing different sections like image upload, preview, and overall layout. It ties together smaller UI components to create the complete user experience.\n\\n- Index 1: Backend Proxy & API Gateway\n (Relevant file indices: [2, 4, 5])\\n  Description: This acts as a secure intermediary, or a \"bouncer,\" between the user-facing frontend and the external AI image enhancement service. When you upload an image, the frontend sends it here first. The backend then securely adds your secret API key (which the frontend doesn't see) and forwards the image to the external AI service. It also handles polling the external service for the enhancement status and sends the results back to the frontend.\n\\n- Index 2: Image Enhancement Client Service\n (Relevant file indices: [19])\\n  Description: This is the dedicated \"messenger\" on the frontend side that talks to our backend server to get images enhanced. It handles the full client-side workflow for enhancement: first, it sends your image to the backend, then it repeatedly asks (or \"polls\") the backend for updates until the enhancement is complete and the final enhanced image URL is ready. It abstracts away the complex communication details from the main UI components.\n\\n- Index 3: Image Upload Interface\n (Relevant file indices: [14])\\n  Description: This is the specific part of the user interface where you interact to bring your image into the application. It's like a special \"mailbox\" where you can either click to browse your files or simply drag and drop your image. It ensures that only valid image files are accepted and then prepares them to be sent off for enhancement.\n\\n- Index 4: Image Preview & Comparison Interface\n (Relevant file indices: [13])\\n  Description: Once an image is uploaded, this component acts as a \"photo album\" that displays your pictures. Initially, it shows your original image. Once enhanced, it transforms into a \"compare tool,\" allowing you to slide a bar across to see the before-and-after versions side-by-side. It also provides an easy way to download the new, improved image.\n\\n- Index 5: Theme Management System\n (Relevant file indices: [10, 16, 20])\\n  Description: This system is responsible for controlling the application's visual style, specifically switching between a \"light\" mode (bright colors) and a \"dark\" mode (darker colors). It acts like a \"light switch\" for the entire app. It remembers your preferred theme (even if you close and reopen the app) and applies the correct colors and backgrounds across the interface, enhancing readability and user comfort.\n\\n\\nRelevant File Snippets (Referenced by Index and Path):\\n--- File: 2 # backend/.env.example ---\\n# API Key for the external image enhancement service\nEXTERNAL_API_KEY=YOUR_API_KEY_HERE\n\n# Optional: Port for the backend server (defaults to 3001 if not set)\nPORT=3001\\n\\n--- File: 4 # backend/package.json ---\\n{\n  \"name\": \"backend\",\n  \"version\": \"1.0.0\",\n  \"main\": \"index.js\",\n  \"scripts\": {\n    \"test\": \"echo \\\"Error: no test specified\\\" && exit 1\",\n    \"start\": \"node server.js\"\n  },\n  \"keywords\": [],\n  \"author\": \"\",\n  \"license\": \"ISC\",\n  \"description\": \"\",\n  \"dependencies\": {\n    \"axios\": \"^1.8.4\",\n    \"cors\": \"^2.8.5\",\n    \"dotenv\": \"^16.4.7\",\n    \"express\": \"^5.1.0\",\n    \"multer\": \"^1.4.5-lts.2\"\n  }\n}\n\\n\\n--- File: 5 # backend/server.js ---\\nrequire('dotenv').config();\nconst express = require('express');\nconst axios = require('axios');\nconst multer = require('multer');\nconst cors = require('cors');\n\nconst app = express();\nconst port = process.env.PORT || 3001; // Use port from env or default to 3001\n\n// --- External API Configuration ---\nconst EXTERNAL_API_KEY = process.env.EXTERNAL_API_KEY;\nconst EXTERNAL_BASE_URL = \"https://techhk.aoscdn.com/\";\nconst MAXIMUM_RETRIES = 20; // As defined in the original frontend code\n\nif (!EXTERNAL_API_KEY) {\n    console.error(\"FATAL ERROR: EXTERNAL_API_KEY is not defined in the environment variables.\");\n}\n\n\n// --- Middleware ---\napp.use(cors());\napp.use(express.json());\n\n// Configure Multer for file uploads (using memory storage for simplicity)\nconst storage = multer.memoryStorage();\nconst upload = multer({ storage: storage });\n\n// --- API Endpoints ---\n\n\n// POST /api/enhance - Receives image, uploads to external API, returns task ID\napp.post('/api/enhance', upload.single('image_file'), async (req, res, next) => {\n    console.log(\"Received file:\", req.file?.originalname);\n\n    if (!req.file) {\n        return res.status(400).json({ message: 'No image file uploaded.' });\n    }\n\n    if (!EXTERNAL_API_KEY) {\n        console.error(\"API Key missing in backend configuration.\");\n        return res.status(500).json({ message: 'Server configuration error.' });\n    }\n\n    const formData = new FormData();\n    // Convert buffer to Blob before appending\n    const imageBlob = new Blob([req.file.buffer], { type: req.file.mimetype });\n    formData.append('image_file', imageBlob, req.file.originalname);\n\n    try {\n        console.log(`Uploading ${req.file.originalname} to external API...`);\n        const { data } = await axios.post(\n            `${EXTERNAL_BASE_URL}/api/tasks/visual/scale`,\n            formData,\n            {\n                headers: {\n                    'X-API-KEY': EXTERNAL_API_KEY,\n                    ...formData.getHeaders?.() // Necessary for Axios with FormData in Node.js\n                },\n                maxBodyLength: Infinity, // Handle large file uploads\n                maxContentLength: Infinity\n            }\n        );\n\n        console.log(\"External API Response:\", data);\n\n        if (!data?.data?.task_id) {\n            console.error(\"Failed to get task_id from external API response:\", data);\n            throw new Error(\"Failed to upload image to external service! Task ID not found.\");\n        }\n\n        const taskId = data.data.task_id;\n        console.log(\"Image Uploaded Successfully, Task ID:\", taskId);\n        res.json({ taskId: taskId });\n\n    } catch (error) {\n        console.error(\"Error in /api/enhance:\", error.response?.data || error.message);\n        // Forward specific error message if available, otherwise generic\n        const errorMessage = error.response?.data?.message || error.message || 'Failed to process image enhancement request.';\n        const statusCode = error.response?.status || 500;\n        res.status(statusCode).json({ message: errorMessage });\n    }\n});\n\n// GET /api/status/:taskId - Polls external API for enhancement status/result\napp.get('/api/status/:taskId', async (req, res) => {\n    const { taskId } = req.params;\n    console.log(\"Checking status for task:\", taskId);\n\n    if (!EXTERNAL_API_KEY) {\n        console.error(\"API Key missing in backend configuration.\");\n        return res.status(500).json({ message: 'Server configuration error.' });\n    }\n\n    try {\n        const { data } = await axios.get(\n            `${EXTERNAL_BASE_URL}/api/tasks/visual/scale/${taskId}`,\n            {\n                headers: {\n                    'X-API-KEY': EXTERNAL_API_KEY,\n                },\n            }\n        );\n\n        console.log(`Status for task ${taskId}:`, data.data?.state, data.data?.image_url);\n\n        if (!data?.data) {\n             console.error(\"No data found for task:\", taskId, \"Response:\", data);\n            // It's possible the task isn't ready or doesn't exist, return appropriate status\n             return res.status(404).json({ message: 'Task status not found or task not yet processed.' });\n        }\n\n        // Return the relevant data from the external API response\n        // The frontend will handle the polling logic based on the 'state'\n        res.json(data.data);\n\n    } catch (error) {\n        console.error(`Error fetching status for task ${taskId}:`, error.response?.data || error.message);\n        const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch task status.';\n        const statusCode = error.response?.status || 500;\n        res.status(statusCode).json({ message: errorMessage });\n    }\n});\n\n// --- Basic Error Handling ---\napp.use((err, req, res, next) => {\n    console.error(err.stack);\n    res.status(500).send('Something broke!');\n});\n\n// --- Start Server ---\napp.listen(port, () => {\n    console.log(`Backend server listening at http://localhost:${port}`);\n});\\n\\n--- File: 8 # package.json ---\\n{\n  \"name\": \"image-enhancer\",\n  \"private\": true,\n  \"version\": \"0.0.0\",\n  \"type\": \"module\",\n  \"scripts\": {\n    \"dev\": \"vite\",\n    \"build\": \"vite build\",\n    \"lint\": \"eslint .\",\n    \"preview\": \"vite preview\"\n  },\n  \"dependencies\": {\n    \"@tailwindcss/vite\": \"^4.0.17\",\n    \"axios\": \"^1.8.4\",\n    \"react\": \"^19.0.0\",\n    \"react-compare-slider\": \"^3.1.0\",\n    \"react-dom\": \"^19.0.0\",\n    \"tailwindcss\": \"^4.0.17\"\n  },\n  \"devDependencies\": {\n    \"@eslint/js\": \"^9.21.0\",\n    \"@types/react\": \"^19.0.10\",\n    \"@types/react-dom\": \"^19.0.4\",\n    \"@vitejs/plugin-react\": \"^4.3.4\",\n    \"eslint\": \"^9.21.0\",\n    \"eslint-plugin-react-hooks\": \"^5.1.0\",\n    \"eslint-plugin-react-refresh\": \"^0.4.19\",\n    \"globals\": \"^15.15.0\",\n    \"vite\": \"^6.2.0\"\n  }\n}\n\\n\\n--- File: 10 # src/App.jsx ---\\nimport React, { useContext } from 'react';\nimport Home from './components/Home';\nimport { ThemeContext } from './contexts/ThemeContext';\n\nconst App = () => {\n  const { theme, toggleTheme } = useContext(ThemeContext);\n\n  return (\n    <div className='flex flex-col items-center justify-center min-h-screen bg-gray-200 dark:bg-gray-900 py-8 px-4 relative'>\n      <button\n        onClick={toggleTheme}\n        className=\"absolute top-4 right-4 p-2 rounded-md bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-gray-200\"\n      >\n        {theme === 'light' ? 'Switch to Dark Mode' : 'Switch to Light Mode'}\n      </button>\n      <div className='text-center mb-8'>\n        <h1 className='text-5xl font-bold text-gray-800 dark:text-gray-100 mb-2'>AI Image Enhancer</h1>\n        <p className='text-lg text-gray-500 dark:text-gray-400'>Upload an image and let AI enhance it in seconds.</p>\n      </div>\n      <Home/>\n      <div className='text-lg text-gray-500 dark:text-gray-400 mt-6'>\n        Powered by @Learning-On-Peak\n      </div>\n    </div>\n  );\n};\n\nexport default App;\\n\\n--- File: 12 # src/components/Home.jsx ---\\nimport React, { useState } from 'react'\nimport ImageUpload from './ImageUpload'\nimport ImagePreview from './ImagePreview'\nimport { enhanceImage } from '../services/imageEnhancer'\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  const [enhancedImage, setEnhancedImage] = useState(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      const imageUrl = URL.createObjectURL(imageFile)\n      setOriginalImage(imageUrl)\n      \n      setIsLoading(true)\n      setEnhancedImage(null)\n      \n      try {\n        const enhancedImageData = await enhanceImage(imageFile)\n        // Extract the image URL from the response object\n        setEnhancedImage(enhancedImageData.image)\n        setIsLoading(false)\n      } catch (error) {\n        console.log(error);\n        alert('Failed to enhance image. Please try again later.')\n        setIsLoading(false)\n      }\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      <ImageUpload onImageUpload={handleImageUpload} />\n      <ImagePreview \n        originalImage={originalImage} \n        enhancedImage={enhancedImage}\n        isLoading={isLoading}\n      />\n    </div>\n  )\n}\n\nexport default Home\n\\n\\n--- File: 13 # src/components/ImagePreview.jsx ---\\nimport React from 'react';\nimport { ReactCompareSlider, ReactCompareSliderImage } from 'react-compare-slider';\nimport Loading from './Loading';\n\nconst ImagePreview = ({ originalImage, enhancedImage, isLoading }) => {\n    // If loading, show the loading indicator\n    if (isLoading) {\n        return (\n            <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl flex justify-center items-center min-h-[300px]\">\n                <Loading />\n                {/* Optionally show original image dimmed while loading */}\n                {/* originalImage && <img src={originalImage} alt=\"Original Uploaded\" className=\"mt-4 max-w-full h-auto rounded opacity-50\" /> */}\n            </div>\n        );\n    }\n\n    // If not loading but no original image, show nothing or a placeholder\n    if (!originalImage) {\n        return null; // Or a placeholder message like <p>Upload an image to see the preview.</p>\n    }\n\n    // If not loading and original image exists, but no enhanced image yet (after upload, before enhancement finishes or if enhancement failed)\n    // We only show the original image in this case.\n    if (originalImage && !enhancedImage && !isLoading) {\n         return (\n             <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl\">\n                <h2 className=\"text-xl font-semibold text-center text-gray-700 dark:text-gray-300 mb-4\">Original Image</h2>\n                <img src={originalImage} alt=\"Original Uploaded\" className=\"max-w-full h-auto rounded mx-auto\" style={{ maxHeight: '60vh' }} />\n            </div>\n         );\n    }\n\n\n    // If both images are available, show the comparison slider\n    if (originalImage && enhancedImage) {\n        // Define common styles to ensure images fit within their container and align\n        const imageStyle = {\n            width: '100%',\n            height: '100%',\n            objectFit: 'contain', // Fit entire image within the container, preserving aspect ratio\n            display: 'block', // Prevents potential extra space below the image\n        };\n\n        return (\n             <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl\">\n                 <h2 className=\"text-xl font-semibold text-center text-gray-700 dark:text-gray-300 mb-4\">Compare Images</h2>\n                 {/* --- Start of ReactCompareSlider code --- */}\n                 <ReactCompareSlider\n                     style={{ height: '70vh', width: '100%', margin: '0 auto' }}\n                    itemOne={\n                        <div style={{ width: '100%', height: '100%', overflow: 'hidden' }}>\n                             <ReactCompareSliderImage\n                                src={originalImage}\n                                alt=\"Original Image\"\n                                style={imageStyle} // Apply styles\n                             />\n                        </div>\n                     }\n                    itemTwo={\n                        <div style={{ width: '100%', height: '100%', overflow: 'hidden' }}>\n                            <ReactCompareSliderImage\n                                src={enhancedImage}\n                                alt=\"Enhanced Image\"\n                                style={imageStyle} // Apply styles\n                             />\n                        </div>\n                     }\n                />\n                {/* --- End of ReactCompareSlider code --- */}\n                 {/* --- EDIT START: Restore the download button --- */}\n                 <div className=\"text-center mt-4\">\n                        <a\n                          href={enhancedImage} // Use the enhanced image URL\n                          download=\"enhanced-image.png\" // Suggest a filename for download\n                          className=\"inline-block bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-bold py-2 px-4 rounded transition duration-300\"\n                        >\n                            Download Enhanced Image\n                        </a>\n                 </div>\n                 {/* --- EDIT END --- */}\n            </div>\n        );\n        // The closing bracket for this block might look duplicated due to the previous comment format, it's correct.\n    }\n\n    // Fallback case (shouldn't normally be reached)\n    return null;\n};\n\nexport default ImagePreview;\n\\n\\n--- File: 14 # src/components/ImageUpload.jsx ---\\nimport React, { useState, useRef } from 'react'\n\nconst ImageUpload = ({ onImageUpload }) => {\n  const [isDragging, setIsDragging] = useState(false)\n  const [fileName, setFileName] = useState('')\n  const fileInputRef = useRef(null)\n\n  const handleDragOver = (e) => {\n    e.preventDefault()\n    setIsDragging(true)\n  }\n\n  const handleDragLeave = () => {\n    setIsDragging(false)\n  }\n\n  const handleDrop = (e) => {\n    e.preventDefault()\n    setIsDragging(false)\n    \n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      const file = e.dataTransfer.files[0]\n      processFile(file)\n    }\n  }\n\n  const handleFileChange = (e) => {\n    if (e.target.files && e.target.files[0]) {\n      const file = e.target.files[0]\n      processFile(file)\n    }\n  }\n\n  const processFile = (file) => {\n    if (!file.type.match('image.*')) {\n      alert('Please select an image file')\n      return\n    }\n    \n    setFileName(file.name)\n    onImageUpload(file)\n  }\n\n  const handleClick = () => {\n    fileInputRef.current.click()\n  }\n\n  return (\n    <div className='bg-white dark:bg-gray-800 shadow-lg rounded-2xl w-full max-w-2xl p-6'>\n      <div \n        className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-all cursor-pointer\n          ${isDragging ? 'border-blue-500 bg-blue-50 dark:bg-blue-900' : 'border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400'}`}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onDrop={handleDrop}\n        onClick={handleClick}\n      >\n        <input \n          type=\"file\" \n          id=\"fileInput\" \n          ref={fileInputRef}\n          className='hidden' \n          accept=\"image/*\"\n          onChange={handleFileChange}\n        />\n        \n        <div className=\"flex flex-col items-center justify-center py-4\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-12 w-12 text-gray-400 dark:text-gray-500 mb-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n          </svg>\n          \n          <p className='text-lg font-medium text-gray-600 dark:text-gray-300 mb-1'>\n            {fileName ? `Selected: ${fileName}` : 'Click or drag to upload an image'}\n          </p>\n          <p className='text-sm text-gray-500 dark:text-gray-400'>\n            Supports JPG, PNG, WEBP (Max 10MB)\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport default ImageUpload\n\\n\\n--- File: 16 # src/contexts/ThemeContext.jsx ---\\nimport React, { createContext, useState, useEffect } from 'react';\n\n// Create the context\nexport const ThemeContext = createContext();\n\n// Create the provider component\nexport const ThemeProvider = ({ children }) => {\n  const [theme, setTheme] = useState('light');\n\n  // Function to toggle theme\n  const toggleTheme = () => {\n    setTheme((prevTheme) => (prevTheme === 'light' ? 'dark' : 'light'));\n  };\n\n  // Effect to load theme from localStorage and apply it\n  useEffect(() => {\n    const storedTheme = localStorage.getItem('theme');\n    if (storedTheme) {\n      setTheme(storedTheme);\n    }\n  }, []);\n\n  // Effect to update localStorage and HTML element class when theme changes\n  useEffect(() => {\n    localStorage.setItem('theme', theme);\n    if (theme === 'dark') {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n  }, [theme]);\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\\n\\n--- File: 18 # src/main.jsx ---\\nimport { StrictMode } from 'react'\nimport { createRoot } from 'react-dom/client'\nimport './index.css'\nimport App from './App.jsx'\nimport { ThemeProvider } from './contexts/ThemeContext';\n\ncreateRoot(document.getElementById('root')).render(\n  <StrictMode>\n    <ThemeProvider>\n      <App />\n    </ThemeProvider>\n  </StrictMode>,\n)\n\\n\\n--- File: 19 # src/services/imageEnhancer.js ---\\nimport axios from \"axios\";\n\nconst BASE_URL = \"http://localhost:3001\"; // URL of our backend proxy\nconst MAXIMUM_RETRIES = 20; // Max polling attempts\n\nexport const enhanceImage = async (file) => {\n    try {\n        const taskId = await uploadImage(file);\n        console.log(\"Image Uploaded Successfully, Task ID:\", taskId);\n\n        const enhancedImageData = await PollForEnhancedImage(taskId);\n        console.log(\"Enhanced Image Data:\", enhancedImageData);\n\n        return enhancedImageData;\n    } catch (error) {\n        // Log the error potentially coming from the backend or network issues\n        console.error(\"Error enhancing image:\", error.response?.data?.message || error.message);\n        // Optionally re-throw or return an error indicator to the UI\n        throw error; // Re-throw the error so the component can handle it (e.g., show message)\n    }\n};\n\nconst uploadImage = async (file) => {\n    const formData = new FormData();\n    formData.append(\"image_file\", file);\n\n    // Send image to our backend proxy\n    const { data } = await axios.post(\n        `${BASE_URL}/api/enhance`, // Endpoint on our backend\n        formData,\n        {\n            headers: {\n                // Content-Type is set automatically by browser for FormData\n                // No API Key needed here\n            },\n        }\n    );\n\n    // Our backend returns { taskId: '...' } directly\n    if (!data?.taskId) {\n        throw new Error(\"Failed to upload image! Task ID not received from backend.\");\n    }\n    return data.taskId;\n};\n\nconst PollForEnhancedImage = async (taskId, retries = 0) => {\n    const result = await fetchEnhancedImage(taskId);\n\n    // Check if the task state indicates it's still processing (state 4 means processing)\n    if (result.state === 4) {\n        console.log(`Processing...(${retries}/${MAXIMUM_RETRIES})`);\n\n        if (retries >= MAXIMUM_RETRIES) {\n            throw new Error(\"Max retries reached. Please try again later.\");\n        }\n\n        // wait for 2 second\n        await new Promise((resolve) => setTimeout(resolve, 2000));\n\n        return PollForEnhancedImage(taskId, retries + 1);\n    }\n\n    console.log(\"Enhanced Image URL:\", result);\n    return result;\n};\n\nconst fetchEnhancedImage = async (taskId) => {\n    // Fetch status from our backend proxy\n    const { data } = await axios.get(\n        `${BASE_URL}/api/status/${taskId}`, // Status endpoint on our backend\n        {\n            // No headers needed here\n        }\n    );\n    // Backend forwards the external API's data structure, which might be directly the data object\n    // or nested under 'data'. Adjust based on backend's response structure.\n    // Assuming backend returns the external API's data directly:\n    if (!data) { // Check if data object itself exists\n        throw new Error(\"Failed to fetch enhanced image status from backend!\");\n    }\n    // The polling logic expects the object containing 'state', etc.\n    return data;\n};\n\\n\\n--- File: 20 # tailwind.config.js ---\\n/** @type {import('tailwindcss').Config} */\nexport default {\n  content: [\n    \"./index.html\",\n    \"./src/**/*.{js,ts,jsx,tsx}\",\n  ],\n  theme: {\n    extend: {},\n  },\n  plugins: [],\n  darkMode: 'class',\n}\n\n\nPlease provide:\n1. A high-level `summary` of the project's main purpose and functionality in a few beginner-friendly sentences. Use markdown formatting with **bold** and *italic* text to highlight important concepts.\n2. A list (`relationships`) describing the key interactions between these abstractions. For each relationship, specify:\n    - `from_abstraction`: Index of the source abstraction (e.g., `0 # AbstractionName1`)\n    - `to_abstraction`: Index of the target abstraction (e.g., `1 # AbstractionName2`)\n    - `label`: A brief label for the interaction **in just a few words** (e.g., \"Manages\", \"Inherits\", \"Uses\").\n    Ideally the relationship should be backed by one abstraction calling or passing parameters to another.\n    Simplify the relationship and exclude those non-important ones.\n\nIMPORTANT: Make sure EVERY abstraction is involved in at least ONE relationship (either as source or target). Each abstraction index must appear at least once across all relationships.\n\nFormat the output as YAML:\n\n```yaml\nsummary: |\n  A brief, simple explanation of the project.\n  Can span multiple lines with **bold** and *italic* for emphasis.\nrelationships:\n  - from_abstraction: 0 # AbstractionName1\n    to_abstraction: 1 # AbstractionName2\n    label: \"Manages\"\n  - from_abstraction: 2 # AbstractionName3\n    to_abstraction: 0 # AbstractionName1\n    label: \"Provides config\"\n  # ... other relationships\n```\n\nNow, provide the YAML output:\n": "```yaml\nsummary: |\n  This project, `Simple-AI-Image-Enhancer`, is a user-friendly web application designed to **automatically improve your photos** using artificial intelligence. You can *easily upload an image*, watch as the AI works its magic behind the scenes, and then *compare the original with the enhanced version* side-by-side. It also lets you switch between light and dark modes for a comfortable viewing experience.\nrelationships:\n  - from_abstraction: 0 # Frontend Application Core\n    to_abstraction: 3 # Image Upload Interface\n    label: \"Uses Upload UI\"\n  - from_abstraction: 3 # Image Upload Interface\n    to_abstraction: 0 # Frontend Application Core\n    label: \"Sends Uploaded File\"\n  - from_abstraction: 0 # Frontend Application Core\n    to_abstraction: 2 # Image Enhancement Client Service\n    label: \"Triggers Enhancement\"\n  - from_abstraction: 0 # Frontend Application Core\n    to_abstraction: 4 # Image Preview & Comparison Interface\n    label: \"Manages Display\"\n  - from_abstraction: 2 # Image Enhancement Client Service\n    to_abstraction: 1 # Backend Proxy & API Gateway\n    label: \"Calls API\"\n  - from_abstraction: 1 # Backend Proxy & API Gateway\n    to_abstraction: 2 # Image Enhancement Client Service\n    label: \"Serves Requests\"\n  - from_abstraction: 0 # Frontend Application Core\n    to_abstraction: 5 # Theme Management System\n    label: \"Utilizes Theme\"\n  - from_abstraction: 5 # Theme Management System\n    to_abstraction: 0 # Frontend Application Core\n    label: \"Controls UI Theme\"\n```", "\nGiven the following project abstractions and their relationships for the project ```` Simple-AI-Image-Enhancer ````:\n\nAbstractions (Index # Name):\n- 0 # Frontend Application Core\n\n- 1 # Backend Proxy & API Gateway\n\n- 2 # Image Enhancement Client Service\n\n- 3 # Image Upload Interface\n\n- 4 # Image Preview & Comparison Interface\n\n- 5 # Theme Management System\n\n\nContext about relationships and project summary:\nProject Summary:\nThis project, `Simple-AI-Image-Enhancer`, is a user-friendly web application designed to **automatically improve your photos** using artificial intelligence. You can *easily upload an image*, watch as the AI works its magic behind the scenes, and then *compare the original with the enhanced version* side-by-side. It also lets you switch between light and dark modes for a comfortable viewing experience.\n\n\nRelationships (Indices refer to abstractions above):\n- From 0 (Frontend Application Core\n) to 3 (Image Upload Interface\n): Uses Upload UI\n- From 3 (Image Upload Interface\n) to 0 (Frontend Application Core\n): Sends Uploaded File\n- From 0 (Frontend Application Core\n) to 2 (Image Enhancement Client Service\n): Triggers Enhancement\n- From 0 (Frontend Application Core\n) to 4 (Image Preview & Comparison Interface\n): Manages Display\n- From 2 (Image Enhancement Client Service\n) to 1 (Backend Proxy & API Gateway\n): Calls API\n- From 1 (Backend Proxy & API Gateway\n) to 2 (Image Enhancement Client Service\n): Serves Requests\n- From 0 (Frontend Application Core\n) to 5 (Theme Management System\n): Utilizes Theme\n- From 5 (Theme Management System\n) to 0 (Frontend Application Core\n): Controls UI Theme\n\n\nIf you are going to make a tutorial for ```` Simple-AI-Image-Enhancer ````, what is the best order to explain these abstractions, from first to last?\nIdeally, first explain those that are the most important or foundational, perhaps user-facing concepts or entry points. Then move to more detailed, lower-level implementation details or supporting concepts.\n\nOutput the ordered list of abstraction indices, including the name in a comment for clarity. Use the format `idx # AbstractionName`.\n\n```yaml\n- 2 # FoundationalConcept\n- 0 # CoreClassA\n- 1 # CoreClassB (uses CoreClassA)\n- ...\n```\n\nNow, provide the YAML output:\n": "```yaml\n- 0 # Frontend Application Core\n- 3 # Image Upload Interface\n- 4 # Image Preview & Comparison Interface\n- 2 # Image Enhancement Client Service\n- 1 # Backend Proxy & API Gateway\n- 5 # Theme Management System\n```", "\nWrite a very beginner-friendly tutorial chapter (in Markdown format) for the project `Simple-AI-Image-Enhancer` about the concept: \"Frontend Application Core\n\". This is Chapter 1.\n\nConcept Details:\n- Name: Frontend Application Core\n\n- Description:\nThis is the main brain of the user-facing part of the application. It orchestrates how users interact with the image enhancement process, from uploading a photo to displaying the final enhanced version. Think of it as the control center of the whole UI, managing different sections like image upload, preview, and overall layout. It ties together smaller UI components to create the complete user experience.\n\n\nComplete Tutorial Structure:\n1. [Frontend Application Core\n](01_frontend_application_core_.md)\n2. [Image Upload Interface\n](02_image_upload_interface_.md)\n3. [Image Preview & Comparison Interface\n](03_image_preview___comparison_interface_.md)\n4. [Image Enhancement Client Service\n](04_image_enhancement_client_service_.md)\n5. [Backend Proxy & API Gateway\n](05_backend_proxy___api_gateway_.md)\n6. [Theme Management System\n](06_theme_management_system_.md)\n\nContext from previous chapters:\nThis is the first chapter.\n\nRelevant Code Snippets (Code itself remains unchanged):\n--- File: package.json ---\n{\n  \"name\": \"image-enhancer\",\n  \"private\": true,\n  \"version\": \"0.0.0\",\n  \"type\": \"module\",\n  \"scripts\": {\n    \"dev\": \"vite\",\n    \"build\": \"vite build\",\n    \"lint\": \"eslint .\",\n    \"preview\": \"vite preview\"\n  },\n  \"dependencies\": {\n    \"@tailwindcss/vite\": \"^4.0.17\",\n    \"axios\": \"^1.8.4\",\n    \"react\": \"^19.0.0\",\n    \"react-compare-slider\": \"^3.1.0\",\n    \"react-dom\": \"^19.0.0\",\n    \"tailwindcss\": \"^4.0.17\"\n  },\n  \"devDependencies\": {\n    \"@eslint/js\": \"^9.21.0\",\n    \"@types/react\": \"^19.0.10\",\n    \"@types/react-dom\": \"^19.0.4\",\n    \"@vitejs/plugin-react\": \"^4.3.4\",\n    \"eslint\": \"^9.21.0\",\n    \"eslint-plugin-react-hooks\": \"^5.1.0\",\n    \"eslint-plugin-react-refresh\": \"^0.4.19\",\n    \"globals\": \"^15.15.0\",\n    \"vite\": \"^6.2.0\"\n  }\n}\n\n\n--- File: src/App.jsx ---\nimport React, { useContext } from 'react';\nimport Home from './components/Home';\nimport { ThemeContext } from './contexts/ThemeContext';\n\nconst App = () => {\n  const { theme, toggleTheme } = useContext(ThemeContext);\n\n  return (\n    <div className='flex flex-col items-center justify-center min-h-screen bg-gray-200 dark:bg-gray-900 py-8 px-4 relative'>\n      <button\n        onClick={toggleTheme}\n        className=\"absolute top-4 right-4 p-2 rounded-md bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-gray-200\"\n      >\n        {theme === 'light' ? 'Switch to Dark Mode' : 'Switch to Light Mode'}\n      </button>\n      <div className='text-center mb-8'>\n        <h1 className='text-5xl font-bold text-gray-800 dark:text-gray-100 mb-2'>AI Image Enhancer</h1>\n        <p className='text-lg text-gray-500 dark:text-gray-400'>Upload an image and let AI enhance it in seconds.</p>\n      </div>\n      <Home/>\n      <div className='text-lg text-gray-500 dark:text-gray-400 mt-6'>\n        Powered by @Learning-On-Peak\n      </div>\n    </div>\n  );\n};\n\nexport default App;\n\n--- File: src/components/Home.jsx ---\nimport React, { useState } from 'react'\nimport ImageUpload from './ImageUpload'\nimport ImagePreview from './ImagePreview'\nimport { enhanceImage } from '../services/imageEnhancer'\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  const [enhancedImage, setEnhancedImage] = useState(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      const imageUrl = URL.createObjectURL(imageFile)\n      setOriginalImage(imageUrl)\n      \n      setIsLoading(true)\n      setEnhancedImage(null)\n      \n      try {\n        const enhancedImageData = await enhanceImage(imageFile)\n        // Extract the image URL from the response object\n        setEnhancedImage(enhancedImageData.image)\n        setIsLoading(false)\n      } catch (error) {\n        console.log(error);\n        alert('Failed to enhance image. Please try again later.')\n        setIsLoading(false)\n      }\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      <ImageUpload onImageUpload={handleImageUpload} />\n      <ImagePreview \n        originalImage={originalImage} \n        enhancedImage={enhancedImage}\n        isLoading={isLoading}\n      />\n    </div>\n  )\n}\n\nexport default Home\n\n\n--- File: src/main.jsx ---\nimport { StrictMode } from 'react'\nimport { createRoot } from 'react-dom/client'\nimport './index.css'\nimport App from './App.jsx'\nimport { ThemeProvider } from './contexts/ThemeContext';\n\ncreateRoot(document.getElementById('root')).render(\n  <StrictMode>\n    <ThemeProvider>\n      <App />\n    </ThemeProvider>\n  </StrictMode>,\n)\n\n\nInstructions for the chapter (Generate content in English unless specified otherwise):\n- Start with a clear heading (e.g., `# Chapter 1: Frontend Application Core\n`). Use the provided concept name.\n\n- If this is not the first chapter, begin with a brief transition from the previous chapter, referencing it with a proper Markdown link using its name.\n\n- Begin with a high-level motivation explaining what problem this abstraction solves. Start with a central use case as a concrete example. The whole chapter should guide the reader to understand how to solve this use case. Make it very minimal and friendly to beginners.\n\n- If the abstraction is complex, break it down into key concepts. Explain each concept one-by-one in a very beginner-friendly way.\n\n- Explain how to use this abstraction to solve the use case. Give example inputs and outputs for code snippets (if the output isn't values, describe at a high level what will happen).\n\n- Each code block should be BELOW 10 lines! If longer code blocks are needed, break them down into smaller pieces and walk through them one-by-one. Aggresively simplify the code to make it minimal. Use comments to skip non-important implementation details. Each code block should have a beginner friendly explanation right after it.\n\n- Describe the internal implementation to help understand what's under the hood. First provide a non-code or code-light walkthrough on what happens step-by-step when the abstraction is called. It's recommended to use a simple sequenceDiagram with a dummy example - keep it minimal with at most 5 participants to ensure clarity. If participant name has space, use: `participant QP as Query Processing`. .\n\n- Then dive deeper into code for the internal implementation with references to files. Provide example code blocks, but make them similarly simple and beginner-friendly. Explain.\n\n- IMPORTANT: When you need to refer to other core abstractions covered in other chapters, ALWAYS use proper Markdown links like this: [Chapter Title](filename.md). Use the Complete Tutorial Structure above to find the correct filename and the chapter title. Translate the surrounding text.\n\n- Use mermaid diagrams to illustrate complex concepts (```mermaid``` format). .\n\n- Heavily use analogies and examples throughout to help beginners understand.\n\n- End the chapter with a brief conclusion that summarizes what was learned and provides a transition to the next chapter. If there is a next chapter, use a proper Markdown link: [Next Chapter Title](next_chapter_filename).\n\n- Ensure the tone is welcoming and easy for a newcomer to understand.\n\n- Output *only* the Markdown content for this chapter.\n\nNow, directly provide a super beginner-friendly Markdown output (DON'T need ```markdown``` tags):\n": "# Chapter 1: Frontend Application Core\n\nWelcome to the first chapter of our tutorial for the `Simple-AI-Image-Enhancer` project! In this chapter, we're going to uncover the \"brain\" of our application's user-facing side, which we call the **Frontend Application Core**.\n\n### What is the Frontend Application Core?\n\nImagine you're building a house. You don't just throw bricks and wood together; you need a blueprint, a general contractor, and a clear plan for how everything fits. The \"Frontend Application Core\" is like that general contractor for our AI Image Enhancer app.\n\nIts main job is to **orchestrate** how you, the user, interact with the application. From the moment you open the app, to uploading your photo, seeing it processed, and finally viewing the enhanced version – the Frontend Application Core is in charge of managing this entire journey.\n\n**Think of it as the control center of the whole user interface.** It makes sure that different parts of the app, like the \"upload photo\" area or the \"show enhanced image\" section, work together smoothly to give you a complete and easy-to-use experience.\n\n### Our Goal: Understanding the Enhancement Flow\n\nOur main goal in this chapter is to understand how the app guides you through the core process of enhancing an image. Specifically, we want to know how the app takes your raw input (an unenhanced image) and prepares it for enhancement, ultimately displaying the result.\n\nHere's the simple user journey we'll explore:\n1.  You open the app.\n2.  You see a place to upload an image.\n3.  You select an image file from your computer.\n4.  The app displays your original image.\n5.  After some processing, the app displays the *enhanced* version of your image.\n\nHow does our \"Frontend Application Core\" manage all these steps? Let's dive in!\n\n### The Building Blocks: `main.jsx`, `App.jsx`, and `Home.jsx`\n\nOur application's core functionality is primarily handled by three key files:\n\n1.  **`src/main.jsx`**: This is the very first file that runs when our application starts. It's like the \"power button\" for our React app.\n2.  **`src/App.jsx`**: This file represents the top-level part of our application. Think of it as the \"main house\" or the overall container that holds everything together. It sets up the basic layout and includes other major parts.\n3.  **`src/components/Home.jsx`**: This is a key \"room\" within our \"main house\" specifically dedicated to the image enhancement process. It manages the steps for uploading, displaying, and triggering the enhancement.\n\nLet's look at each of them.\n\n#### 1. `src/main.jsx`: The Starting Line\n\nThis file is responsible for \"bootstrapping\" our React application. It tells the web browser where to \"mount\" or display our app.\n\n```javascript\n// src/main.jsx\nimport { StrictMode } from 'react'\nimport { createRoot } from 'react-dom/client'\nimport './index.css' // Basic styles\nimport App from './App.jsx' // Our main application component\nimport { ThemeProvider } from './contexts/ThemeContext'; // Theme settings\n\ncreateRoot(document.getElementById('root')).render(\n  <StrictMode>\n    <ThemeProvider>\n      <App /> {/* This is where our entire app starts! */}\n    </ThemeProvider>\n  </StrictMode>,\n)\n```\n\nIn this code, `createRoot(document.getElementById('root'))` finds a special spot in our web page (an HTML element with the ID `root`) and prepares it to display our React app. Then, `.render(<App />)` tells React to put our entire `App` component inside that spot. Notice how `App` is wrapped in `ThemeProvider` – this helps us manage light and dark modes across the app, which we'll cover in [Theme Management System](06_theme_management_system_.md).\n\n#### 2. `src/App.jsx`: The Main Stage\n\n`App.jsx` is where the overall structure and feel of our application are set. It's like the main stage where all the action happens. It defines the title of our app and includes the `Home` component, which is where the main image enhancement features live.\n\n```javascript\n// src/App.jsx\nimport React, { useContext } from 'react';\nimport Home from './components/Home'; // Our core enhancement component\nimport { ThemeContext } from './contexts/ThemeContext'; // For theme switching\n\nconst App = () => {\n  const { theme, toggleTheme } = useContext(ThemeContext);\n\n  return (\n    <div className='flex flex-col items-center ...'> {/* Overall layout */}\n      {/* ... Theme button and header ... */}\n      <h1 className='text-5xl font-bold ...'>AI Image Enhancer</h1>\n      <p className='text-lg text-gray-500 ...'>Upload an image and let AI enhance it in seconds.</p>\n      <Home/> {/* This is where the core functionality resides! */}\n      {/* ... Footer ... */}\n    </div>\n  );\n};\n\nexport default App;\n```\n\nHere, `App.jsx` is mainly responsible for the very top-level appearance and for including the `Home` component. It also contains the theme switching button, which is related to our [Theme Management System](06_theme_management_system_.md). The key takeaway here is that `App` brings in `Home` to do the actual image enhancement work.\n\n#### 3. `src/components/Home.jsx`: The Orchestrator\n\nThis is where the magic of orchestrating the image enhancement process truly happens. `Home.jsx` acts as the central control for managing the original image, the enhanced image, and the loading state. It connects the \"upload\" part with the \"preview\" part.\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\nimport ImageUpload from './ImageUpload' // Component to handle uploads\nimport ImagePreview from './ImagePreview' // Component to display images\nimport { enhanceImage } from '../services/imageEnhancer' // Service for enhancement\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  const [enhancedImage, setEnhancedImage] = useState(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      // ... logic to prepare and send image for enhancement ...\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      <ImageUpload onImageUpload={handleImageUpload} /> {/* Upload area */}\n      <ImagePreview \n        originalImage={originalImage} \n        enhancedImage={enhancedImage}\n        isLoading={isLoading}\n      /> {/* Preview area */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\n`Home.jsx` is critical because it:\n*   Uses `useState` to keep track of the `originalImage` (what you uploaded), `enhancedImage` (the result from the AI), and `isLoading` (to show if the app is busy).\n*   Defines `handleImageUpload`, a function that will be called when you select an image. This function is responsible for sending the image to the enhancement service and updating the state with the results.\n*   Includes two other important components:\n    *   `ImageUpload`: This component provides the user interface for selecting an image. We'll explore it in detail in [Image Upload Interface](02_image_upload_interface_.md).\n    *   `ImagePreview`: This component displays both the original and enhanced images. We'll learn more about it in [Image Preview & Comparison Interface](03_image_preview___comparison_interface_.md).\n\n### The Frontend Core in Action: A Simple Flow\n\nLet's visualize how these pieces work together when you upload an image:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant Main as main.jsx\n    participant App as App.jsx\n    participant Home as Home.jsx\n    participant ImageUpload as ImageUpload.jsx\n    participant ImagePreview as ImagePreview.jsx\n\n    User->>Main: Launches App\n    Main->>App: Renders App component\n    App->>Home: Renders Home component (the core enhancement logic)\n    Home->>ImageUpload: Renders Image Upload Interface\n    Home->>ImagePreview: Renders Image Preview Interface\n\n    User->>ImageUpload: Selects an image file\n    ImageUpload->>Home: Calls onImageUpload(imageFile)\n    Home->>Home: Updates originalImage state\n    Home->>Home: Sets isLoading to true\n    Home->>ImagePreview: Passes updated states (originalImage, isLoading)\n    ImagePreview-->>User: Shows original image & loading indicator\n    Home->>Backend Proxy & API Gateway: Calls enhanceImage(imageFile) (via Image Enhancement Client Service)\n    Backend Proxy & API Gateway-->>Home: Returns enhanced image data\n    Home->>Home: Updates enhancedImage state\n    Home->>Home: Sets isLoading to false\n    Home->>ImagePreview: Passes updated states (originalImage, enhancedImage, isLoading)\n    ImagePreview-->>User: Shows original and enhanced images for comparison\n```\n\n1.  **Start-up:** When you launch the app, `main.jsx` starts `App.jsx`.\n2.  **Overall Layout:** `App.jsx` sets up the basic page layout and includes `Home.jsx`.\n3.  **Core Logic:** `Home.jsx` then sets up the `ImageUpload` area and the `ImagePreview` area.\n4.  **User Uploads:** When you choose an image, the `ImageUpload` component notifies `Home.jsx` about the new image.\n5.  **State Management:** `Home.jsx` takes this original image, saves it, and also sets a \"loading\" flag. It passes these pieces of information down to `ImagePreview` so you can see your original image and know the app is working.\n6.  **Enhancement Request:** `Home.jsx` then uses the `enhanceImage` function (which is part of our [Image Enhancement Client Service](04_image_enhancement_client_service_.md)) to send your image to the AI backend.\n7.  **Result Display:** Once the AI returns the enhanced image, `Home.jsx` updates its state again, including the `enhancedImage` and turning off the \"loading\" flag. This new information is then passed to `ImagePreview` so you can see the amazing transformation!\n\n### Conclusion\n\nIn this chapter, we've explored the \"Frontend Application Core\" of our AI Image Enhancer. We learned that it's the central control system that orchestrates the user's journey, from uploading an image to viewing the enhanced result. We saw how `main.jsx` kicks off the application, how `App.jsx` provides the main stage, and most importantly, how `Home.jsx` acts as the orchestrator, managing the flow and connecting the different parts of the image enhancement process.\n\nNext, we'll dive deeper into one of these connected parts: the **Image Upload Interface**. We'll learn how users can select and upload their photos to our application.\n\n[Next Chapter: Image Upload Interface](02_image_upload_interface_.md)", "\nWrite a very beginner-friendly tutorial chapter (in Markdown format) for the project `Simple-AI-Image-Enhancer` about the concept: \"Image Upload Interface\n\". This is Chapter 2.\n\nConcept Details:\n- Name: Image Upload Interface\n\n- Description:\nThis is the specific part of the user interface where you interact to bring your image into the application. It's like a special \"mailbox\" where you can either click to browse your files or simply drag and drop your image. It ensures that only valid image files are accepted and then prepares them to be sent off for enhancement.\n\n\nComplete Tutorial Structure:\n1. [Frontend Application Core\n](01_frontend_application_core_.md)\n2. [Image Upload Interface\n](02_image_upload_interface_.md)\n3. [Image Preview & Comparison Interface\n](03_image_preview___comparison_interface_.md)\n4. [Image Enhancement Client Service\n](04_image_enhancement_client_service_.md)\n5. [Backend Proxy & API Gateway\n](05_backend_proxy___api_gateway_.md)\n6. [Theme Management System\n](06_theme_management_system_.md)\n\nContext from previous chapters:\n# Chapter 1: Frontend Application Core\n\nWelcome to the first chapter of our tutorial for the `Simple-AI-Image-Enhancer` project! In this chapter, we're going to uncover the \"brain\" of our application's user-facing side, which we call the **Frontend Application Core**.\n\n### What is the Frontend Application Core?\n\nImagine you're building a house. You don't just throw bricks and wood together; you need a blueprint, a general contractor, and a clear plan for how everything fits. The \"Frontend Application Core\" is like that general contractor for our AI Image Enhancer app.\n\nIts main job is to **orchestrate** how you, the user, interact with the application. From the moment you open the app, to uploading your photo, seeing it processed, and finally viewing the enhanced version – the Frontend Application Core is in charge of managing this entire journey.\n\n**Think of it as the control center of the whole user interface.** It makes sure that different parts of the app, like the \"upload photo\" area or the \"show enhanced image\" section, work together smoothly to give you a complete and easy-to-use experience.\n\n### Our Goal: Understanding the Enhancement Flow\n\nOur main goal in this chapter is to understand how the app guides you through the core process of enhancing an image. Specifically, we want to know how the app takes your raw input (an unenhanced image) and prepares it for enhancement, ultimately displaying the result.\n\nHere's the simple user journey we'll explore:\n1.  You open the app.\n2.  You see a place to upload an image.\n3.  You select an image file from your computer.\n4.  The app displays your original image.\n5.  After some processing, the app displays the *enhanced* version of your image.\n\nHow does our \"Frontend Application Core\" manage all these steps? Let's dive in!\n\n### The Building Blocks: `main.jsx`, `App.jsx`, and `Home.jsx`\n\nOur application's core functionality is primarily handled by three key files:\n\n1.  **`src/main.jsx`**: This is the very first file that runs when our application starts. It's like the \"power button\" for our React app.\n2.  **`src/App.jsx`**: This file represents the top-level part of our application. Think of it as the \"main house\" or the overall container that holds everything together. It sets up the basic layout and includes other major parts.\n3.  **`src/components/Home.jsx`**: This is a key \"room\" within our \"main house\" specifically dedicated to the image enhancement process. It manages the steps for uploading, displaying, and triggering the enhancement.\n\nLet's look at each of them.\n\n#### 1. `src/main.jsx`: The Starting Line\n\nThis file is responsible for \"bootstrapping\" our React application. It tells the web browser where to \"mount\" or display our app.\n\n```javascript\n// src/main.jsx\nimport { StrictMode } from 'react'\nimport { createRoot } from 'react-dom/client'\nimport './index.css' // Basic styles\nimport App from './App.jsx' // Our main application component\nimport { ThemeProvider } from './contexts/ThemeContext'; // Theme settings\n\ncreateRoot(document.getElementById('root')).render(\n  <StrictMode>\n    <ThemeProvider>\n      <App /> {/* This is where our entire app starts! */}\n    </ThemeProvider>\n  </StrictMode>,\n)\n```\n\nIn this code, `createRoot(document.getElementById('root'))` finds a special spot in our web page (an HTML element with the ID `root`) and prepares it to display our React app. Then, `.render(<App />)` tells React to put our entire `App` component inside that spot. Notice how `App` is wrapped in `ThemeProvider` – this helps us manage light and dark modes across the app, which we'll cover in [Theme Management System](06_theme_management_system_.md).\n\n#### 2. `src/App.jsx`: The Main Stage\n\n`App.jsx` is where the overall structure and feel of our application are set. It's like the main stage where all the action happens. It defines the title of our app and includes the `Home` component, which is where the main image enhancement features live.\n\n```javascript\n// src/App.jsx\nimport React, { useContext } from 'react';\nimport Home from './components/Home'; // Our core enhancement component\nimport { ThemeContext } from './contexts/ThemeContext'; // For theme switching\n\nconst App = () => {\n  const { theme, toggleTheme } = useContext(ThemeContext);\n\n  return (\n    <div className='flex flex-col items-center ...'> {/* Overall layout */}\n      {/* ... Theme button and header ... */}\n      <h1 className='text-5xl font-bold ...'>AI Image Enhancer</h1>\n      <p className='text-lg text-gray-500 ...'>Upload an image and let AI enhance it in seconds.</p>\n      <Home/> {/* This is where the core functionality resides! */}\n      {/* ... Footer ... */}\n    </div>\n  );\n};\n\nexport default App;\n```\n\nHere, `App.jsx` is mainly responsible for the very top-level appearance and for including the `Home` component. It also contains the theme switching button, which is related to our [Theme Management System](06_theme_management_system_.md). The key takeaway here is that `App` brings in `Home` to do the actual image enhancement work.\n\n#### 3. `src/components/Home.jsx`: The Orchestrator\n\nThis is where the magic of orchestrating the image enhancement process truly happens. `Home.jsx` acts as the central control for managing the original image, the enhanced image, and the loading state. It connects the \"upload\" part with the \"preview\" part.\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\nimport ImageUpload from './ImageUpload' // Component to handle uploads\nimport ImagePreview from './ImagePreview' // Component to display images\nimport { enhanceImage } from '../services/imageEnhancer' // Service for enhancement\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  const [enhancedImage, setEnhancedImage] = useState(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      // ... logic to prepare and send image for enhancement ...\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      <ImageUpload onImageUpload={handleImageUpload} /> {/* Upload area */}\n      <ImagePreview \n        originalImage={originalImage} \n        enhancedImage={enhancedImage}\n        isLoading={isLoading}\n      /> {/* Preview area */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\n`Home.jsx` is critical because it:\n*   Uses `useState` to keep track of the `originalImage` (what you uploaded), `enhancedImage` (the result from the AI), and `isLoading` (to show if the app is busy).\n*   Defines `handleImageUpload`, a function that will be called when you select an image. This function is responsible for sending the image to the enhancement service and updating the state with the results.\n*   Includes two other important components:\n    *   `ImageUpload`: This component provides the user interface for selecting an image. We'll explore it in detail in [Image Upload Interface](02_image_upload_interface_.md).\n    *   `ImagePreview`: This component displays both the original and enhanced images. We'll learn more about it in [Image Preview & Comparison Interface](03_image_preview___comparison_interface_.md).\n\n### The Frontend Core in Action: A Simple Flow\n\nLet's visualize how these pieces work together when you upload an image:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant Main as main.jsx\n    participant App as App.jsx\n    participant Home as Home.jsx\n    participant ImageUpload as ImageUpload.jsx\n    participant ImagePreview as ImagePreview.jsx\n\n    User->>Main: Launches App\n    Main->>App: Renders App component\n    App->>Home: Renders Home component (the core enhancement logic)\n    Home->>ImageUpload: Renders Image Upload Interface\n    Home->>ImagePreview: Renders Image Preview Interface\n\n    User->>ImageUpload: Selects an image file\n    ImageUpload->>Home: Calls onImageUpload(imageFile)\n    Home->>Home: Updates originalImage state\n    Home->>Home: Sets isLoading to true\n    Home->>ImagePreview: Passes updated states (originalImage, isLoading)\n    ImagePreview-->>User: Shows original image & loading indicator\n    Home->>Backend Proxy & API Gateway: Calls enhanceImage(imageFile) (via Image Enhancement Client Service)\n    Backend Proxy & API Gateway-->>Home: Returns enhanced image data\n    Home->>Home: Updates enhancedImage state\n    Home->>Home: Sets isLoading to false\n    Home->>ImagePreview: Passes updated states (originalImage, enhancedImage, isLoading)\n    ImagePreview-->>User: Shows original and enhanced images for comparison\n```\n\n1.  **Start-up:** When you launch the app, `main.jsx` starts `App.jsx`.\n2.  **Overall Layout:** `App.jsx` sets up the basic page layout and includes `Home.jsx`.\n3.  **Core Logic:** `Home.jsx` then sets up the `ImageUpload` area and the `ImagePreview` area.\n4.  **User Uploads:** When you choose an image, the `ImageUpload` component notifies `Home.jsx` about the new image.\n5.  **State Management:** `Home.jsx` takes this original image, saves it, and also sets a \"loading\" flag. It passes these pieces of information down to `ImagePreview` so you can see your original image and know the app is working.\n6.  **Enhancement Request:** `Home.jsx` then uses the `enhanceImage` function (which is part of our [Image Enhancement Client Service](04_image_enhancement_client_service_.md)) to send your image to the AI backend.\n7.  **Result Display:** Once the AI returns the enhanced image, `Home.jsx` updates its state again, including the `enhancedImage` and turning off the \"loading\" flag. This new information is then passed to `ImagePreview` so you can see the amazing transformation!\n\n### Conclusion\n\nIn this chapter, we've explored the \"Frontend Application Core\" of our AI Image Enhancer. We learned that it's the central control system that orchestrates the user's journey, from uploading an image to viewing the enhanced result. We saw how `main.jsx` kicks off the application, how `App.jsx` provides the main stage, and most importantly, how `Home.jsx` acts as the orchestrator, managing the flow and connecting the different parts of the image enhancement process.\n\nNext, we'll dive deeper into one of these connected parts: the **Image Upload Interface**. We'll learn how users can select and upload their photos to our application.\n\n[Next Chapter: Image Upload Interface](02_image_upload_interface_.md)\n\nRelevant Code Snippets (Code itself remains unchanged):\n--- File: src/components/ImageUpload.jsx ---\nimport React, { useState, useRef } from 'react'\n\nconst ImageUpload = ({ onImageUpload }) => {\n  const [isDragging, setIsDragging] = useState(false)\n  const [fileName, setFileName] = useState('')\n  const fileInputRef = useRef(null)\n\n  const handleDragOver = (e) => {\n    e.preventDefault()\n    setIsDragging(true)\n  }\n\n  const handleDragLeave = () => {\n    setIsDragging(false)\n  }\n\n  const handleDrop = (e) => {\n    e.preventDefault()\n    setIsDragging(false)\n    \n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      const file = e.dataTransfer.files[0]\n      processFile(file)\n    }\n  }\n\n  const handleFileChange = (e) => {\n    if (e.target.files && e.target.files[0]) {\n      const file = e.target.files[0]\n      processFile(file)\n    }\n  }\n\n  const processFile = (file) => {\n    if (!file.type.match('image.*')) {\n      alert('Please select an image file')\n      return\n    }\n    \n    setFileName(file.name)\n    onImageUpload(file)\n  }\n\n  const handleClick = () => {\n    fileInputRef.current.click()\n  }\n\n  return (\n    <div className='bg-white dark:bg-gray-800 shadow-lg rounded-2xl w-full max-w-2xl p-6'>\n      <div \n        className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-all cursor-pointer\n          ${isDragging ? 'border-blue-500 bg-blue-50 dark:bg-blue-900' : 'border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400'}`}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onDrop={handleDrop}\n        onClick={handleClick}\n      >\n        <input \n          type=\"file\" \n          id=\"fileInput\" \n          ref={fileInputRef}\n          className='hidden' \n          accept=\"image/*\"\n          onChange={handleFileChange}\n        />\n        \n        <div className=\"flex flex-col items-center justify-center py-4\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-12 w-12 text-gray-400 dark:text-gray-500 mb-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n          </svg>\n          \n          <p className='text-lg font-medium text-gray-600 dark:text-gray-300 mb-1'>\n            {fileName ? `Selected: ${fileName}` : 'Click or drag to upload an image'}\n          </p>\n          <p className='text-sm text-gray-500 dark:text-gray-400'>\n            Supports JPG, PNG, WEBP (Max 10MB)\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport default ImageUpload\n\n\nInstructions for the chapter (Generate content in English unless specified otherwise):\n- Start with a clear heading (e.g., `# Chapter 2: Image Upload Interface\n`). Use the provided concept name.\n\n- If this is not the first chapter, begin with a brief transition from the previous chapter, referencing it with a proper Markdown link using its name.\n\n- Begin with a high-level motivation explaining what problem this abstraction solves. Start with a central use case as a concrete example. The whole chapter should guide the reader to understand how to solve this use case. Make it very minimal and friendly to beginners.\n\n- If the abstraction is complex, break it down into key concepts. Explain each concept one-by-one in a very beginner-friendly way.\n\n- Explain how to use this abstraction to solve the use case. Give example inputs and outputs for code snippets (if the output isn't values, describe at a high level what will happen).\n\n- Each code block should be BELOW 10 lines! If longer code blocks are needed, break them down into smaller pieces and walk through them one-by-one. Aggresively simplify the code to make it minimal. Use comments to skip non-important implementation details. Each code block should have a beginner friendly explanation right after it.\n\n- Describe the internal implementation to help understand what's under the hood. First provide a non-code or code-light walkthrough on what happens step-by-step when the abstraction is called. It's recommended to use a simple sequenceDiagram with a dummy example - keep it minimal with at most 5 participants to ensure clarity. If participant name has space, use: `participant QP as Query Processing`. .\n\n- Then dive deeper into code for the internal implementation with references to files. Provide example code blocks, but make them similarly simple and beginner-friendly. Explain.\n\n- IMPORTANT: When you need to refer to other core abstractions covered in other chapters, ALWAYS use proper Markdown links like this: [Chapter Title](filename.md). Use the Complete Tutorial Structure above to find the correct filename and the chapter title. Translate the surrounding text.\n\n- Use mermaid diagrams to illustrate complex concepts (```mermaid``` format). .\n\n- Heavily use analogies and examples throughout to help beginners understand.\n\n- End the chapter with a brief conclusion that summarizes what was learned and provides a transition to the next chapter. If there is a next chapter, use a proper Markdown link: [Next Chapter Title](next_chapter_filename).\n\n- Ensure the tone is welcoming and easy for a newcomer to understand.\n\n- Output *only* the Markdown content for this chapter.\n\nNow, directly provide a super beginner-friendly Markdown output (DON'T need ```markdown``` tags):\n": "# Chapter 2: Image Upload Interface\n\nIn our previous chapter, [Frontend Application Core](01_frontend_application_core_.md), we learned that the `Home.jsx` component is the central \"orchestrator\" for managing the image enhancement process. It's like the conductor of an orchestra, making sure every part plays its role. One of the very first instruments in this orchestra is the **Image Upload Interface**.\n\n### What is the Image Upload Interface?\n\nImagine you want to send a letter. You don't just throw it into the air and hope it reaches its destination! You need a mailbox. In our `Simple-AI-Image-Enhancer` application, the **Image Upload Interface** is that special \"mailbox\" for your photos.\n\nThis is the specific part of the user interface where you interact to bring your image into the application. It's designed to be super easy to use:\n\n*   **Click to Browse:** You can simply click on the area, and a window will pop up, allowing you to select an image file from your computer, just like opening a document.\n*   **Drag and Drop:** Or, even simpler, you can just drag an image file directly from your computer's folders and \"drop\" it onto this area in the app.\n\nThe Image Upload Interface also has a smart guard dog: it makes sure that only valid image files (like JPGs or PNGs) are accepted. Once your image is safely inside, it then prepares it to be sent off for enhancement.\n\n### Our Goal: Bringing Your Image In\n\nOur main goal in this chapter is to understand how we can get your image file from your computer into our application, specifically into the `Home.jsx` component we talked about in Chapter 1.\n\nRecall from [Frontend Application Core](01_frontend_application_core_.md) that `Home.jsx` includes `ImageUpload` like this:\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\nimport ImageUpload from './ImageUpload' // Component to handle uploads\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  // ... other states ...\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      setOriginalImage(imageFile) // Save the image received from ImageUpload\n      // ... logic to send for enhancement ...\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      <ImageUpload onImageUpload={handleImageUpload} /> {/* This is our upload area! */}\n      {/* ... ImagePreview ... */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\nNotice the line `<ImageUpload onImageUpload={handleImageUpload} />`. This shows us that `Home.jsx` gives `ImageUpload` a special function called `handleImageUpload`. This is how `ImageUpload` will \"mail\" the selected image back to `Home.jsx`.\n\n### How It Works: The Journey of Your Image\n\nLet's trace what happens when you interact with the Image Upload Interface:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant ImageUpload as ImageUpload.jsx\n    participant Home as Home.jsx\n\n    User->>ImageUpload: Clicks or Drags & Drops image file\n    ImageUpload->>ImageUpload: Checks if it's a valid image (e.g., JPG, PNG)\n    alt If not a valid image\n        ImageUpload-->>User: Shows an \"Oops!\" message\n    else If valid image\n        ImageUpload->>Home: Calls onImageUpload(imageFile)\n        Home->>Home: Receives imageFile and prepares it for enhancement\n    end\n```\n\n1.  **You Interact:** You either click the upload area or drag an image file onto it.\n2.  **ImageUpload Catches It:** The `ImageUpload` component, which is the blueprint for our \"mailbox,\" \"catches\" the file.\n3.  **The Guard Dog Checks:** It immediately performs a quick check: \"Is this actually an image file?\" If it's not (e.g., you accidentally dropped a text file), it gently tells you.\n4.  **Delivering the Mail:** If it *is* a valid image, `ImageUpload` doesn't keep it. Instead, it takes that image file and \"mails\" it back to `Home.jsx` by calling the `onImageUpload` function that `Home.jsx` gave it.\n5.  **Home.jsx Takes Over:** Now `Home.jsx` has your image, ready to start the enhancement process!\n\n### Diving into the Code: `src/components/ImageUpload.jsx`\n\nLet's open up the `src/components/ImageUpload.jsx` file to see how this \"mailbox\" is built.\n\n#### 1. Remembering What's Happening with `useState`\n\nFirst, `ImageUpload.jsx` needs to remember a few things, like if you're currently dragging a file or what file you've selected. It uses `useState` for this.\n\n```javascript\n// src/components/ImageUpload.jsx\nimport React, { useState, useRef } from 'react'\n\nconst ImageUpload = ({ onImageUpload }) => {\n  const [isDragging, setIsDragging] = useState(false) // Is a file being dragged over the area?\n  const [fileName, setFileName] = useState('')       // What's the name of the file selected?\n  const fileInputRef = useRef(null)                   // A secret way to click a hidden file input\n\n  // ... rest of the component's code ...\n}\nexport default ImageUpload\n```\n\n*Explanation*: `useState` helps our component remember information that might change (like if `isDragging` is true or false). `useRef` is like having a direct \"handle\" to an actual part of the web page (in our case, a hidden file selector), so we can tell it to do things, like \"click yourself!\"\n\n#### 2. Handling Drag-and-Drop\n\nWhen you drag a file, your browser has its own default behavior (like trying to open the file). We need to stop that and tell our component what to do instead.\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component)\nconst handleDragOver = (e) => {\n  e.preventDefault() // Stop the browser from doing its own thing\n  setIsDragging(true) // Turn on the \"dragging\" style (e.g., blue border)\n}\n\nconst handleDragLeave = () => {\n  setIsDragging(false) // Turn off the \"dragging\" style\n}\n\nconst handleDrop = (e) => {\n  e.preventDefault() // Stop the browser\n  setIsDragging(false) // Turn off the \"dragging\" style\n  \n  if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n    const file = e.dataTransfer.files[0] // Get the dropped file\n    processFile(file) // Send it to our \"guard dog\" function\n  }\n}\n// ... rest of the component ...\n```\n\n*Explanation*: These three functions handle the drag-and-drop actions. `handleDragOver` and `handleDragLeave` simply change the look of our upload box to give you feedback (like making the border blue). When you finally `handleDrop` the file, we grab the file and send it to `processFile` to check if it's an image.\n\n#### 3. Handling Click-to-Upload\n\nNot everyone likes dragging and dropping! We also provide a way to click and browse your files.\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component)\nconst handleFileChange = (e) => {\n  if (e.target.files && e.target.files[0]) {\n    const file = e.target.files[0] // Get the selected file\n    processFile(file) // Send it to our \"guard dog\" function\n  }\n}\n\nconst handleClick = () => {\n  fileInputRef.current.click() // Secretly \"click\" the hidden file selection box\n}\n// ... rest of the component ...\n```\n\n*Explanation*: When you click anywhere on our upload box, `handleClick` makes the hidden file input (which normally opens the file selection window) pop up. Once you choose a file from that window, `handleFileChange` is activated, which then passes your chosen file to `processFile`.\n\n#### 4. The \"Guard Dog\" and Mailman: `processFile`\n\nThis is a crucial function that acts as both the validator and the delivery person.\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component)\nconst processFile = (file) => {\n  // Is it an image?\n  if (!file.type.match('image.*')) { // Checks if the file type starts with 'image/'\n    alert('Oops! Please select an image file (like JPG, PNG, WEBP).')\n    return // Stop right here if it's not an image\n  }\n  \n  setFileName(file.name) // Display the file's name in the UI\n  onImageUpload(file)    // This is where we \"mail\" the file back to Home.jsx!\n}\n// ... rest of the component ...\n```\n\n*Explanation*: `processFile` first checks if the file's `type` matches an image format. If not, it shows an alert. If it *is* an image, it updates the `fileName` to be displayed and then, most importantly, calls `onImageUpload(file)`. This is the exact moment the `ImageUpload` component hands off the selected image file to `Home.jsx` for further processing.\n\n#### 5. The Visual \"Mailbox\": JSX Structure\n\nFinally, all these pieces come together in the visual part of the component, which uses JSX (a mix of JavaScript and HTML-like tags).\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component's return statement)\nreturn (\n  <div className='bg-white dark:bg-gray-800 shadow-lg rounded-2xl w-full max-w-2xl p-6'>\n    <div \n      className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-all cursor-pointer\n        ${isDragging ? 'border-blue-500 bg-blue-50 dark:bg-blue-900' : 'border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400'}`}\n      onDragOver={handleDragOver}   // What happens when dragging over\n      onDragLeave={handleDragLeave} // What happens when dragging leaves\n      onDrop={handleDrop}           // What happens when dropped\n      onClick={handleClick}         // What happens when clicked\n    >\n      <input \n        type=\"file\" \n        id=\"fileInput\" \n        ref={fileInputRef}     // Connects to our secret handle\n        className='hidden'     // Makes the default file input invisible\n        accept=\"image/*\"       // Only allows image files in the selection window\n        onChange={handleFileChange} // What happens when a file is selected\n      />\n      \n      <div className=\"flex flex-col items-center justify-center py-4\">\n        {/* An SVG icon to make it look nice */}\n        <p className='text-lg font-medium'>\n          {fileName ? `Selected: ${fileName}` : 'Click or drag to upload an image'}\n        </p>\n        <p className='text-sm text-gray-500'>\n          Supports JPG, PNG, WEBP (Max 10MB)\n        </p>\n      </div>\n    </div>\n  </div>\n)\n```\n\n*Explanation*: This is what you see! It's a nicely styled `div` (our \"mailbox\" container). It listens for your actions like `onDragOver`, `onDragLeave`, `onDrop`, and `onClick`, connecting them to the functions we just discussed. Inside it, there's a `<input type=\"file\" />` that's kept `hidden` and controlled by our JavaScript. The text dynamically changes to show the `fileName` once an image is selected, making the interface clear and user-friendly.\n\n### Conclusion\n\nIn this chapter, we've explored the **Image Upload Interface**, the crucial first step in our image enhancement journey. We learned how this component acts as a user-friendly \"mailbox,\" allowing you to easily bring your images into the application using either click-to-browse or drag-and-drop. We also saw how it validates files and, most importantly, \"mails\" the selected image back to `Home.jsx` via the `onImageUpload` function.\n\nNow that we know how to get an image *into* the app, the next logical step is to see it! In the next chapter, we'll dive into the **Image Preview & Comparison Interface**, where you'll see your original image and, eventually, its beautifully enhanced counterpart.\n\n[Next Chapter: Image Preview & Comparison Interface](03_image_preview___comparison_interface_.md)", "\nWrite a very beginner-friendly tutorial chapter (in Markdown format) for the project `Simple-AI-Image-Enhancer` about the concept: \"Image Preview & Comparison Interface\n\". This is Chapter 3.\n\nConcept Details:\n- Name: Image Preview & Comparison Interface\n\n- Description:\nOnce an image is uploaded, this component acts as a \"photo album\" that displays your pictures. Initially, it shows your original image. Once enhanced, it transforms into a \"compare tool,\" allowing you to slide a bar across to see the before-and-after versions side-by-side. It also provides an easy way to download the new, improved image.\n\n\nComplete Tutorial Structure:\n1. [Frontend Application Core\n](01_frontend_application_core_.md)\n2. [Image Upload Interface\n](02_image_upload_interface_.md)\n3. [Image Preview & Comparison Interface\n](03_image_preview___comparison_interface_.md)\n4. [Image Enhancement Client Service\n](04_image_enhancement_client_service_.md)\n5. [Backend Proxy & API Gateway\n](05_backend_proxy___api_gateway_.md)\n6. [Theme Management System\n](06_theme_management_system_.md)\n\nContext from previous chapters:\n# Chapter 1: Frontend Application Core\n\nWelcome to the first chapter of our tutorial for the `Simple-AI-Image-Enhancer` project! In this chapter, we're going to uncover the \"brain\" of our application's user-facing side, which we call the **Frontend Application Core**.\n\n### What is the Frontend Application Core?\n\nImagine you're building a house. You don't just throw bricks and wood together; you need a blueprint, a general contractor, and a clear plan for how everything fits. The \"Frontend Application Core\" is like that general contractor for our AI Image Enhancer app.\n\nIts main job is to **orchestrate** how you, the user, interact with the application. From the moment you open the app, to uploading your photo, seeing it processed, and finally viewing the enhanced version – the Frontend Application Core is in charge of managing this entire journey.\n\n**Think of it as the control center of the whole user interface.** It makes sure that different parts of the app, like the \"upload photo\" area or the \"show enhanced image\" section, work together smoothly to give you a complete and easy-to-use experience.\n\n### Our Goal: Understanding the Enhancement Flow\n\nOur main goal in this chapter is to understand how the app guides you through the core process of enhancing an image. Specifically, we want to know how the app takes your raw input (an unenhanced image) and prepares it for enhancement, ultimately displaying the result.\n\nHere's the simple user journey we'll explore:\n1.  You open the app.\n2.  You see a place to upload an image.\n3.  You select an image file from your computer.\n4.  The app displays your original image.\n5.  After some processing, the app displays the *enhanced* version of your image.\n\nHow does our \"Frontend Application Core\" manage all these steps? Let's dive in!\n\n### The Building Blocks: `main.jsx`, `App.jsx`, and `Home.jsx`\n\nOur application's core functionality is primarily handled by three key files:\n\n1.  **`src/main.jsx`**: This is the very first file that runs when our application starts. It's like the \"power button\" for our React app.\n2.  **`src/App.jsx`**: This file represents the top-level part of our application. Think of it as the \"main house\" or the overall container that holds everything together. It sets up the basic layout and includes other major parts.\n3.  **`src/components/Home.jsx`**: This is a key \"room\" within our \"main house\" specifically dedicated to the image enhancement process. It manages the steps for uploading, displaying, and triggering the enhancement.\n\nLet's look at each of them.\n\n#### 1. `src/main.jsx`: The Starting Line\n\nThis file is responsible for \"bootstrapping\" our React application. It tells the web browser where to \"mount\" or display our app.\n\n```javascript\n// src/main.jsx\nimport { StrictMode } from 'react'\nimport { createRoot } from 'react-dom/client'\nimport './index.css' // Basic styles\nimport App from './App.jsx' // Our main application component\nimport { ThemeProvider } from './contexts/ThemeContext'; // Theme settings\n\ncreateRoot(document.getElementById('root')).render(\n  <StrictMode>\n    <ThemeProvider>\n      <App /> {/* This is where our entire app starts! */}\n    </ThemeProvider>\n  </StrictMode>,\n)\n```\n\nIn this code, `createRoot(document.getElementById('root'))` finds a special spot in our web page (an HTML element with the ID `root`) and prepares it to display our React app. Then, `.render(<App />)` tells React to put our entire `App` component inside that spot. Notice how `App` is wrapped in `ThemeProvider` – this helps us manage light and dark modes across the app, which we'll cover in [Theme Management System](06_theme_management_system_.md).\n\n#### 2. `src/App.jsx`: The Main Stage\n\n`App.jsx` is where the overall structure and feel of our application are set. It's like the main stage where all the action happens. It defines the title of our app and includes the `Home` component, which is where the main image enhancement features live.\n\n```javascript\n// src/App.jsx\nimport React, { useContext } from 'react';\nimport Home from './components/Home'; // Our core enhancement component\nimport { ThemeContext } from './contexts/ThemeContext'; // For theme switching\n\nconst App = () => {\n  const { theme, toggleTheme } = useContext(ThemeContext);\n\n  return (\n    <div className='flex flex-col items-center ...'> {/* Overall layout */}\n      {/* ... Theme button and header ... */}\n      <h1 className='text-5xl font-bold ...'>AI Image Enhancer</h1>\n      <p className='text-lg text-gray-500 ...'>Upload an image and let AI enhance it in seconds.</p>\n      <Home/> {/* This is where the core functionality resides! */}\n      {/* ... Footer ... */}\n    </div>\n  );\n};\n\nexport default App;\n```\n\nHere, `App.jsx` is mainly responsible for the very top-level appearance and for including the `Home` component. It also contains the theme switching button, which is related to our [Theme Management System](06_theme_management_system_.md). The key takeaway here is that `App` brings in `Home` to do the actual image enhancement work.\n\n#### 3. `src/components/Home.jsx`: The Orchestrator\n\nThis is where the magic of orchestrating the image enhancement process truly happens. `Home.jsx` acts as the central control for managing the original image, the enhanced image, and the loading state. It connects the \"upload\" part with the \"preview\" part.\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\nimport ImageUpload from './ImageUpload' // Component to handle uploads\nimport ImagePreview from './ImagePreview' // Component to display images\nimport { enhanceImage } from '../services/imageEnhancer' // Service for enhancement\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  const [enhancedImage, setEnhancedImage] = useState(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      // ... logic to prepare and send image for enhancement ...\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      <ImageUpload onImageUpload={handleImageUpload} /> {/* Upload area */}\n      <ImagePreview \n        originalImage={originalImage} \n        enhancedImage={enhancedImage}\n        isLoading={isLoading}\n      /> {/* Preview area */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\n`Home.jsx` is critical because it:\n*   Uses `useState` to keep track of the `originalImage` (what you uploaded), `enhancedImage` (the result from the AI), and `isLoading` (to show if the app is busy).\n*   Defines `handleImageUpload`, a function that will be called when you select an image. This function is responsible for sending the image to the enhancement service and updating the state with the results.\n*   Includes two other important components:\n    *   `ImageUpload`: This component provides the user interface for selecting an image. We'll explore it in detail in [Image Upload Interface](02_image_upload_interface_.md).\n    *   `ImagePreview`: This component displays both the original and enhanced images. We'll learn more about it in [Image Preview & Comparison Interface](03_image_preview___comparison_interface_.md).\n\n### The Frontend Core in Action: A Simple Flow\n\nLet's visualize how these pieces work together when you upload an image:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant Main as main.jsx\n    participant App as App.jsx\n    participant Home as Home.jsx\n    participant ImageUpload as ImageUpload.jsx\n    participant ImagePreview as ImagePreview.jsx\n\n    User->>Main: Launches App\n    Main->>App: Renders App component\n    App->>Home: Renders Home component (the core enhancement logic)\n    Home->>ImageUpload: Renders Image Upload Interface\n    Home->>ImagePreview: Renders Image Preview Interface\n\n    User->>ImageUpload: Selects an image file\n    ImageUpload->>Home: Calls onImageUpload(imageFile)\n    Home->>Home: Updates originalImage state\n    Home->>Home: Sets isLoading to true\n    Home->>ImagePreview: Passes updated states (originalImage, isLoading)\n    ImagePreview-->>User: Shows original image & loading indicator\n    Home->>Backend Proxy & API Gateway: Calls enhanceImage(imageFile) (via Image Enhancement Client Service)\n    Backend Proxy & API Gateway-->>Home: Returns enhanced image data\n    Home->>Home: Updates enhancedImage state\n    Home->>Home: Sets isLoading to false\n    Home->>ImagePreview: Passes updated states (originalImage, enhancedImage, isLoading)\n    ImagePreview-->>User: Shows original and enhanced images for comparison\n```\n\n1.  **Start-up:** When you launch the app, `main.jsx` starts `App.jsx`.\n2.  **Overall Layout:** `App.jsx` sets up the basic page layout and includes `Home.jsx`.\n3.  **Core Logic:** `Home.jsx` then sets up the `ImageUpload` area and the `ImagePreview` area.\n4.  **User Uploads:** When you choose an image, the `ImageUpload` component notifies `Home.jsx` about the new image.\n5.  **State Management:** `Home.jsx` takes this original image, saves it, and also sets a \"loading\" flag. It passes these pieces of information down to `ImagePreview` so you can see your original image and know the app is working.\n6.  **Enhancement Request:** `Home.jsx` then uses the `enhanceImage` function (which is part of our [Image Enhancement Client Service](04_image_enhancement_client_service_.md)) to send your image to the AI backend.\n7.  **Result Display:** Once the AI returns the enhanced image, `Home.jsx` updates its state again, including the `enhancedImage` and turning off the \"loading\" flag. This new information is then passed to `ImagePreview` so you can see the amazing transformation!\n\n### Conclusion\n\nIn this chapter, we've explored the \"Frontend Application Core\" of our AI Image Enhancer. We learned that it's the central control system that orchestrates the user's journey, from uploading an image to viewing the enhanced result. We saw how `main.jsx` kicks off the application, how `App.jsx` provides the main stage, and most importantly, how `Home.jsx` acts as the orchestrator, managing the flow and connecting the different parts of the image enhancement process.\n\nNext, we'll dive deeper into one of these connected parts: the **Image Upload Interface**. We'll learn how users can select and upload their photos to our application.\n\n[Next Chapter: Image Upload Interface](02_image_upload_interface_.md)\n---\n# Chapter 2: Image Upload Interface\n\nIn our previous chapter, [Frontend Application Core](01_frontend_application_core_.md), we learned that the `Home.jsx` component is the central \"orchestrator\" for managing the image enhancement process. It's like the conductor of an orchestra, making sure every part plays its role. One of the very first instruments in this orchestra is the **Image Upload Interface**.\n\n### What is the Image Upload Interface?\n\nImagine you want to send a letter. You don't just throw it into the air and hope it reaches its destination! You need a mailbox. In our `Simple-AI-Image-Enhancer` application, the **Image Upload Interface** is that special \"mailbox\" for your photos.\n\nThis is the specific part of the user interface where you interact to bring your image into the application. It's designed to be super easy to use:\n\n*   **Click to Browse:** You can simply click on the area, and a window will pop up, allowing you to select an image file from your computer, just like opening a document.\n*   **Drag and Drop:** Or, even simpler, you can just drag an image file directly from your computer's folders and \"drop\" it onto this area in the app.\n\nThe Image Upload Interface also has a smart guard dog: it makes sure that only valid image files (like JPGs or PNGs) are accepted. Once your image is safely inside, it then prepares it to be sent off for enhancement.\n\n### Our Goal: Bringing Your Image In\n\nOur main goal in this chapter is to understand how we can get your image file from your computer into our application, specifically into the `Home.jsx` component we talked about in Chapter 1.\n\nRecall from [Frontend Application Core](01_frontend_application_core_.md) that `Home.jsx` includes `ImageUpload` like this:\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\nimport ImageUpload from './ImageUpload' // Component to handle uploads\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  // ... other states ...\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      setOriginalImage(imageFile) // Save the image received from ImageUpload\n      // ... logic to send for enhancement ...\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      <ImageUpload onImageUpload={handleImageUpload} /> {/* This is our upload area! */}\n      {/* ... ImagePreview ... */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\nNotice the line `<ImageUpload onImageUpload={handleImageUpload} />`. This shows us that `Home.jsx` gives `ImageUpload` a special function called `handleImageUpload`. This is how `ImageUpload` will \"mail\" the selected image back to `Home.jsx`.\n\n### How It Works: The Journey of Your Image\n\nLet's trace what happens when you interact with the Image Upload Interface:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant ImageUpload as ImageUpload.jsx\n    participant Home as Home.jsx\n\n    User->>ImageUpload: Clicks or Drags & Drops image file\n    ImageUpload->>ImageUpload: Checks if it's a valid image (e.g., JPG, PNG)\n    alt If not a valid image\n        ImageUpload-->>User: Shows an \"Oops!\" message\n    else If valid image\n        ImageUpload->>Home: Calls onImageUpload(imageFile)\n        Home->>Home: Receives imageFile and prepares it for enhancement\n    end\n```\n\n1.  **You Interact:** You either click the upload area or drag an image file onto it.\n2.  **ImageUpload Catches It:** The `ImageUpload` component, which is the blueprint for our \"mailbox,\" \"catches\" the file.\n3.  **The Guard Dog Checks:** It immediately performs a quick check: \"Is this actually an image file?\" If it's not (e.g., you accidentally dropped a text file), it gently tells you.\n4.  **Delivering the Mail:** If it *is* a valid image, `ImageUpload` doesn't keep it. Instead, it takes that image file and \"mails\" it back to `Home.jsx` by calling the `onImageUpload` function that `Home.jsx` gave it.\n5.  **Home.jsx Takes Over:** Now `Home.jsx` has your image, ready to start the enhancement process!\n\n### Diving into the Code: `src/components/ImageUpload.jsx`\n\nLet's open up the `src/components/ImageUpload.jsx` file to see how this \"mailbox\" is built.\n\n#### 1. Remembering What's Happening with `useState`\n\nFirst, `ImageUpload.jsx` needs to remember a few things, like if you're currently dragging a file or what file you've selected. It uses `useState` for this.\n\n```javascript\n// src/components/ImageUpload.jsx\nimport React, { useState, useRef } from 'react'\n\nconst ImageUpload = ({ onImageUpload }) => {\n  const [isDragging, setIsDragging] = useState(false) // Is a file being dragged over the area?\n  const [fileName, setFileName] = useState('')       // What's the name of the file selected?\n  const fileInputRef = useRef(null)                   // A secret way to click a hidden file input\n\n  // ... rest of the component's code ...\n}\nexport default ImageUpload\n```\n\n*Explanation*: `useState` helps our component remember information that might change (like if `isDragging` is true or false). `useRef` is like having a direct \"handle\" to an actual part of the web page (in our case, a hidden file selector), so we can tell it to do things, like \"click yourself!\"\n\n#### 2. Handling Drag-and-Drop\n\nWhen you drag a file, your browser has its own default behavior (like trying to open the file). We need to stop that and tell our component what to do instead.\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component)\nconst handleDragOver = (e) => {\n  e.preventDefault() // Stop the browser from doing its own thing\n  setIsDragging(true) // Turn on the \"dragging\" style (e.g., blue border)\n}\n\nconst handleDragLeave = () => {\n  setIsDragging(false) // Turn off the \"dragging\" style\n}\n\nconst handleDrop = (e) => {\n  e.preventDefault() // Stop the browser\n  setIsDragging(false) // Turn off the \"dragging\" style\n  \n  if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n    const file = e.dataTransfer.files[0] // Get the dropped file\n    processFile(file) // Send it to our \"guard dog\" function\n  }\n}\n// ... rest of the component ...\n```\n\n*Explanation*: These three functions handle the drag-and-drop actions. `handleDragOver` and `handleDragLeave` simply change the look of our upload box to give you feedback (like making the border blue). When you finally `handleDrop` the file, we grab the file and send it to `processFile` to check if it's an image.\n\n#### 3. Handling Click-to-Upload\n\nNot everyone likes dragging and dropping! We also provide a way to click and browse your files.\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component)\nconst handleFileChange = (e) => {\n  if (e.target.files && e.target.files[0]) {\n    const file = e.target.files[0] // Get the selected file\n    processFile(file) // Send it to our \"guard dog\" function\n  }\n}\n\nconst handleClick = () => {\n  fileInputRef.current.click() // Secretly \"click\" the hidden file selection box\n}\n// ... rest of the component ...\n```\n\n*Explanation*: When you click anywhere on our upload box, `handleClick` makes the hidden file input (which normally opens the file selection window) pop up. Once you choose a file from that window, `handleFileChange` is activated, which then passes your chosen file to `processFile`.\n\n#### 4. The \"Guard Dog\" and Mailman: `processFile`\n\nThis is a crucial function that acts as both the validator and the delivery person.\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component)\nconst processFile = (file) => {\n  // Is it an image?\n  if (!file.type.match('image.*')) { // Checks if the file type starts with 'image/'\n    alert('Oops! Please select an image file (like JPG, PNG, WEBP).')\n    return // Stop right here if it's not an image\n  }\n  \n  setFileName(file.name) // Display the file's name in the UI\n  onImageUpload(file)    // This is where we \"mail\" the file back to Home.jsx!\n}\n// ... rest of the component ...\n```\n\n*Explanation*: `processFile` first checks if the file's `type` matches an image format. If not, it shows an alert. If it *is* an image, it updates the `fileName` to be displayed and then, most importantly, calls `onImageUpload(file)`. This is the exact moment the `ImageUpload` component hands off the selected image file to `Home.jsx` for further processing.\n\n#### 5. The Visual \"Mailbox\": JSX Structure\n\nFinally, all these pieces come together in the visual part of the component, which uses JSX (a mix of JavaScript and HTML-like tags).\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component's return statement)\nreturn (\n  <div className='bg-white dark:bg-gray-800 shadow-lg rounded-2xl w-full max-w-2xl p-6'>\n    <div \n      className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-all cursor-pointer\n        ${isDragging ? 'border-blue-500 bg-blue-50 dark:bg-blue-900' : 'border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400'}`}\n      onDragOver={handleDragOver}   // What happens when dragging over\n      onDragLeave={handleDragLeave} // What happens when dragging leaves\n      onDrop={handleDrop}           // What happens when dropped\n      onClick={handleClick}         // What happens when clicked\n    >\n      <input \n        type=\"file\" \n        id=\"fileInput\" \n        ref={fileInputRef}     // Connects to our secret handle\n        className='hidden'     // Makes the default file input invisible\n        accept=\"image/*\"       // Only allows image files in the selection window\n        onChange={handleFileChange} // What happens when a file is selected\n      />\n      \n      <div className=\"flex flex-col items-center justify-center py-4\">\n        {/* An SVG icon to make it look nice */}\n        <p className='text-lg font-medium'>\n          {fileName ? `Selected: ${fileName}` : 'Click or drag to upload an image'}\n        </p>\n        <p className='text-sm text-gray-500'>\n          Supports JPG, PNG, WEBP (Max 10MB)\n        </p>\n      </div>\n    </div>\n  </div>\n)\n```\n\n*Explanation*: This is what you see! It's a nicely styled `div` (our \"mailbox\" container). It listens for your actions like `onDragOver`, `onDragLeave`, `onDrop`, and `onClick`, connecting them to the functions we just discussed. Inside it, there's a `<input type=\"file\" />` that's kept `hidden` and controlled by our JavaScript. The text dynamically changes to show the `fileName` once an image is selected, making the interface clear and user-friendly.\n\n### Conclusion\n\nIn this chapter, we've explored the **Image Upload Interface**, the crucial first step in our image enhancement journey. We learned how this component acts as a user-friendly \"mailbox,\" allowing you to easily bring your images into the application using either click-to-browse or drag-and-drop. We also saw how it validates files and, most importantly, \"mails\" the selected image back to `Home.jsx` via the `onImageUpload` function.\n\nNow that we know how to get an image *into* the app, the next logical step is to see it! In the next chapter, we'll dive into the **Image Preview & Comparison Interface**, where you'll see your original image and, eventually, its beautifully enhanced counterpart.\n\n[Next Chapter: Image Preview & Comparison Interface](03_image_preview___comparison_interface_.md)\n\nRelevant Code Snippets (Code itself remains unchanged):\n--- File: src/components/ImagePreview.jsx ---\nimport React from 'react';\nimport { ReactCompareSlider, ReactCompareSliderImage } from 'react-compare-slider';\nimport Loading from './Loading';\n\nconst ImagePreview = ({ originalImage, enhancedImage, isLoading }) => {\n    // If loading, show the loading indicator\n    if (isLoading) {\n        return (\n            <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl flex justify-center items-center min-h-[300px]\">\n                <Loading />\n                {/* Optionally show original image dimmed while loading */}\n                {/* originalImage && <img src={originalImage} alt=\"Original Uploaded\" className=\"mt-4 max-w-full h-auto rounded opacity-50\" /> */}\n            </div>\n        );\n    }\n\n    // If not loading but no original image, show nothing or a placeholder\n    if (!originalImage) {\n        return null; // Or a placeholder message like <p>Upload an image to see the preview.</p>\n    }\n\n    // If not loading and original image exists, but no enhanced image yet (after upload, before enhancement finishes or if enhancement failed)\n    // We only show the original image in this case.\n    if (originalImage && !enhancedImage && !isLoading) {\n         return (\n             <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl\">\n                <h2 className=\"text-xl font-semibold text-center text-gray-700 dark:text-gray-300 mb-4\">Original Image</h2>\n                <img src={originalImage} alt=\"Original Uploaded\" className=\"max-w-full h-auto rounded mx-auto\" style={{ maxHeight: '60vh' }} />\n            </div>\n         );\n    }\n\n\n    // If both images are available, show the comparison slider\n    if (originalImage && enhancedImage) {\n        // Define common styles to ensure images fit within their container and align\n        const imageStyle = {\n            width: '100%',\n            height: '100%',\n            objectFit: 'contain', // Fit entire image within the container, preserving aspect ratio\n            display: 'block', // Prevents potential extra space below the image\n        };\n\n        return (\n             <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl\">\n                 <h2 className=\"text-xl font-semibold text-center text-gray-700 dark:text-gray-300 mb-4\">Compare Images</h2>\n                 {/* --- Start of ReactCompareSlider code --- */}\n                 <ReactCompareSlider\n                     style={{ height: '70vh', width: '100%', margin: '0 auto' }}\n                    itemOne={\n                        <div style={{ width: '100%', height: '100%', overflow: 'hidden' }}>\n                             <ReactCompareSliderImage\n                                src={originalImage}\n                                alt=\"Original Image\"\n                                style={imageStyle} // Apply styles\n                             />\n                        </div>\n                     }\n                    itemTwo={\n                        <div style={{ width: '100%', height: '100%', overflow: 'hidden' }}>\n                            <ReactCompareSliderImage\n                                src={enhancedImage}\n                                alt=\"Enhanced Image\"\n                                style={imageStyle} // Apply styles\n                             />\n                        </div>\n                     }\n                />\n                {/* --- End of ReactCompareSlider code --- */}\n                 {/* --- EDIT START: Restore the download button --- */}\n                 <div className=\"text-center mt-4\">\n                        <a\n                          href={enhancedImage} // Use the enhanced image URL\n                          download=\"enhanced-image.png\" // Suggest a filename for download\n                          className=\"inline-block bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-bold py-2 px-4 rounded transition duration-300\"\n                        >\n                            Download Enhanced Image\n                        </a>\n                 </div>\n                 {/* --- EDIT END --- */}\n            </div>\n        );\n        // The closing bracket for this block might look duplicated due to the previous comment format, it's correct.\n    }\n\n    // Fallback case (shouldn't normally be reached)\n    return null;\n};\n\nexport default ImagePreview;\n\n\nInstructions for the chapter (Generate content in English unless specified otherwise):\n- Start with a clear heading (e.g., `# Chapter 3: Image Preview & Comparison Interface\n`). Use the provided concept name.\n\n- If this is not the first chapter, begin with a brief transition from the previous chapter, referencing it with a proper Markdown link using its name.\n\n- Begin with a high-level motivation explaining what problem this abstraction solves. Start with a central use case as a concrete example. The whole chapter should guide the reader to understand how to solve this use case. Make it very minimal and friendly to beginners.\n\n- If the abstraction is complex, break it down into key concepts. Explain each concept one-by-one in a very beginner-friendly way.\n\n- Explain how to use this abstraction to solve the use case. Give example inputs and outputs for code snippets (if the output isn't values, describe at a high level what will happen).\n\n- Each code block should be BELOW 10 lines! If longer code blocks are needed, break them down into smaller pieces and walk through them one-by-one. Aggresively simplify the code to make it minimal. Use comments to skip non-important implementation details. Each code block should have a beginner friendly explanation right after it.\n\n- Describe the internal implementation to help understand what's under the hood. First provide a non-code or code-light walkthrough on what happens step-by-step when the abstraction is called. It's recommended to use a simple sequenceDiagram with a dummy example - keep it minimal with at most 5 participants to ensure clarity. If participant name has space, use: `participant QP as Query Processing`. .\n\n- Then dive deeper into code for the internal implementation with references to files. Provide example code blocks, but make them similarly simple and beginner-friendly. Explain.\n\n- IMPORTANT: When you need to refer to other core abstractions covered in other chapters, ALWAYS use proper Markdown links like this: [Chapter Title](filename.md). Use the Complete Tutorial Structure above to find the correct filename and the chapter title. Translate the surrounding text.\n\n- Use mermaid diagrams to illustrate complex concepts (```mermaid``` format). .\n\n- Heavily use analogies and examples throughout to help beginners understand.\n\n- End the chapter with a brief conclusion that summarizes what was learned and provides a transition to the next chapter. If there is a next chapter, use a proper Markdown link: [Next Chapter Title](next_chapter_filename).\n\n- Ensure the tone is welcoming and easy for a newcomer to understand.\n\n- Output *only* the Markdown content for this chapter.\n\nNow, directly provide a super beginner-friendly Markdown output (DON'T need ```markdown``` tags):\n": "# Chapter 3: Image Preview & Comparison Interface\n\nIn our previous chapter, [Image Upload Interface](02_image_upload_interface.md), we learned how our application provides a user-friendly \"mailbox\" for you to bring your images into the system. Now that your image has been successfully \"mailed\" into the app, where does it go next? It needs a place to be seen!\n\nThis is where the **Image Preview & Comparison Interface** comes in.\n\n### What is the Image Preview & Comparison Interface?\n\nImagine you're looking at a photo album. When you first upload a picture, you want to see that picture immediately. That's the first job of this interface: it acts like a digital \"photo album\" displaying your original image as soon as it's ready.\n\nBut it gets even better! Once our clever AI has worked its magic and enhanced your image, this interface transforms into a super-cool \"compare tool.\" It lets you see your original image right next to the new, improved version. You can even slide a bar across the image to smoothly switch between the \"before\" and \"after\" views, helping you truly appreciate the enhancement.\n\nAnd of course, once you're happy with the result, it gives you an easy way to \"take home\" your newly enhanced masterpiece by providing a convenient download button.\n\n### Our Goal: Showing Off Your Images\n\nOur main goal in this chapter is to understand how the `ImagePreview` component works to display your pictures at different stages of the enhancement process – from just being uploaded, to waiting for AI, and finally, showcasing the amazing before-and-after transformation.\n\nRecall from [Frontend Application Core](01_frontend_application_core.md) that `Home.jsx` is the orchestrator, and it uses `ImagePreview` by passing it important information:\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\n// ... other imports ...\nimport ImagePreview from './ImagePreview' // Our preview component\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  const [enhancedImage, setEnhancedImage] = useState(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  // ... handleImageUpload function (updates originalImage, sets isLoading) ...\n  // ... logic to call enhancement service (updates enhancedImage, sets isLoading false) ...\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      {/* ... ImageUpload component ... */}\n      <ImagePreview \n        originalImage={originalImage} \n        enhancedImage={enhancedImage}\n        isLoading={isLoading}\n      /> {/* This is our preview area! */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\n`Home.jsx` passes three key pieces of information (called \"props\") to `ImagePreview`:\n*   `originalImage`: The image you just uploaded.\n*   `enhancedImage`: The amazing picture after the AI has worked on it.\n*   `isLoading`: A true/false switch that tells `ImagePreview` if the AI is currently busy enhancing your image.\n\n### How It Works: The Image's Display Journey\n\nLet's trace how your image appears and changes in the `ImagePreview` interface:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant Home as Home.jsx\n    participant ImagePreview as ImagePreview.jsx\n\n    User->>Home: Uploads image\n    Home->>ImagePreview: Passes originalImage, isLoading=true\n    ImagePreview-->>User: Shows a \"Please Wait...\" screen (Loading State)\n\n    Home->>Home: Sends image for enhancement (via Image Enhancement Client Service)\n    Home->>Home: Receives enhanced image\n    Home->>ImagePreview: Passes originalImage, enhancedImage, isLoading=false\n\n    alt If original exists, but no enhanced yet (e.g., after initial upload, before enhancement result)\n        ImagePreview-->>User: Shows ONLY the original image (Initial Preview State)\n    else If both original and enhanced images are ready\n        ImagePreview-->>User: Shows the interactive Before/After comparison (Comparison State)\n    end\n```\n\n1.  **Original Arrives, AI is Busy**: As soon as you upload an image, `Home.jsx` sends your `originalImage` to `ImagePreview` and also tells it `isLoading` is `true`. `ImagePreview` immediately puts up a \"Please Wait...\" sign.\n2.  **AI Finishes**: Once the AI processing is done, `Home.jsx` updates `ImagePreview` with the `enhancedImage` and sets `isLoading` to `false`.\n3.  **The Big Reveal**: Now `ImagePreview` has both the original and enhanced images, and it switches from the \"Please Wait...\" screen to the cool comparison tool, letting you slide between them.\n\n### Diving into the Code: `src/components/ImagePreview.jsx`\n\nLet's look inside the `src/components/ImagePreview.jsx` file to see how it manages these different display \"scenarios.\"\n\n#### 1. The Component's \"Brain\": Receiving Information\n\nFirst, the `ImagePreview` component gets the `originalImage`, `enhancedImage`, and `isLoading` information from `Home.jsx`.\n\n```javascript\n// src/components/ImagePreview.jsx\nimport React from 'react';\nimport { ReactCompareSlider, ReactCompareSliderImage } from 'react-compare-slider';\nimport Loading from './Loading'; // A small component to show \"loading\"\n\nconst ImagePreview = ({ originalImage, enhancedImage, isLoading }) => {\n    // This component will change what it shows based on these three pieces of info!\n\n    // ... The rest of the logic (different display scenarios) ...\n};\n\nexport default ImagePreview;\n```\n*Explanation*: The `ImagePreview` component is a \"smart display.\" It listens to the `originalImage`, `enhancedImage`, and `isLoading` signals from `Home.jsx` to decide what to show on your screen.\n\n#### 2. Scenario 1: The \"Please Wait...\" Screen\n\nIf the `isLoading` signal is `true`, it means our AI is busy working its magic. We want to show a friendly loading animation.\n\n```javascript\n// src/components/ImagePreview.jsx (inside the component)\n    if (isLoading) {\n        return (\n            <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl flex justify-center items-center min-h-[300px]\">\n                <Loading /> {/* Our spinning wheel or message */}\n            </div>\n        );\n    }\n```\n*Explanation*: If `isLoading` is `true`, the component immediately stops here and shows a `Loading` animation (which is a separate, simple spinning wheel component). This tells you that the app is busy and you should wait.\n\n#### 3. Scenario 2: The \"First Look\" – Only Original Image\n\nWhat if you've uploaded an image, but the AI hasn't finished enhancing it yet (or perhaps it failed)? In this case, we still want to show your original picture, even if the enhanced one isn't ready.\n\n```javascript\n// src/components/ImagePreview.jsx (inside the component)\n    // If not loading, and original image exists, but no enhanced image yet:\n    if (originalImage && !enhancedImage && !isLoading) {\n         return (\n             <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl\">\n                <h2 className=\"text-xl font-semibold text-center ...\">Original Image</h2>\n                <img \n                    src={originalImage} \n                    alt=\"Original Uploaded\" \n                    className=\"max-w-full h-auto rounded mx-auto\" \n                    style={{ maxHeight: '60vh' }} \n                />\n            </div>\n         );\n    }\n```\n*Explanation*: This block checks if we have an `originalImage`, but `enhancedImage` is `null` (meaning it's not ready yet), and we're not currently `isLoading`. If all these are true, it simply displays your `originalImage` along with a \"Original Image\" title.\n\n#### 4. Scenario 3: The \"Big Reveal\" – Before & After Comparison\n\nThis is the main event! If we have both `originalImage` AND `enhancedImage`, it's time to show them side-by-side using a special comparison tool.\n\n```javascript\n// src/components/ImagePreview.jsx (inside the component)\n    // If both original and enhanced images are available:\n    if (originalImage && enhancedImage) {\n        return (\n             <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl\">\n                 <h2 className=\"text-xl font-semibold text-center ...\">Compare Images</h2>\n                 <ReactCompareSlider\n                     style={{ height: '70vh', width: '100%', margin: '0 auto' }}\n                    itemOne={\n                         <ReactCompareSliderImage src={originalImage} alt=\"Original Image\" />\n                     }\n                    itemTwo={\n                            <ReactCompareSliderImage src={enhancedImage} alt=\"Enhanced Image\" />\n                     }\n                />\n                 <div className=\"text-center mt-4\">\n                        <a\n                          href={enhancedImage} // Link to the enhanced image\n                          download=\"enhanced-image.png\" // Suggested filename\n                          className=\"inline-block bg-blue-500 hover:bg-blue-700 ... \"\n                        >\n                            Download Enhanced Image\n                        </a>\n                 </div>\n            </div>\n        );\n    }\n```\n*Explanation*:\n*   **`ReactCompareSlider`**: This is a powerful little tool (a \"third-party library\") that makes the cool sliding comparison effect. We give it `itemOne` (our `originalImage`) and `itemTwo` (our `enhancedImage`), and it magically creates the slider.\n*   **Download Button**: Below the slider, there's a simple `<a>` (link) tag. The `href` points directly to the `enhancedImage` data, and the `download` attribute tells your browser to save it as `enhanced-image.png` when you click it. It's like pressing \"Save\" on your masterpiece!\n\n#### 5. Fallback: Nothing to Show\n\nFinally, if none of the above conditions are met (e.g., no image has been uploaded yet, and it's not loading), the component simply returns nothing.\n\n```javascript\n// src/components/ImagePreview.jsx (inside the component)\n    // Fallback case (e.g., no images uploaded yet)\n    return null;\n}; // End of the ImagePreview component\n```\n*Explanation*: This ensures that if there's no relevant image data, our `ImagePreview` area remains empty and tidy, waiting for you to upload a picture.\n\n### Conclusion\n\nIn this chapter, we've explored the **Image Preview & Comparison Interface**, the \"photo album\" and \"compare tool\" of our application. We learned how the `ImagePreview` component intelligently displays your images at different stages: showing a loading screen when the AI is busy, displaying just the original image when first uploaded, and finally, transforming into an interactive before-and-after slider with a handy download button once the enhancement is complete.\n\nNow that we know how images are displayed, the next logical step is to understand how the application *requests* the enhancement from the AI. In the next chapter, we'll dive into the **Image Enhancement Client Service**, the part of our app that talks to the powerful AI backend.\n\n[Next Chapter: Image Enhancement Client Service](04_image_enhancement_client_service.md)", "\nWrite a very beginner-friendly tutorial chapter (in Markdown format) for the project `Simple-AI-Image-Enhancer` about the concept: \"Image Enhancement Client Service\n\". This is Chapter 4.\n\nConcept Details:\n- Name: Image Enhancement Client Service\n\n- Description:\nThis is the dedicated \"messenger\" on the frontend side that talks to our backend server to get images enhanced. It handles the full client-side workflow for enhancement: first, it sends your image to the backend, then it repeatedly asks (or \"polls\") the backend for updates until the enhancement is complete and the final enhanced image URL is ready. It abstracts away the complex communication details from the main UI components.\n\n\nComplete Tutorial Structure:\n1. [Frontend Application Core\n](01_frontend_application_core_.md)\n2. [Image Upload Interface\n](02_image_upload_interface_.md)\n3. [Image Preview & Comparison Interface\n](03_image_preview___comparison_interface_.md)\n4. [Image Enhancement Client Service\n](04_image_enhancement_client_service_.md)\n5. [Backend Proxy & API Gateway\n](05_backend_proxy___api_gateway_.md)\n6. [Theme Management System\n](06_theme_management_system_.md)\n\nContext from previous chapters:\n# Chapter 1: Frontend Application Core\n\nWelcome to the first chapter of our tutorial for the `Simple-AI-Image-Enhancer` project! In this chapter, we're going to uncover the \"brain\" of our application's user-facing side, which we call the **Frontend Application Core**.\n\n### What is the Frontend Application Core?\n\nImagine you're building a house. You don't just throw bricks and wood together; you need a blueprint, a general contractor, and a clear plan for how everything fits. The \"Frontend Application Core\" is like that general contractor for our AI Image Enhancer app.\n\nIts main job is to **orchestrate** how you, the user, interact with the application. From the moment you open the app, to uploading your photo, seeing it processed, and finally viewing the enhanced version – the Frontend Application Core is in charge of managing this entire journey.\n\n**Think of it as the control center of the whole user interface.** It makes sure that different parts of the app, like the \"upload photo\" area or the \"show enhanced image\" section, work together smoothly to give you a complete and easy-to-use experience.\n\n### Our Goal: Understanding the Enhancement Flow\n\nOur main goal in this chapter is to understand how the app guides you through the core process of enhancing an image. Specifically, we want to know how the app takes your raw input (an unenhanced image) and prepares it for enhancement, ultimately displaying the result.\n\nHere's the simple user journey we'll explore:\n1.  You open the app.\n2.  You see a place to upload an image.\n3.  You select an image file from your computer.\n4.  The app displays your original image.\n5.  After some processing, the app displays the *enhanced* version of your image.\n\nHow does our \"Frontend Application Core\" manage all these steps? Let's dive in!\n\n### The Building Blocks: `main.jsx`, `App.jsx`, and `Home.jsx`\n\nOur application's core functionality is primarily handled by three key files:\n\n1.  **`src/main.jsx`**: This is the very first file that runs when our application starts. It's like the \"power button\" for our React app.\n2.  **`src/App.jsx`**: This file represents the top-level part of our application. Think of it as the \"main house\" or the overall container that holds everything together. It sets up the basic layout and includes other major parts.\n3.  **`src/components/Home.jsx`**: This is a key \"room\" within our \"main house\" specifically dedicated to the image enhancement process. It manages the steps for uploading, displaying, and triggering the enhancement.\n\nLet's look at each of them.\n\n#### 1. `src/main.jsx`: The Starting Line\n\nThis file is responsible for \"bootstrapping\" our React application. It tells the web browser where to \"mount\" or display our app.\n\n```javascript\n// src/main.jsx\nimport { StrictMode } from 'react'\nimport { createRoot } from 'react-dom/client'\nimport './index.css' // Basic styles\nimport App from './App.jsx' // Our main application component\nimport { ThemeProvider } from './contexts/ThemeContext'; // Theme settings\n\ncreateRoot(document.getElementById('root')).render(\n  <StrictMode>\n    <ThemeProvider>\n      <App /> {/* This is where our entire app starts! */}\n    </ThemeProvider>\n  </StrictMode>,\n)\n```\n\nIn this code, `createRoot(document.getElementById('root'))` finds a special spot in our web page (an HTML element with the ID `root`) and prepares it to display our React app. Then, `.render(<App />)` tells React to put our entire `App` component inside that spot. Notice how `App` is wrapped in `ThemeProvider` – this helps us manage light and dark modes across the app, which we'll cover in [Theme Management System](06_theme_management_system_.md).\n\n#### 2. `src/App.jsx`: The Main Stage\n\n`App.jsx` is where the overall structure and feel of our application are set. It's like the main stage where all the action happens. It defines the title of our app and includes the `Home` component, which is where the main image enhancement features live.\n\n```javascript\n// src/App.jsx\nimport React, { useContext } from 'react';\nimport Home from './components/Home'; // Our core enhancement component\nimport { ThemeContext } from './contexts/ThemeContext'; // For theme switching\n\nconst App = () => {\n  const { theme, toggleTheme } = useContext(ThemeContext);\n\n  return (\n    <div className='flex flex-col items-center ...'> {/* Overall layout */}\n      {/* ... Theme button and header ... */}\n      <h1 className='text-5xl font-bold ...'>AI Image Enhancer</h1>\n      <p className='text-lg text-gray-500 ...'>Upload an image and let AI enhance it in seconds.</p>\n      <Home/> {/* This is where the core functionality resides! */}\n      {/* ... Footer ... */}\n    </div>\n  );\n};\n\nexport default App;\n```\n\nHere, `App.jsx` is mainly responsible for the very top-level appearance and for including the `Home` component. It also contains the theme switching button, which is related to our [Theme Management System](06_theme_management_system_.md). The key takeaway here is that `App` brings in `Home` to do the actual image enhancement work.\n\n#### 3. `src/components/Home.jsx`: The Orchestrator\n\nThis is where the magic of orchestrating the image enhancement process truly happens. `Home.jsx` acts as the central control for managing the original image, the enhanced image, and the loading state. It connects the \"upload\" part with the \"preview\" part.\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\nimport ImageUpload from './ImageUpload' // Component to handle uploads\nimport ImagePreview from './ImagePreview' // Component to display images\nimport { enhanceImage } from '../services/imageEnhancer' // Service for enhancement\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  const [enhancedImage, setEnhancedImage] = useState(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      // ... logic to prepare and send image for enhancement ...\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      <ImageUpload onImageUpload={handleImageUpload} /> {/* Upload area */}\n      <ImagePreview \n        originalImage={originalImage} \n        enhancedImage={enhancedImage}\n        isLoading={isLoading}\n      /> {/* Preview area */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\n`Home.jsx` is critical because it:\n*   Uses `useState` to keep track of the `originalImage` (what you uploaded), `enhancedImage` (the result from the AI), and `isLoading` (to show if the app is busy).\n*   Defines `handleImageUpload`, a function that will be called when you select an image. This function is responsible for sending the image to the enhancement service and updating the state with the results.\n*   Includes two other important components:\n    *   `ImageUpload`: This component provides the user interface for selecting an image. We'll explore it in detail in [Image Upload Interface](02_image_upload_interface_.md).\n    *   `ImagePreview`: This component displays both the original and enhanced images. We'll learn more about it in [Image Preview & Comparison Interface](03_image_preview___comparison_interface_.md).\n\n### The Frontend Core in Action: A Simple Flow\n\nLet's visualize how these pieces work together when you upload an image:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant Main as main.jsx\n    participant App as App.jsx\n    participant Home as Home.jsx\n    participant ImageUpload as ImageUpload.jsx\n    participant ImagePreview as ImagePreview.jsx\n\n    User->>Main: Launches App\n    Main->>App: Renders App component\n    App->>Home: Renders Home component (the core enhancement logic)\n    Home->>ImageUpload: Renders Image Upload Interface\n    Home->>ImagePreview: Renders Image Preview Interface\n\n    User->>ImageUpload: Selects an image file\n    ImageUpload->>Home: Calls onImageUpload(imageFile)\n    Home->>Home: Updates originalImage state\n    Home->>Home: Sets isLoading to true\n    Home->>ImagePreview: Passes updated states (originalImage, isLoading)\n    ImagePreview-->>User: Shows original image & loading indicator\n    Home->>Backend Proxy & API Gateway: Calls enhanceImage(imageFile) (via Image Enhancement Client Service)\n    Backend Proxy & API Gateway-->>Home: Returns enhanced image data\n    Home->>Home: Updates enhancedImage state\n    Home->>Home: Sets isLoading to false\n    Home->>ImagePreview: Passes updated states (originalImage, enhancedImage, isLoading)\n    ImagePreview-->>User: Shows original and enhanced images for comparison\n```\n\n1.  **Start-up:** When you launch the app, `main.jsx` starts `App.jsx`.\n2.  **Overall Layout:** `App.jsx` sets up the basic page layout and includes `Home.jsx`.\n3.  **Core Logic:** `Home.jsx` then sets up the `ImageUpload` area and the `ImagePreview` area.\n4.  **User Uploads:** When you choose an image, the `ImageUpload` component notifies `Home.jsx` about the new image.\n5.  **State Management:** `Home.jsx` takes this original image, saves it, and also sets a \"loading\" flag. It passes these pieces of information down to `ImagePreview` so you can see your original image and know the app is working.\n6.  **Enhancement Request:** `Home.jsx` then uses the `enhanceImage` function (which is part of our [Image Enhancement Client Service](04_image_enhancement_client_service_.md)) to send your image to the AI backend.\n7.  **Result Display:** Once the AI returns the enhanced image, `Home.jsx` updates its state again, including the `enhancedImage` and turning off the \"loading\" flag. This new information is then passed to `ImagePreview` so you can see the amazing transformation!\n\n### Conclusion\n\nIn this chapter, we've explored the \"Frontend Application Core\" of our AI Image Enhancer. We learned that it's the central control system that orchestrates the user's journey, from uploading an image to viewing the enhanced result. We saw how `main.jsx` kicks off the application, how `App.jsx` provides the main stage, and most importantly, how `Home.jsx` acts as the orchestrator, managing the flow and connecting the different parts of the image enhancement process.\n\nNext, we'll dive deeper into one of these connected parts: the **Image Upload Interface**. We'll learn how users can select and upload their photos to our application.\n\n[Next Chapter: Image Upload Interface](02_image_upload_interface_.md)\n---\n# Chapter 2: Image Upload Interface\n\nIn our previous chapter, [Frontend Application Core](01_frontend_application_core_.md), we learned that the `Home.jsx` component is the central \"orchestrator\" for managing the image enhancement process. It's like the conductor of an orchestra, making sure every part plays its role. One of the very first instruments in this orchestra is the **Image Upload Interface**.\n\n### What is the Image Upload Interface?\n\nImagine you want to send a letter. You don't just throw it into the air and hope it reaches its destination! You need a mailbox. In our `Simple-AI-Image-Enhancer` application, the **Image Upload Interface** is that special \"mailbox\" for your photos.\n\nThis is the specific part of the user interface where you interact to bring your image into the application. It's designed to be super easy to use:\n\n*   **Click to Browse:** You can simply click on the area, and a window will pop up, allowing you to select an image file from your computer, just like opening a document.\n*   **Drag and Drop:** Or, even simpler, you can just drag an image file directly from your computer's folders and \"drop\" it onto this area in the app.\n\nThe Image Upload Interface also has a smart guard dog: it makes sure that only valid image files (like JPGs or PNGs) are accepted. Once your image is safely inside, it then prepares it to be sent off for enhancement.\n\n### Our Goal: Bringing Your Image In\n\nOur main goal in this chapter is to understand how we can get your image file from your computer into our application, specifically into the `Home.jsx` component we talked about in Chapter 1.\n\nRecall from [Frontend Application Core](01_frontend_application_core_.md) that `Home.jsx` includes `ImageUpload` like this:\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\nimport ImageUpload from './ImageUpload' // Component to handle uploads\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  // ... other states ...\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      setOriginalImage(imageFile) // Save the image received from ImageUpload\n      // ... logic to send for enhancement ...\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      <ImageUpload onImageUpload={handleImageUpload} /> {/* This is our upload area! */}\n      {/* ... ImagePreview ... */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\nNotice the line `<ImageUpload onImageUpload={handleImageUpload} />`. This shows us that `Home.jsx` gives `ImageUpload` a special function called `handleImageUpload`. This is how `ImageUpload` will \"mail\" the selected image back to `Home.jsx`.\n\n### How It Works: The Journey of Your Image\n\nLet's trace what happens when you interact with the Image Upload Interface:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant ImageUpload as ImageUpload.jsx\n    participant Home as Home.jsx\n\n    User->>ImageUpload: Clicks or Drags & Drops image file\n    ImageUpload->>ImageUpload: Checks if it's a valid image (e.g., JPG, PNG)\n    alt If not a valid image\n        ImageUpload-->>User: Shows an \"Oops!\" message\n    else If valid image\n        ImageUpload->>Home: Calls onImageUpload(imageFile)\n        Home->>Home: Receives imageFile and prepares it for enhancement\n    end\n```\n\n1.  **You Interact:** You either click the upload area or drag an image file onto it.\n2.  **ImageUpload Catches It:** The `ImageUpload` component, which is the blueprint for our \"mailbox,\" \"catches\" the file.\n3.  **The Guard Dog Checks:** It immediately performs a quick check: \"Is this actually an image file?\" If it's not (e.g., you accidentally dropped a text file), it gently tells you.\n4.  **Delivering the Mail:** If it *is* a valid image, `ImageUpload` doesn't keep it. Instead, it takes that image file and \"mails\" it back to `Home.jsx` by calling the `onImageUpload` function that `Home.jsx` gave it.\n5.  **Home.jsx Takes Over:** Now `Home.jsx` has your image, ready to start the enhancement process!\n\n### Diving into the Code: `src/components/ImageUpload.jsx`\n\nLet's open up the `src/components/ImageUpload.jsx` file to see how this \"mailbox\" is built.\n\n#### 1. Remembering What's Happening with `useState`\n\nFirst, `ImageUpload.jsx` needs to remember a few things, like if you're currently dragging a file or what file you've selected. It uses `useState` for this.\n\n```javascript\n// src/components/ImageUpload.jsx\nimport React, { useState, useRef } from 'react'\n\nconst ImageUpload = ({ onImageUpload }) => {\n  const [isDragging, setIsDragging] = useState(false) // Is a file being dragged over the area?\n  const [fileName, setFileName] = useState('')       // What's the name of the file selected?\n  const fileInputRef = useRef(null)                   // A secret way to click a hidden file input\n\n  // ... rest of the component's code ...\n}\nexport default ImageUpload\n```\n\n*Explanation*: `useState` helps our component remember information that might change (like if `isDragging` is true or false). `useRef` is like having a direct \"handle\" to an actual part of the web page (in our case, a hidden file selector), so we can tell it to do things, like \"click yourself!\"\n\n#### 2. Handling Drag-and-Drop\n\nWhen you drag a file, your browser has its own default behavior (like trying to open the file). We need to stop that and tell our component what to do instead.\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component)\nconst handleDragOver = (e) => {\n  e.preventDefault() // Stop the browser from doing its own thing\n  setIsDragging(true) // Turn on the \"dragging\" style (e.g., blue border)\n}\n\nconst handleDragLeave = () => {\n  setIsDragging(false) // Turn off the \"dragging\" style\n}\n\nconst handleDrop = (e) => {\n  e.preventDefault() // Stop the browser\n  setIsDragging(false) // Turn off the \"dragging\" style\n  \n  if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n    const file = e.dataTransfer.files[0] // Get the dropped file\n    processFile(file) // Send it to our \"guard dog\" function\n  }\n}\n// ... rest of the component ...\n```\n\n*Explanation*: These three functions handle the drag-and-drop actions. `handleDragOver` and `handleDragLeave` simply change the look of our upload box to give you feedback (like making the border blue). When you finally `handleDrop` the file, we grab the file and send it to `processFile` to check if it's an image.\n\n#### 3. Handling Click-to-Upload\n\nNot everyone likes dragging and dropping! We also provide a way to click and browse your files.\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component)\nconst handleFileChange = (e) => {\n  if (e.target.files && e.target.files[0]) {\n    const file = e.target.files[0] // Get the selected file\n    processFile(file) // Send it to our \"guard dog\" function\n  }\n}\n\nconst handleClick = () => {\n  fileInputRef.current.click() // Secretly \"click\" the hidden file selection box\n}\n// ... rest of the component ...\n```\n\n*Explanation*: When you click anywhere on our upload box, `handleClick` makes the hidden file input (which normally opens the file selection window) pop up. Once you choose a file from that window, `handleFileChange` is activated, which then passes your chosen file to `processFile`.\n\n#### 4. The \"Guard Dog\" and Mailman: `processFile`\n\nThis is a crucial function that acts as both the validator and the delivery person.\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component)\nconst processFile = (file) => {\n  // Is it an image?\n  if (!file.type.match('image.*')) { // Checks if the file type starts with 'image/'\n    alert('Oops! Please select an image file (like JPG, PNG, WEBP).')\n    return // Stop right here if it's not an image\n  }\n  \n  setFileName(file.name) // Display the file's name in the UI\n  onImageUpload(file)    // This is where we \"mail\" the file back to Home.jsx!\n}\n// ... rest of the component ...\n```\n\n*Explanation*: `processFile` first checks if the file's `type` matches an image format. If not, it shows an alert. If it *is* an image, it updates the `fileName` to be displayed and then, most importantly, calls `onImageUpload(file)`. This is the exact moment the `ImageUpload` component hands off the selected image file to `Home.jsx` for further processing.\n\n#### 5. The Visual \"Mailbox\": JSX Structure\n\nFinally, all these pieces come together in the visual part of the component, which uses JSX (a mix of JavaScript and HTML-like tags).\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component's return statement)\nreturn (\n  <div className='bg-white dark:bg-gray-800 shadow-lg rounded-2xl w-full max-w-2xl p-6'>\n    <div \n      className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-all cursor-pointer\n        ${isDragging ? 'border-blue-500 bg-blue-50 dark:bg-blue-900' : 'border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400'}`}\n      onDragOver={handleDragOver}   // What happens when dragging over\n      onDragLeave={handleDragLeave} // What happens when dragging leaves\n      onDrop={handleDrop}           // What happens when dropped\n      onClick={handleClick}         // What happens when clicked\n    >\n      <input \n        type=\"file\" \n        id=\"fileInput\" \n        ref={fileInputRef}     // Connects to our secret handle\n        className='hidden'     // Makes the default file input invisible\n        accept=\"image/*\"       // Only allows image files in the selection window\n        onChange={handleFileChange} // What happens when a file is selected\n      />\n      \n      <div className=\"flex flex-col items-center justify-center py-4\">\n        {/* An SVG icon to make it look nice */}\n        <p className='text-lg font-medium'>\n          {fileName ? `Selected: ${fileName}` : 'Click or drag to upload an image'}\n        </p>\n        <p className='text-sm text-gray-500'>\n          Supports JPG, PNG, WEBP (Max 10MB)\n        </p>\n      </div>\n    </div>\n  </div>\n)\n```\n\n*Explanation*: This is what you see! It's a nicely styled `div` (our \"mailbox\" container). It listens for your actions like `onDragOver`, `onDragLeave`, `onDrop`, and `onClick`, connecting them to the functions we just discussed. Inside it, there's a `<input type=\"file\" />` that's kept `hidden` and controlled by our JavaScript. The text dynamically changes to show the `fileName` once an image is selected, making the interface clear and user-friendly.\n\n### Conclusion\n\nIn this chapter, we've explored the **Image Upload Interface**, the crucial first step in our image enhancement journey. We learned how this component acts as a user-friendly \"mailbox,\" allowing you to easily bring your images into the application using either click-to-browse or drag-and-drop. We also saw how it validates files and, most importantly, \"mails\" the selected image back to `Home.jsx` via the `onImageUpload` function.\n\nNow that we know how to get an image *into* the app, the next logical step is to see it! In the next chapter, we'll dive into the **Image Preview & Comparison Interface**, where you'll see your original image and, eventually, its beautifully enhanced counterpart.\n\n[Next Chapter: Image Preview & Comparison Interface](03_image_preview___comparison_interface_.md)\n---\n# Chapter 3: Image Preview & Comparison Interface\n\nIn our previous chapter, [Image Upload Interface](02_image_upload_interface.md), we learned how our application provides a user-friendly \"mailbox\" for you to bring your images into the system. Now that your image has been successfully \"mailed\" into the app, where does it go next? It needs a place to be seen!\n\nThis is where the **Image Preview & Comparison Interface** comes in.\n\n### What is the Image Preview & Comparison Interface?\n\nImagine you're looking at a photo album. When you first upload a picture, you want to see that picture immediately. That's the first job of this interface: it acts like a digital \"photo album\" displaying your original image as soon as it's ready.\n\nBut it gets even better! Once our clever AI has worked its magic and enhanced your image, this interface transforms into a super-cool \"compare tool.\" It lets you see your original image right next to the new, improved version. You can even slide a bar across the image to smoothly switch between the \"before\" and \"after\" views, helping you truly appreciate the enhancement.\n\nAnd of course, once you're happy with the result, it gives you an easy way to \"take home\" your newly enhanced masterpiece by providing a convenient download button.\n\n### Our Goal: Showing Off Your Images\n\nOur main goal in this chapter is to understand how the `ImagePreview` component works to display your pictures at different stages of the enhancement process – from just being uploaded, to waiting for AI, and finally, showcasing the amazing before-and-after transformation.\n\nRecall from [Frontend Application Core](01_frontend_application_core.md) that `Home.jsx` is the orchestrator, and it uses `ImagePreview` by passing it important information:\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\n// ... other imports ...\nimport ImagePreview from './ImagePreview' // Our preview component\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  const [enhancedImage, setEnhancedImage] = useState(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  // ... handleImageUpload function (updates originalImage, sets isLoading) ...\n  // ... logic to call enhancement service (updates enhancedImage, sets isLoading false) ...\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      {/* ... ImageUpload component ... */}\n      <ImagePreview \n        originalImage={originalImage} \n        enhancedImage={enhancedImage}\n        isLoading={isLoading}\n      /> {/* This is our preview area! */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\n`Home.jsx` passes three key pieces of information (called \"props\") to `ImagePreview`:\n*   `originalImage`: The image you just uploaded.\n*   `enhancedImage`: The amazing picture after the AI has worked on it.\n*   `isLoading`: A true/false switch that tells `ImagePreview` if the AI is currently busy enhancing your image.\n\n### How It Works: The Image's Display Journey\n\nLet's trace how your image appears and changes in the `ImagePreview` interface:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant Home as Home.jsx\n    participant ImagePreview as ImagePreview.jsx\n\n    User->>Home: Uploads image\n    Home->>ImagePreview: Passes originalImage, isLoading=true\n    ImagePreview-->>User: Shows a \"Please Wait...\" screen (Loading State)\n\n    Home->>Home: Sends image for enhancement (via Image Enhancement Client Service)\n    Home->>Home: Receives enhanced image\n    Home->>ImagePreview: Passes originalImage, enhancedImage, isLoading=false\n\n    alt If original exists, but no enhanced yet (e.g., after initial upload, before enhancement result)\n        ImagePreview-->>User: Shows ONLY the original image (Initial Preview State)\n    else If both original and enhanced images are ready\n        ImagePreview-->>User: Shows the interactive Before/After comparison (Comparison State)\n    end\n```\n\n1.  **Original Arrives, AI is Busy**: As soon as you upload an image, `Home.jsx` sends your `originalImage` to `ImagePreview` and also tells it `isLoading` is `true`. `ImagePreview` immediately puts up a \"Please Wait...\" sign.\n2.  **AI Finishes**: Once the AI processing is done, `Home.jsx` updates `ImagePreview` with the `enhancedImage` and sets `isLoading` to `false`.\n3.  **The Big Reveal**: Now `ImagePreview` has both the original and enhanced images, and it switches from the \"Please Wait...\" screen to the cool comparison tool, letting you slide between them.\n\n### Diving into the Code: `src/components/ImagePreview.jsx`\n\nLet's look inside the `src/components/ImagePreview.jsx` file to see how it manages these different display \"scenarios.\"\n\n#### 1. The Component's \"Brain\": Receiving Information\n\nFirst, the `ImagePreview` component gets the `originalImage`, `enhancedImage`, and `isLoading` information from `Home.jsx`.\n\n```javascript\n// src/components/ImagePreview.jsx\nimport React from 'react';\nimport { ReactCompareSlider, ReactCompareSliderImage } from 'react-compare-slider';\nimport Loading from './Loading'; // A small component to show \"loading\"\n\nconst ImagePreview = ({ originalImage, enhancedImage, isLoading }) => {\n    // This component will change what it shows based on these three pieces of info!\n\n    // ... The rest of the logic (different display scenarios) ...\n};\n\nexport default ImagePreview;\n```\n*Explanation*: The `ImagePreview` component is a \"smart display.\" It listens to the `originalImage`, `enhancedImage`, and `isLoading` signals from `Home.jsx` to decide what to show on your screen.\n\n#### 2. Scenario 1: The \"Please Wait...\" Screen\n\nIf the `isLoading` signal is `true`, it means our AI is busy working its magic. We want to show a friendly loading animation.\n\n```javascript\n// src/components/ImagePreview.jsx (inside the component)\n    if (isLoading) {\n        return (\n            <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl flex justify-center items-center min-h-[300px]\">\n                <Loading /> {/* Our spinning wheel or message */}\n            </div>\n        );\n    }\n```\n*Explanation*: If `isLoading` is `true`, the component immediately stops here and shows a `Loading` animation (which is a separate, simple spinning wheel component). This tells you that the app is busy and you should wait.\n\n#### 3. Scenario 2: The \"First Look\" – Only Original Image\n\nWhat if you've uploaded an image, but the AI hasn't finished enhancing it yet (or perhaps it failed)? In this case, we still want to show your original picture, even if the enhanced one isn't ready.\n\n```javascript\n// src/components/ImagePreview.jsx (inside the component)\n    // If not loading, and original image exists, but no enhanced image yet:\n    if (originalImage && !enhancedImage && !isLoading) {\n         return (\n             <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl\">\n                <h2 className=\"text-xl font-semibold text-center ...\">Original Image</h2>\n                <img \n                    src={originalImage} \n                    alt=\"Original Uploaded\" \n                    className=\"max-w-full h-auto rounded mx-auto\" \n                    style={{ maxHeight: '60vh' }} \n                />\n            </div>\n         );\n    }\n```\n*Explanation*: This block checks if we have an `originalImage`, but `enhancedImage` is `null` (meaning it's not ready yet), and we're not currently `isLoading`. If all these are true, it simply displays your `originalImage` along with a \"Original Image\" title.\n\n#### 4. Scenario 3: The \"Big Reveal\" – Before & After Comparison\n\nThis is the main event! If we have both `originalImage` AND `enhancedImage`, it's time to show them side-by-side using a special comparison tool.\n\n```javascript\n// src/components/ImagePreview.jsx (inside the component)\n    // If both original and enhanced images are available:\n    if (originalImage && enhancedImage) {\n        return (\n             <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl\">\n                 <h2 className=\"text-xl font-semibold text-center ...\">Compare Images</h2>\n                 <ReactCompareSlider\n                     style={{ height: '70vh', width: '100%', margin: '0 auto' }}\n                    itemOne={\n                         <ReactCompareSliderImage src={originalImage} alt=\"Original Image\" />\n                     }\n                    itemTwo={\n                            <ReactCompareSliderImage src={enhancedImage} alt=\"Enhanced Image\" />\n                     }\n                />\n                 <div className=\"text-center mt-4\">\n                        <a\n                          href={enhancedImage} // Link to the enhanced image\n                          download=\"enhanced-image.png\" // Suggested filename\n                          className=\"inline-block bg-blue-500 hover:bg-blue-700 ... \"\n                        >\n                            Download Enhanced Image\n                        </a>\n                 </div>\n            </div>\n        );\n    }\n```\n*Explanation*:\n*   **`ReactCompareSlider`**: This is a powerful little tool (a \"third-party library\") that makes the cool sliding comparison effect. We give it `itemOne` (our `originalImage`) and `itemTwo` (our `enhancedImage`), and it magically creates the slider.\n*   **Download Button**: Below the slider, there's a simple `<a>` (link) tag. The `href` points directly to the `enhancedImage` data, and the `download` attribute tells your browser to save it as `enhanced-image.png` when you click it. It's like pressing \"Save\" on your masterpiece!\n\n#### 5. Fallback: Nothing to Show\n\nFinally, if none of the above conditions are met (e.g., no image has been uploaded yet, and it's not loading), the component simply returns nothing.\n\n```javascript\n// src/components/ImagePreview.jsx (inside the component)\n    // Fallback case (e.g., no images uploaded yet)\n    return null;\n}; // End of the ImagePreview component\n```\n*Explanation*: This ensures that if there's no relevant image data, our `ImagePreview` area remains empty and tidy, waiting for you to upload a picture.\n\n### Conclusion\n\nIn this chapter, we've explored the **Image Preview & Comparison Interface**, the \"photo album\" and \"compare tool\" of our application. We learned how the `ImagePreview` component intelligently displays your images at different stages: showing a loading screen when the AI is busy, displaying just the original image when first uploaded, and finally, transforming into an interactive before-and-after slider with a handy download button once the enhancement is complete.\n\nNow that we know how images are displayed, the next logical step is to understand how the application *requests* the enhancement from the AI. In the next chapter, we'll dive into the **Image Enhancement Client Service**, the part of our app that talks to the powerful AI backend.\n\n[Next Chapter: Image Enhancement Client Service](04_image_enhancement_client_service.md)\n\nRelevant Code Snippets (Code itself remains unchanged):\n--- File: src/services/imageEnhancer.js ---\nimport axios from \"axios\";\n\nconst BASE_URL = \"http://localhost:3001\"; // URL of our backend proxy\nconst MAXIMUM_RETRIES = 20; // Max polling attempts\n\nexport const enhanceImage = async (file) => {\n    try {\n        const taskId = await uploadImage(file);\n        console.log(\"Image Uploaded Successfully, Task ID:\", taskId);\n\n        const enhancedImageData = await PollForEnhancedImage(taskId);\n        console.log(\"Enhanced Image Data:\", enhancedImageData);\n\n        return enhancedImageData;\n    } catch (error) {\n        // Log the error potentially coming from the backend or network issues\n        console.error(\"Error enhancing image:\", error.response?.data?.message || error.message);\n        // Optionally re-throw or return an error indicator to the UI\n        throw error; // Re-throw the error so the component can handle it (e.g., show message)\n    }\n};\n\nconst uploadImage = async (file) => {\n    const formData = new FormData();\n    formData.append(\"image_file\", file);\n\n    // Send image to our backend proxy\n    const { data } = await axios.post(\n        `${BASE_URL}/api/enhance`, // Endpoint on our backend\n        formData,\n        {\n            headers: {\n                // Content-Type is set automatically by browser for FormData\n                // No API Key needed here\n            },\n        }\n    );\n\n    // Our backend returns { taskId: '...' } directly\n    if (!data?.taskId) {\n        throw new Error(\"Failed to upload image! Task ID not received from backend.\");\n    }\n    return data.taskId;\n};\n\nconst PollForEnhancedImage = async (taskId, retries = 0) => {\n    const result = await fetchEnhancedImage(taskId);\n\n    // Check if the task state indicates it's still processing (state 4 means processing)\n    if (result.state === 4) {\n        console.log(`Processing...(${retries}/${MAXIMUM_RETRIES})`);\n\n        if (retries >= MAXIMUM_RETRIES) {\n            throw new Error(\"Max retries reached. Please try again later.\");\n        }\n\n        // wait for 2 second\n        await new Promise((resolve) => setTimeout(resolve, 2000));\n\n        return PollForEnhancedImage(taskId, retries + 1);\n    }\n\n    console.log(\"Enhanced Image URL:\", result);\n    return result;\n};\n\nconst fetchEnhancedImage = async (taskId) => {\n    // Fetch status from our backend proxy\n    const { data } = await axios.get(\n        `${BASE_URL}/api/status/${taskId}`, // Status endpoint on our backend\n        {\n            // No headers needed here\n        }\n    );\n    // Backend forwards the external API's data structure, which might be directly the data object\n    // or nested under 'data'. Adjust based on backend's response structure.\n    // Assuming backend returns the external API's data directly:\n    if (!data) { // Check if data object itself exists\n        throw new Error(\"Failed to fetch enhanced image status from backend!\");\n    }\n    // The polling logic expects the object containing 'state', etc.\n    return data;\n};\n\n\nInstructions for the chapter (Generate content in English unless specified otherwise):\n- Start with a clear heading (e.g., `# Chapter 4: Image Enhancement Client Service\n`). Use the provided concept name.\n\n- If this is not the first chapter, begin with a brief transition from the previous chapter, referencing it with a proper Markdown link using its name.\n\n- Begin with a high-level motivation explaining what problem this abstraction solves. Start with a central use case as a concrete example. The whole chapter should guide the reader to understand how to solve this use case. Make it very minimal and friendly to beginners.\n\n- If the abstraction is complex, break it down into key concepts. Explain each concept one-by-one in a very beginner-friendly way.\n\n- Explain how to use this abstraction to solve the use case. Give example inputs and outputs for code snippets (if the output isn't values, describe at a high level what will happen).\n\n- Each code block should be BELOW 10 lines! If longer code blocks are needed, break them down into smaller pieces and walk through them one-by-one. Aggresively simplify the code to make it minimal. Use comments to skip non-important implementation details. Each code block should have a beginner friendly explanation right after it.\n\n- Describe the internal implementation to help understand what's under the hood. First provide a non-code or code-light walkthrough on what happens step-by-step when the abstraction is called. It's recommended to use a simple sequenceDiagram with a dummy example - keep it minimal with at most 5 participants to ensure clarity. If participant name has space, use: `participant QP as Query Processing`. .\n\n- Then dive deeper into code for the internal implementation with references to files. Provide example code blocks, but make them similarly simple and beginner-friendly. Explain.\n\n- IMPORTANT: When you need to refer to other core abstractions covered in other chapters, ALWAYS use proper Markdown links like this: [Chapter Title](filename.md). Use the Complete Tutorial Structure above to find the correct filename and the chapter title. Translate the surrounding text.\n\n- Use mermaid diagrams to illustrate complex concepts (```mermaid``` format). .\n\n- Heavily use analogies and examples throughout to help beginners understand.\n\n- End the chapter with a brief conclusion that summarizes what was learned and provides a transition to the next chapter. If there is a next chapter, use a proper Markdown link: [Next Chapter Title](next_chapter_filename).\n\n- Ensure the tone is welcoming and easy for a newcomer to understand.\n\n- Output *only* the Markdown content for this chapter.\n\nNow, directly provide a super beginner-friendly Markdown output (DON'T need ```markdown``` tags):\n": "# Chapter 4: Image Enhancement Client Service\n\nIn our previous chapter, [Image Preview & Comparison Interface](03_image_preview___comparison_interface.md), we learned how our application displays images, from the initial upload to the final, amazing before-and-after comparison. But for that comparison to happen, we first need the *enhanced* image!\n\nWhere does that enhanced image come from? It comes from our powerful AI backend, which is like a super-smart artist living in a different city. Our frontend (the part of the app you see and interact with) needs a way to send your image to this artist and then patiently wait for the masterpiece to be sent back. This is where the **Image Enhancement Client Service** steps in.\n\n### What is the Image Enhancement Client Service?\n\nImagine you want to order a custom painting from an artist far away. You wouldn't just shout your request across the country! You'd hire a reliable **messenger service** or a **personal assistant** to handle all the tricky communication.\n\nIn our `Simple-AI-Image-Enhancer` application, the **Image Enhancement Client Service** is exactly that: it's the dedicated \"messenger\" on the frontend side. Its main job is to communicate with our backend server to get your images enhanced. It's the one who:\n\n1.  **Sends your original image** to the AI artist (the backend).\n2.  **Repeatedly checks in** with the artist to ask, \"Is it done yet?\" (We call this \"polling\").\n3.  **Receives the final enhanced image URL** once it's ready.\n\nBy handling all these complex steps, our \"messenger\" makes life easy for the main parts of our app (like `Home.jsx`), allowing them to focus on just *showing* you the images, not worrying about *how* they get enhanced.\n\n### Our Goal: Getting the Enhanced Image\n\nOur main goal in this chapter is to understand how our app uses this \"messenger\" service to send an image for enhancement and then patiently wait for the result. Specifically, we'll look at the `enhanceImage` function that `Home.jsx` calls when you upload a photo.\n\nRecall from [Frontend Application Core](01_frontend_application_core.md) that `Home.jsx` uses this service like so:\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\n// ... other imports ...\nimport { enhanceImage } from '../services/imageEnhancer' // Our \"messenger\" service\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  const [enhancedImage, setEnhancedImage] = useState(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      setOriginalImage(URL.createObjectURL(imageFile)); // For immediate preview\n      setIsLoading(true); // Show loading spinner\n      try {\n        const enhancedResult = await enhanceImage(imageFile); // HERE'S THE CALL!\n        setEnhancedImage(enhancedResult.enhanced_url); // Get the enhanced image URL\n      } catch (error) {\n        console.error(\"Enhancement failed:\", error);\n        // Handle error, maybe show an error message to the user\n      } finally {\n        setIsLoading(false); // Hide loading spinner\n      }\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      {/* ... ImageUpload and ImagePreview components ... */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\nThe line `const enhancedResult = await enhanceImage(imageFile);` is where `Home.jsx` hands off the image to our service and patiently `await`s the result.\n\n### How It Works: The Messenger's Journey\n\nLet's visualize how our \"messenger\" (the Image Enhancement Client Service) handles getting your image enhanced:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant Home as Home.jsx\n    participant ClientService as ImageEnhancerClientService\n    participant Backend as Backend Proxy & API Gateway\n\n    User->>Home: Uploads Image\n    Home->>ClientService: Calls enhanceImage(imageFile)\n    ClientService->>Backend: 1. Uploads original image\n    Backend-->>ClientService: Returns a Task ID (like a tracking number)\n    loop Check Status Repeatedly (Polling)\n        ClientService->>Backend: 2. Asks for status of Task ID\n        Backend-->>ClientService: Returns current status (e.g., \"processing\", \"done\")\n        alt If still processing\n            ClientService: Waits a few seconds\n        else If done\n            break\n        end\n    end\n    Backend-->>ClientService: Returns Enhanced Image URL\n    ClientService-->>Home: Returns Enhanced Image URL\n    Home-->>User: Displays enhanced image\n```\n\n1.  **User Uploads**: You select an image, and `Home.jsx` gets it.\n2.  **Hand-off to Messenger**: `Home.jsx` then calls `enhanceImage` in our `ClientService`, giving it your image.\n3.  **Messenger Uploads**: The `ClientService` first sends your image to the `Backend`. The `Backend` gives back a `Task ID` – a unique tracking number for your enhancement job.\n4.  **Messenger Polls (Checks Status)**: The `ClientService` then uses this `Task ID` to repeatedly ask the `Backend`: \"Is task `[Task ID]` done yet?\"\n5.  **Backend Responds**: The `Backend` responds, either saying \"Still working!\" or \"It's done! Here's the URL for your enhanced image!\"\n6.  **Messenger Delivers**: Once the `Backend` says it's done and gives the URL, the `ClientService` delivers that URL back to `Home.jsx`.\n7.  **Display**: `Home.jsx` then updates its state, and `ImagePreview` shows your beautifully enhanced image!\n\n### Diving into the Code: `src/services/imageEnhancer.js`\n\nAll the magic for our \"messenger\" service happens in the `src/services/imageEnhancer.js` file. Let's break it down.\n\n#### 1. The Main Messenger Function: `enhanceImage`\n\nThis is the central function that `Home.jsx` calls. It orchestrates the entire process: first uploading the image and then repeatedly checking for the result.\n\n```javascript\n// src/services/imageEnhancer.js\nimport axios from \"axios\"; // A library to easily send web requests\n\n// Our backend's address and how many times we'll check\nconst BASE_URL = \"http://localhost:3001\";\nconst MAXIMUM_RETRIES = 20;\n\nexport const enhanceImage = async (file) => {\n    try {\n        const taskId = await uploadImage(file); // Step 1: Send the image & get a task ID\n        console.log(\"Image Uploaded, Task ID:\", taskId);\n\n        // Step 2: Keep asking the backend until the enhancement is ready\n        const enhancedImageData = await PollForEnhancedImage(taskId);\n        console.log(\"Enhanced Image Data Received:\", enhancedImageData);\n\n        return enhancedImageData; // Give the enhanced image URL back to Home.jsx\n    } catch (error) {\n        // If anything goes wrong, log it and let Home.jsx know\n        console.error(\"Error enhancing image:\", error.message);\n        throw error;\n    }\n};\n```\n*Explanation*: The `enhanceImage` function is like the project manager. It calls `uploadImage` to start the process and get a `taskId`. Then, it hands that `taskId` over to `PollForEnhancedImage`, which is responsible for waiting until the job is done. Once `PollForEnhancedImage` finishes, it returns the final enhanced image data.\n\n#### 2. Step 1: Sending the Image (`uploadImage`)\n\nThis function is responsible for taking your image file and sending it to our backend for processing.\n\n```javascript\n// src/services/imageEnhancer.js (inside imageEnhancer.js)\nconst uploadImage = async (file) => {\n    const formData = new FormData();\n    formData.append(\"image_file\", file); // Prepare the image to be sent\n\n    // Send the image to our backend's enhancement endpoint\n    const { data } = await axios.post(\n        `${BASE_URL}/api/enhance`, // The specific web address for enhancement\n        formData, // The image data\n        // Headers are automatically set for FormData, no API key needed here\n    );\n\n    // The backend should give us a task ID right away\n    if (!data?.taskId) {\n        throw new Error(\"Failed to get Task ID from backend!\");\n    }\n    return data.taskId; // Return the unique ID for this enhancement job\n};\n```\n*Explanation*: `uploadImage` uses `FormData` to bundle your image file in a way that can be sent over the internet. It then uses `axios.post` to send this bundle to the backend at the `/api/enhance` address. The backend immediately responds with a `taskId`, which is like a unique tracking number for your specific enhancement request.\n\n#### 3. Step 2: Repeatedly Asking for Updates (`PollForEnhancedImage`)\n\nThis is where the \"polling\" happens. This function repeatedly asks the backend for the status of your enhancement job using the `taskId`.\n\n```javascript\n// src/services/imageEnhancer.js (inside imageEnhancer.js)\nconst PollForEnhancedImage = async (taskId, retries = 0) => {\n    const result = await fetchEnhancedImage(taskId); // Ask for the latest status\n\n    // Our backend might use 'state: 4' to mean \"still processing\"\n    if (result.state === 4) {\n        console.log(`Image processing... (Attempt ${retries + 1} of ${MAXIMUM_RETRIES})`);\n\n        if (retries >= MAXIMUM_RETRIES) {\n            throw new Error(\"Enhancement took too long. Please try again.\");\n        }\n\n        // Wait for 2 seconds before asking again\n        await new Promise((resolve) => setTimeout(resolve, 2000));\n\n        // Call ourselves again to check the status after waiting\n        return PollForEnhancedImage(taskId, retries + 1);\n    }\n\n    // If 'state' is not 4, it means it's done (or failed, which backend handles)\n    return result; // This contains the enhanced image URL if successful\n};\n```\n*Explanation*: `PollForEnhancedImage` calls `fetchEnhancedImage` to get the status. If the status says `state: 4` (meaning \"processing\"), it prints a message, waits for 2 seconds using `setTimeout`, and then calls *itself* (`PollForEnhancedImage`) again to check the status. It does this over and over until the `state` is no longer `4` (meaning it's done!) or it hits the `MAXIMUM_RETRIES` limit.\n\n#### 4. Step 3: Getting the Latest Status (`fetchEnhancedImage`)\n\nThis is the simplest part of the polling process: it just asks the backend for the current status of a given `taskId`.\n\n```javascript\n// src/services/imageEnhancer.js (inside imageEnhancer.js)\nconst fetchEnhancedImage = async (taskId) => {\n    // Send a request to our backend's status endpoint with the task ID\n    const { data } = await axios.get(\n        `${BASE_URL}/api/status/${taskId}`, // The specific web address for status checks\n    );\n\n    if (!data) {\n        throw new Error(\"Did not receive status data from backend!\");\n    }\n    // The backend provides an object with status information (e.g., state, or enhanced_url)\n    return data;\n};\n```\n*Explanation*: `fetchEnhancedImage` uses `axios.get` to send a request to the backend's `/api/status/[taskId]` address. The backend responds with an object that tells us the current `state` of the enhancement job (e.g., if it's still processing, or if it's done and provides the enhanced image URL).\n\n### Conclusion\n\nIn this chapter, we've explored the **Image Enhancement Client Service**, our application's dedicated \"messenger\" for talking to the AI backend. We learned how this service takes your original image, sends it for enhancement, and then intelligently \"polls\" (repeatedly checks) the backend for updates until the enhanced image is ready. This powerful abstraction makes the complex communication process simple and reliable for the rest of our frontend application.\n\nNow that we understand how the frontend talks to the backend, it's time to see what happens on the *other side* of the conversation! In the next chapter, we'll dive into the **Backend Proxy & API Gateway**, which is the server that receives our messenger's requests and forwards them to the actual AI.\n\n[Next Chapter: Backend Proxy & API Gateway](05_backend_proxy___api_gateway_.md)", "\nWrite a very beginner-friendly tutorial chapter (in Markdown format) for the project `Simple-AI-Image-Enhancer` about the concept: \"Backend Proxy & API Gateway\n\". This is Chapter 5.\n\nConcept Details:\n- Name: Backend Proxy & API Gateway\n\n- Description:\nThis acts as a secure intermediary, or a \"bouncer,\" between the user-facing frontend and the external AI image enhancement service. When you upload an image, the frontend sends it here first. The backend then securely adds your secret API key (which the frontend doesn't see) and forwards the image to the external AI service. It also handles polling the external service for the enhancement status and sends the results back to the frontend.\n\n\nComplete Tutorial Structure:\n1. [Frontend Application Core\n](01_frontend_application_core_.md)\n2. [Image Upload Interface\n](02_image_upload_interface_.md)\n3. [Image Preview & Comparison Interface\n](03_image_preview___comparison_interface_.md)\n4. [Image Enhancement Client Service\n](04_image_enhancement_client_service_.md)\n5. [Backend Proxy & API Gateway\n](05_backend_proxy___api_gateway_.md)\n6. [Theme Management System\n](06_theme_management_system_.md)\n\nContext from previous chapters:\n# Chapter 1: Frontend Application Core\n\nWelcome to the first chapter of our tutorial for the `Simple-AI-Image-Enhancer` project! In this chapter, we're going to uncover the \"brain\" of our application's user-facing side, which we call the **Frontend Application Core**.\n\n### What is the Frontend Application Core?\n\nImagine you're building a house. You don't just throw bricks and wood together; you need a blueprint, a general contractor, and a clear plan for how everything fits. The \"Frontend Application Core\" is like that general contractor for our AI Image Enhancer app.\n\nIts main job is to **orchestrate** how you, the user, interact with the application. From the moment you open the app, to uploading your photo, seeing it processed, and finally viewing the enhanced version – the Frontend Application Core is in charge of managing this entire journey.\n\n**Think of it as the control center of the whole user interface.** It makes sure that different parts of the app, like the \"upload photo\" area or the \"show enhanced image\" section, work together smoothly to give you a complete and easy-to-use experience.\n\n### Our Goal: Understanding the Enhancement Flow\n\nOur main goal in this chapter is to understand how the app guides you through the core process of enhancing an image. Specifically, we want to know how the app takes your raw input (an unenhanced image) and prepares it for enhancement, ultimately displaying the result.\n\nHere's the simple user journey we'll explore:\n1.  You open the app.\n2.  You see a place to upload an image.\n3.  You select an image file from your computer.\n4.  The app displays your original image.\n5.  After some processing, the app displays the *enhanced* version of your image.\n\nHow does our \"Frontend Application Core\" manage all these steps? Let's dive in!\n\n### The Building Blocks: `main.jsx`, `App.jsx`, and `Home.jsx`\n\nOur application's core functionality is primarily handled by three key files:\n\n1.  **`src/main.jsx`**: This is the very first file that runs when our application starts. It's like the \"power button\" for our React app.\n2.  **`src/App.jsx`**: This file represents the top-level part of our application. Think of it as the \"main house\" or the overall container that holds everything together. It sets up the basic layout and includes other major parts.\n3.  **`src/components/Home.jsx`**: This is a key \"room\" within our \"main house\" specifically dedicated to the image enhancement process. It manages the steps for uploading, displaying, and triggering the enhancement.\n\nLet's look at each of them.\n\n#### 1. `src/main.jsx`: The Starting Line\n\nThis file is responsible for \"bootstrapping\" our React application. It tells the web browser where to \"mount\" or display our app.\n\n```javascript\n// src/main.jsx\nimport { StrictMode } from 'react'\nimport { createRoot } from 'react-dom/client'\nimport './index.css' // Basic styles\nimport App from './App.jsx' // Our main application component\nimport { ThemeProvider } from './contexts/ThemeContext'; // Theme settings\n\ncreateRoot(document.getElementById('root')).render(\n  <StrictMode>\n    <ThemeProvider>\n      <App /> {/* This is where our entire app starts! */}\n    </ThemeProvider>\n  </StrictMode>,\n)\n```\n\nIn this code, `createRoot(document.getElementById('root'))` finds a special spot in our web page (an HTML element with the ID `root`) and prepares it to display our React app. Then, `.render(<App />)` tells React to put our entire `App` component inside that spot. Notice how `App` is wrapped in `ThemeProvider` – this helps us manage light and dark modes across the app, which we'll cover in [Theme Management System](06_theme_management_system_.md).\n\n#### 2. `src/App.jsx`: The Main Stage\n\n`App.jsx` is where the overall structure and feel of our application are set. It's like the main stage where all the action happens. It defines the title of our app and includes the `Home` component, which is where the main image enhancement features live.\n\n```javascript\n// src/App.jsx\nimport React, { useContext } from 'react';\nimport Home from './components/Home'; // Our core enhancement component\nimport { ThemeContext } from './contexts/ThemeContext'; // For theme switching\n\nconst App = () => {\n  const { theme, toggleTheme } = useContext(ThemeContext);\n\n  return (\n    <div className='flex flex-col items-center ...'> {/* Overall layout */}\n      {/* ... Theme button and header ... */}\n      <h1 className='text-5xl font-bold ...'>AI Image Enhancer</h1>\n      <p className='text-lg text-gray-500 ...'>Upload an image and let AI enhance it in seconds.</p>\n      <Home/> {/* This is where the core functionality resides! */}\n      {/* ... Footer ... */}\n    </div>\n  );\n};\n\nexport default App;\n```\n\nHere, `App.jsx` is mainly responsible for the very top-level appearance and for including the `Home` component. It also contains the theme switching button, which is related to our [Theme Management System](06_theme_management_system_.md). The key takeaway here is that `App` brings in `Home` to do the actual image enhancement work.\n\n#### 3. `src/components/Home.jsx`: The Orchestrator\n\nThis is where the magic of orchestrating the image enhancement process truly happens. `Home.jsx` acts as the central control for managing the original image, the enhanced image, and the loading state. It connects the \"upload\" part with the \"preview\" part.\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\nimport ImageUpload from './ImageUpload' // Component to handle uploads\nimport ImagePreview from './ImagePreview' // Component to display images\nimport { enhanceImage } from '../services/imageEnhancer' // Service for enhancement\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  const [enhancedImage, setEnhancedImage] = useState(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      // ... logic to prepare and send image for enhancement ...\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      <ImageUpload onImageUpload={handleImageUpload} /> {/* Upload area */}\n      <ImagePreview \n        originalImage={originalImage} \n        enhancedImage={enhancedImage}\n        isLoading={isLoading}\n      /> {/* Preview area */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\n`Home.jsx` is critical because it:\n*   Uses `useState` to keep track of the `originalImage` (what you uploaded), `enhancedImage` (the result from the AI), and `isLoading` (to show if the app is busy).\n*   Defines `handleImageUpload`, a function that will be called when you select an image. This function is responsible for sending the image to the enhancement service and updating the state with the results.\n*   Includes two other important components:\n    *   `ImageUpload`: This component provides the user interface for selecting an image. We'll explore it in detail in [Image Upload Interface](02_image_upload_interface_.md).\n    *   `ImagePreview`: This component displays both the original and enhanced images. We'll learn more about it in [Image Preview & Comparison Interface](03_image_preview___comparison_interface_.md).\n\n### The Frontend Core in Action: A Simple Flow\n\nLet's visualize how these pieces work together when you upload an image:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant Main as main.jsx\n    participant App as App.jsx\n    participant Home as Home.jsx\n    participant ImageUpload as ImageUpload.jsx\n    participant ImagePreview as ImagePreview.jsx\n\n    User->>Main: Launches App\n    Main->>App: Renders App component\n    App->>Home: Renders Home component (the core enhancement logic)\n    Home->>ImageUpload: Renders Image Upload Interface\n    Home->>ImagePreview: Renders Image Preview Interface\n\n    User->>ImageUpload: Selects an image file\n    ImageUpload->>Home: Calls onImageUpload(imageFile)\n    Home->>Home: Updates originalImage state\n    Home->>Home: Sets isLoading to true\n    Home->>ImagePreview: Passes updated states (originalImage, isLoading)\n    ImagePreview-->>User: Shows original image & loading indicator\n    Home->>Backend Proxy & API Gateway: Calls enhanceImage(imageFile) (via Image Enhancement Client Service)\n    Backend Proxy & API Gateway-->>Home: Returns enhanced image data\n    Home->>Home: Updates enhancedImage state\n    Home->>Home: Sets isLoading to false\n    Home->>ImagePreview: Passes updated states (originalImage, enhancedImage, isLoading)\n    ImagePreview-->>User: Shows original and enhanced images for comparison\n```\n\n1.  **Start-up:** When you launch the app, `main.jsx` starts `App.jsx`.\n2.  **Overall Layout:** `App.jsx` sets up the basic page layout and includes `Home.jsx`.\n3.  **Core Logic:** `Home.jsx` then sets up the `ImageUpload` area and the `ImagePreview` area.\n4.  **User Uploads:** When you choose an image, the `ImageUpload` component notifies `Home.jsx` about the new image.\n5.  **State Management:** `Home.jsx` takes this original image, saves it, and also sets a \"loading\" flag. It passes these pieces of information down to `ImagePreview` so you can see your original image and know the app is working.\n6.  **Enhancement Request:** `Home.jsx` then uses the `enhanceImage` function (which is part of our [Image Enhancement Client Service](04_image_enhancement_client_service_.md)) to send your image to the AI backend.\n7.  **Result Display:** Once the AI returns the enhanced image, `Home.jsx` updates its state again, including the `enhancedImage` and turning off the \"loading\" flag. This new information is then passed to `ImagePreview` so you can see the amazing transformation!\n\n### Conclusion\n\nIn this chapter, we've explored the \"Frontend Application Core\" of our AI Image Enhancer. We learned that it's the central control system that orchestrates the user's journey, from uploading an image to viewing the enhanced result. We saw how `main.jsx` kicks off the application, how `App.jsx` provides the main stage, and most importantly, how `Home.jsx` acts as the orchestrator, managing the flow and connecting the different parts of the image enhancement process.\n\nNext, we'll dive deeper into one of these connected parts: the **Image Upload Interface**. We'll learn how users can select and upload their photos to our application.\n\n[Next Chapter: Image Upload Interface](02_image_upload_interface_.md)\n---\n# Chapter 2: Image Upload Interface\n\nIn our previous chapter, [Frontend Application Core](01_frontend_application_core_.md), we learned that the `Home.jsx` component is the central \"orchestrator\" for managing the image enhancement process. It's like the conductor of an orchestra, making sure every part plays its role. One of the very first instruments in this orchestra is the **Image Upload Interface**.\n\n### What is the Image Upload Interface?\n\nImagine you want to send a letter. You don't just throw it into the air and hope it reaches its destination! You need a mailbox. In our `Simple-AI-Image-Enhancer` application, the **Image Upload Interface** is that special \"mailbox\" for your photos.\n\nThis is the specific part of the user interface where you interact to bring your image into the application. It's designed to be super easy to use:\n\n*   **Click to Browse:** You can simply click on the area, and a window will pop up, allowing you to select an image file from your computer, just like opening a document.\n*   **Drag and Drop:** Or, even simpler, you can just drag an image file directly from your computer's folders and \"drop\" it onto this area in the app.\n\nThe Image Upload Interface also has a smart guard dog: it makes sure that only valid image files (like JPGs or PNGs) are accepted. Once your image is safely inside, it then prepares it to be sent off for enhancement.\n\n### Our Goal: Bringing Your Image In\n\nOur main goal in this chapter is to understand how we can get your image file from your computer into our application, specifically into the `Home.jsx` component we talked about in Chapter 1.\n\nRecall from [Frontend Application Core](01_frontend_application_core_.md) that `Home.jsx` includes `ImageUpload` like this:\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\nimport ImageUpload from './ImageUpload' // Component to handle uploads\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  // ... other states ...\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      setOriginalImage(imageFile) // Save the image received from ImageUpload\n      // ... logic to send for enhancement ...\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      <ImageUpload onImageUpload={handleImageUpload} /> {/* This is our upload area! */}\n      {/* ... ImagePreview ... */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\nNotice the line `<ImageUpload onImageUpload={handleImageUpload} />`. This shows us that `Home.jsx` gives `ImageUpload` a special function called `handleImageUpload`. This is how `ImageUpload` will \"mail\" the selected image back to `Home.jsx`.\n\n### How It Works: The Journey of Your Image\n\nLet's trace what happens when you interact with the Image Upload Interface:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant ImageUpload as ImageUpload.jsx\n    participant Home as Home.jsx\n\n    User->>ImageUpload: Clicks or Drags & Drops image file\n    ImageUpload->>ImageUpload: Checks if it's a valid image (e.g., JPG, PNG)\n    alt If not a valid image\n        ImageUpload-->>User: Shows an \"Oops!\" message\n    else If valid image\n        ImageUpload->>Home: Calls onImageUpload(imageFile)\n        Home->>Home: Receives imageFile and prepares it for enhancement\n    end\n```\n\n1.  **You Interact:** You either click the upload area or drag an image file onto it.\n2.  **ImageUpload Catches It:** The `ImageUpload` component, which is the blueprint for our \"mailbox,\" \"catches\" the file.\n3.  **The Guard Dog Checks:** It immediately performs a quick check: \"Is this actually an image file?\" If it's not (e.g., you accidentally dropped a text file), it gently tells you.\n4.  **Delivering the Mail:** If it *is* a valid image, `ImageUpload` doesn't keep it. Instead, it takes that image file and \"mails\" it back to `Home.jsx` by calling the `onImageUpload` function that `Home.jsx` gave it.\n5.  **Home.jsx Takes Over:** Now `Home.jsx` has your image, ready to start the enhancement process!\n\n### Diving into the Code: `src/components/ImageUpload.jsx`\n\nLet's open up the `src/components/ImageUpload.jsx` file to see how this \"mailbox\" is built.\n\n#### 1. Remembering What's Happening with `useState`\n\nFirst, `ImageUpload.jsx` needs to remember a few things, like if you're currently dragging a file or what file you've selected. It uses `useState` for this.\n\n```javascript\n// src/components/ImageUpload.jsx\nimport React, { useState, useRef } from 'react'\n\nconst ImageUpload = ({ onImageUpload }) => {\n  const [isDragging, setIsDragging] = useState(false) // Is a file being dragged over the area?\n  const [fileName, setFileName] = useState('')       // What's the name of the file selected?\n  const fileInputRef = useRef(null)                   // A secret way to click a hidden file input\n\n  // ... rest of the component's code ...\n}\nexport default ImageUpload\n```\n\n*Explanation*: `useState` helps our component remember information that might change (like if `isDragging` is true or false). `useRef` is like having a direct \"handle\" to an actual part of the web page (in our case, a hidden file selector), so we can tell it to do things, like \"click yourself!\"\n\n#### 2. Handling Drag-and-Drop\n\nWhen you drag a file, your browser has its own default behavior (like trying to open the file). We need to stop that and tell our component what to do instead.\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component)\nconst handleDragOver = (e) => {\n  e.preventDefault() // Stop the browser from doing its own thing\n  setIsDragging(true) // Turn on the \"dragging\" style (e.g., blue border)\n}\n\nconst handleDragLeave = () => {\n  setIsDragging(false) // Turn off the \"dragging\" style\n}\n\nconst handleDrop = (e) => {\n  e.preventDefault() // Stop the browser\n  setIsDragging(false) // Turn off the \"dragging\" style\n  \n  if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n    const file = e.dataTransfer.files[0] // Get the dropped file\n    processFile(file) // Send it to our \"guard dog\" function\n  }\n}\n// ... rest of the component ...\n```\n\n*Explanation*: These three functions handle the drag-and-drop actions. `handleDragOver` and `handleDragLeave` simply change the look of our upload box to give you feedback (like making the border blue). When you finally `handleDrop` the file, we grab the file and send it to `processFile` to check if it's an image.\n\n#### 3. Handling Click-to-Upload\n\nNot everyone likes dragging and dropping! We also provide a way to click and browse your files.\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component)\nconst handleFileChange = (e) => {\n  if (e.target.files && e.target.files[0]) {\n    const file = e.target.files[0] // Get the selected file\n    processFile(file) // Send it to our \"guard dog\" function\n  }\n}\n\nconst handleClick = () => {\n  fileInputRef.current.click() // Secretly \"click\" the hidden file selection box\n}\n// ... rest of the component ...\n```\n\n*Explanation*: When you click anywhere on our upload box, `handleClick` makes the hidden file input (which normally opens the file selection window) pop up. Once you choose a file from that window, `handleFileChange` is activated, which then passes your chosen file to `processFile`.\n\n#### 4. The \"Guard Dog\" and Mailman: `processFile`\n\nThis is a crucial function that acts as both the validator and the delivery person.\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component)\nconst processFile = (file) => {\n  // Is it an image?\n  if (!file.type.match('image.*')) { // Checks if the file type starts with 'image/'\n    alert('Oops! Please select an image file (like JPG, PNG, WEBP).')\n    return // Stop right here if it's not an image\n  }\n  \n  setFileName(file.name) // Display the file's name in the UI\n  onImageUpload(file)    // This is where we \"mail\" the file back to Home.jsx!\n}\n// ... rest of the component ...\n```\n\n*Explanation*: `processFile` first checks if the file's `type` matches an image format. If not, it shows an alert. If it *is* an image, it updates the `fileName` to be displayed and then, most importantly, calls `onImageUpload(file)`. This is the exact moment the `ImageUpload` component hands off the selected image file to `Home.jsx` for further processing.\n\n#### 5. The Visual \"Mailbox\": JSX Structure\n\nFinally, all these pieces come together in the visual part of the component, which uses JSX (a mix of JavaScript and HTML-like tags).\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component's return statement)\nreturn (\n  <div className='bg-white dark:bg-gray-800 shadow-lg rounded-2xl w-full max-w-2xl p-6'>\n    <div \n      className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-all cursor-pointer\n        ${isDragging ? 'border-blue-500 bg-blue-50 dark:bg-blue-900' : 'border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400'}`}\n      onDragOver={handleDragOver}   // What happens when dragging over\n      onDragLeave={handleDragLeave} // What happens when dragging leaves\n      onDrop={handleDrop}           // What happens when dropped\n      onClick={handleClick}         // What happens when clicked\n    >\n      <input \n        type=\"file\" \n        id=\"fileInput\" \n        ref={fileInputRef}     // Connects to our secret handle\n        className='hidden'     // Makes the default file input invisible\n        accept=\"image/*\"       // Only allows image files in the selection window\n        onChange={handleFileChange} // What happens when a file is selected\n      />\n      \n      <div className=\"flex flex-col items-center justify-center py-4\">\n        {/* An SVG icon to make it look nice */}\n        <p className='text-lg font-medium'>\n          {fileName ? `Selected: ${fileName}` : 'Click or drag to upload an image'}\n        </p>\n        <p className='text-sm text-gray-500'>\n          Supports JPG, PNG, WEBP (Max 10MB)\n        </p>\n      </div>\n    </div>\n  </div>\n)\n```\n\n*Explanation*: This is what you see! It's a nicely styled `div` (our \"mailbox\" container). It listens for your actions like `onDragOver`, `onDragLeave`, `onDrop`, and `onClick`, connecting them to the functions we just discussed. Inside it, there's a `<input type=\"file\" />` that's kept `hidden` and controlled by our JavaScript. The text dynamically changes to show the `fileName` once an image is selected, making the interface clear and user-friendly.\n\n### Conclusion\n\nIn this chapter, we've explored the **Image Upload Interface**, the crucial first step in our image enhancement journey. We learned how this component acts as a user-friendly \"mailbox,\" allowing you to easily bring your images into the application using either click-to-browse or drag-and-drop. We also saw how it validates files and, most importantly, \"mails\" the selected image back to `Home.jsx` via the `onImageUpload` function.\n\nNow that we know how to get an image *into* the app, the next logical step is to see it! In the next chapter, we'll dive into the **Image Preview & Comparison Interface**, where you'll see your original image and, eventually, its beautifully enhanced counterpart.\n\n[Next Chapter: Image Preview & Comparison Interface](03_image_preview___comparison_interface_.md)\n---\n# Chapter 3: Image Preview & Comparison Interface\n\nIn our previous chapter, [Image Upload Interface](02_image_upload_interface.md), we learned how our application provides a user-friendly \"mailbox\" for you to bring your images into the system. Now that your image has been successfully \"mailed\" into the app, where does it go next? It needs a place to be seen!\n\nThis is where the **Image Preview & Comparison Interface** comes in.\n\n### What is the Image Preview & Comparison Interface?\n\nImagine you're looking at a photo album. When you first upload a picture, you want to see that picture immediately. That's the first job of this interface: it acts like a digital \"photo album\" displaying your original image as soon as it's ready.\n\nBut it gets even better! Once our clever AI has worked its magic and enhanced your image, this interface transforms into a super-cool \"compare tool.\" It lets you see your original image right next to the new, improved version. You can even slide a bar across the image to smoothly switch between the \"before\" and \"after\" views, helping you truly appreciate the enhancement.\n\nAnd of course, once you're happy with the result, it gives you an easy way to \"take home\" your newly enhanced masterpiece by providing a convenient download button.\n\n### Our Goal: Showing Off Your Images\n\nOur main goal in this chapter is to understand how the `ImagePreview` component works to display your pictures at different stages of the enhancement process – from just being uploaded, to waiting for AI, and finally, showcasing the amazing before-and-after transformation.\n\nRecall from [Frontend Application Core](01_frontend_application_core.md) that `Home.jsx` is the orchestrator, and it uses `ImagePreview` by passing it important information:\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\n// ... other imports ...\nimport ImagePreview from './ImagePreview' // Our preview component\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  const [enhancedImage, setEnhancedImage] = useState(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  // ... handleImageUpload function (updates originalImage, sets isLoading) ...\n  // ... logic to call enhancement service (updates enhancedImage, sets isLoading false) ...\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      {/* ... ImageUpload component ... */}\n      <ImagePreview \n        originalImage={originalImage} \n        enhancedImage={enhancedImage}\n        isLoading={isLoading}\n      /> {/* This is our preview area! */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\n`Home.jsx` passes three key pieces of information (called \"props\") to `ImagePreview`:\n*   `originalImage`: The image you just uploaded.\n*   `enhancedImage`: The amazing picture after the AI has worked on it.\n*   `isLoading`: A true/false switch that tells `ImagePreview` if the AI is currently busy enhancing your image.\n\n### How It Works: The Image's Display Journey\n\nLet's trace how your image appears and changes in the `ImagePreview` interface:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant Home as Home.jsx\n    participant ImagePreview as ImagePreview.jsx\n\n    User->>Home: Uploads image\n    Home->>ImagePreview: Passes originalImage, isLoading=true\n    ImagePreview-->>User: Shows a \"Please Wait...\" screen (Loading State)\n\n    Home->>Home: Sends image for enhancement (via Image Enhancement Client Service)\n    Home->>Home: Receives enhanced image\n    Home->>ImagePreview: Passes originalImage, enhancedImage, isLoading=false\n\n    alt If original exists, but no enhanced yet (e.g., after initial upload, before enhancement result)\n        ImagePreview-->>User: Shows ONLY the original image (Initial Preview State)\n    else If both original and enhanced images are ready\n        ImagePreview-->>User: Shows the interactive Before/After comparison (Comparison State)\n    end\n```\n\n1.  **Original Arrives, AI is Busy**: As soon as you upload an image, `Home.jsx` sends your `originalImage` to `ImagePreview` and also tells it `isLoading` is `true`. `ImagePreview` immediately puts up a \"Please Wait...\" sign.\n2.  **AI Finishes**: Once the AI processing is done, `Home.jsx` updates `ImagePreview` with the `enhancedImage` and sets `isLoading` to `false`.\n3.  **The Big Reveal**: Now `ImagePreview` has both the original and enhanced images, and it switches from the \"Please Wait...\" screen to the cool comparison tool, letting you slide between them.\n\n### Diving into the Code: `src/components/ImagePreview.jsx`\n\nLet's look inside the `src/components/ImagePreview.jsx` file to see how it manages these different display \"scenarios.\"\n\n#### 1. The Component's \"Brain\": Receiving Information\n\nFirst, the `ImagePreview` component gets the `originalImage`, `enhancedImage`, and `isLoading` information from `Home.jsx`.\n\n```javascript\n// src/components/ImagePreview.jsx\nimport React from 'react';\nimport { ReactCompareSlider, ReactCompareSliderImage } from 'react-compare-slider';\nimport Loading from './Loading'; // A small component to show \"loading\"\n\nconst ImagePreview = ({ originalImage, enhancedImage, isLoading }) => {\n    // This component will change what it shows based on these three pieces of info!\n\n    // ... The rest of the logic (different display scenarios) ...\n};\n\nexport default ImagePreview;\n```\n*Explanation*: The `ImagePreview` component is a \"smart display.\" It listens to the `originalImage`, `enhancedImage`, and `isLoading` signals from `Home.jsx` to decide what to show on your screen.\n\n#### 2. Scenario 1: The \"Please Wait...\" Screen\n\nIf the `isLoading` signal is `true`, it means our AI is busy working its magic. We want to show a friendly loading animation.\n\n```javascript\n// src/components/ImagePreview.jsx (inside the component)\n    if (isLoading) {\n        return (\n            <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl flex justify-center items-center min-h-[300px]\">\n                <Loading /> {/* Our spinning wheel or message */}\n            </div>\n        );\n    }\n```\n*Explanation*: If `isLoading` is `true`, the component immediately stops here and shows a `Loading` animation (which is a separate, simple spinning wheel component). This tells you that the app is busy and you should wait.\n\n#### 3. Scenario 2: The \"First Look\" – Only Original Image\n\nWhat if you've uploaded an image, but the AI hasn't finished enhancing it yet (or perhaps it failed)? In this case, we still want to show your original picture, even if the enhanced one isn't ready.\n\n```javascript\n// src/components/ImagePreview.jsx (inside the component)\n    // If not loading, and original image exists, but no enhanced image yet:\n    if (originalImage && !enhancedImage && !isLoading) {\n         return (\n             <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl\">\n                <h2 className=\"text-xl font-semibold text-center ...\">Original Image</h2>\n                <img \n                    src={originalImage} \n                    alt=\"Original Uploaded\" \n                    className=\"max-w-full h-auto rounded mx-auto\" \n                    style={{ maxHeight: '60vh' }} \n                />\n            </div>\n         );\n    }\n```\n*Explanation*: This block checks if we have an `originalImage`, but `enhancedImage` is `null` (meaning it's not ready yet), and we're not currently `isLoading`. If all these are true, it simply displays your `originalImage` along with a \"Original Image\" title.\n\n#### 4. Scenario 3: The \"Big Reveal\" – Before & After Comparison\n\nThis is the main event! If we have both `originalImage` AND `enhancedImage`, it's time to show them side-by-side using a special comparison tool.\n\n```javascript\n// src/components/ImagePreview.jsx (inside the component)\n    // If both original and enhanced images are available:\n    if (originalImage && enhancedImage) {\n        return (\n             <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl\">\n                 <h2 className=\"text-xl font-semibold text-center ...\">Compare Images</h2>\n                 <ReactCompareSlider\n                     style={{ height: '70vh', width: '100%', margin: '0 auto' }}\n                    itemOne={\n                         <ReactCompareSliderImage src={originalImage} alt=\"Original Image\" />\n                     }\n                    itemTwo={\n                            <ReactCompareSliderImage src={enhancedImage} alt=\"Enhanced Image\" />\n                     }\n                />\n                 <div className=\"text-center mt-4\">\n                        <a\n                          href={enhancedImage} // Link to the enhanced image\n                          download=\"enhanced-image.png\" // Suggested filename\n                          className=\"inline-block bg-blue-500 hover:bg-blue-700 ... \"\n                        >\n                            Download Enhanced Image\n                        </a>\n                 </div>\n            </div>\n        );\n    }\n```\n*Explanation*:\n*   **`ReactCompareSlider`**: This is a powerful little tool (a \"third-party library\") that makes the cool sliding comparison effect. We give it `itemOne` (our `originalImage`) and `itemTwo` (our `enhancedImage`), and it magically creates the slider.\n*   **Download Button**: Below the slider, there's a simple `<a>` (link) tag. The `href` points directly to the `enhancedImage` data, and the `download` attribute tells your browser to save it as `enhanced-image.png` when you click it. It's like pressing \"Save\" on your masterpiece!\n\n#### 5. Fallback: Nothing to Show\n\nFinally, if none of the above conditions are met (e.g., no image has been uploaded yet, and it's not loading), the component simply returns nothing.\n\n```javascript\n// src/components/ImagePreview.jsx (inside the component)\n    // Fallback case (e.g., no images uploaded yet)\n    return null;\n}; // End of the ImagePreview component\n```\n*Explanation*: This ensures that if there's no relevant image data, our `ImagePreview` area remains empty and tidy, waiting for you to upload a picture.\n\n### Conclusion\n\nIn this chapter, we've explored the **Image Preview & Comparison Interface**, the \"photo album\" and \"compare tool\" of our application. We learned how the `ImagePreview` component intelligently displays your images at different stages: showing a loading screen when the AI is busy, displaying just the original image when first uploaded, and finally, transforming into an interactive before-and-after slider with a handy download button once the enhancement is complete.\n\nNow that we know how images are displayed, the next logical step is to understand how the application *requests* the enhancement from the AI. In the next chapter, we'll dive into the **Image Enhancement Client Service**, the part of our app that talks to the powerful AI backend.\n\n[Next Chapter: Image Enhancement Client Service](04_image_enhancement_client_service.md)\n---\n# Chapter 4: Image Enhancement Client Service\n\nIn our previous chapter, [Image Preview & Comparison Interface](03_image_preview___comparison_interface.md), we learned how our application displays images, from the initial upload to the final, amazing before-and-after comparison. But for that comparison to happen, we first need the *enhanced* image!\n\nWhere does that enhanced image come from? It comes from our powerful AI backend, which is like a super-smart artist living in a different city. Our frontend (the part of the app you see and interact with) needs a way to send your image to this artist and then patiently wait for the masterpiece to be sent back. This is where the **Image Enhancement Client Service** steps in.\n\n### What is the Image Enhancement Client Service?\n\nImagine you want to order a custom painting from an artist far away. You wouldn't just shout your request across the country! You'd hire a reliable **messenger service** or a **personal assistant** to handle all the tricky communication.\n\nIn our `Simple-AI-Image-Enhancer` application, the **Image Enhancement Client Service** is exactly that: it's the dedicated \"messenger\" on the frontend side. Its main job is to communicate with our backend server to get your images enhanced. It's the one who:\n\n1.  **Sends your original image** to the AI artist (the backend).\n2.  **Repeatedly checks in** with the artist to ask, \"Is it done yet?\" (We call this \"polling\").\n3.  **Receives the final enhanced image URL** once it's ready.\n\nBy handling all these complex steps, our \"messenger\" makes life easy for the main parts of our app (like `Home.jsx`), allowing them to focus on just *showing* you the images, not worrying about *how* they get enhanced.\n\n### Our Goal: Getting the Enhanced Image\n\nOur main goal in this chapter is to understand how our app uses this \"messenger\" service to send an image for enhancement and then patiently wait for the result. Specifically, we'll look at the `enhanceImage` function that `Home.jsx` calls when you upload a photo.\n\nRecall from [Frontend Application Core](01_frontend_application_core.md) that `Home.jsx` uses this service like so:\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\n// ... other imports ...\nimport { enhanceImage } from '../services/imageEnhancer' // Our \"messenger\" service\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  const [enhancedImage, setEnhancedImage] = useState(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      setOriginalImage(URL.createObjectURL(imageFile)); // For immediate preview\n      setIsLoading(true); // Show loading spinner\n      try {\n        const enhancedResult = await enhanceImage(imageFile); // HERE'S THE CALL!\n        setEnhancedImage(enhancedResult.enhanced_url); // Get the enhanced image URL\n      } catch (error) {\n        console.error(\"Enhancement failed:\", error);\n        // Handle error, maybe show an error message to the user\n      } finally {\n        setIsLoading(false); // Hide loading spinner\n      }\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      {/* ... ImageUpload and ImagePreview components ... */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\nThe line `const enhancedResult = await enhanceImage(imageFile);` is where `Home.jsx` hands off the image to our service and patiently `await`s the result.\n\n### How It Works: The Messenger's Journey\n\nLet's visualize how our \"messenger\" (the Image Enhancement Client Service) handles getting your image enhanced:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant Home as Home.jsx\n    participant ClientService as ImageEnhancerClientService\n    participant Backend as Backend Proxy & API Gateway\n\n    User->>Home: Uploads Image\n    Home->>ClientService: Calls enhanceImage(imageFile)\n    ClientService->>Backend: 1. Uploads original image\n    Backend-->>ClientService: Returns a Task ID (like a tracking number)\n    loop Check Status Repeatedly (Polling)\n        ClientService->>Backend: 2. Asks for status of Task ID\n        Backend-->>ClientService: Returns current status (e.g., \"processing\", \"done\")\n        alt If still processing\n            ClientService: Waits a few seconds\n        else If done\n            break\n        end\n    end\n    Backend-->>ClientService: Returns Enhanced Image URL\n    ClientService-->>Home: Returns Enhanced Image URL\n    Home-->>User: Displays enhanced image\n```\n\n1.  **User Uploads**: You select an image, and `Home.jsx` gets it.\n2.  **Hand-off to Messenger**: `Home.jsx` then calls `enhanceImage` in our `ClientService`, giving it your image.\n3.  **Messenger Uploads**: The `ClientService` first sends your image to the `Backend`. The `Backend` gives back a `Task ID` – a unique tracking number for your enhancement job.\n4.  **Messenger Polls (Checks Status)**: The `ClientService` then uses this `Task ID` to repeatedly ask the `Backend`: \"Is task `[Task ID]` done yet?\"\n5.  **Backend Responds**: The `Backend` responds, either saying \"Still working!\" or \"It's done! Here's the URL for your enhanced image!\"\n6.  **Messenger Delivers**: Once the `Backend` says it's done and gives the URL, the `ClientService` delivers that URL back to `Home.jsx`.\n7.  **Display**: `Home.jsx` then updates its state, and `ImagePreview` shows your beautifully enhanced image!\n\n### Diving into the Code: `src/services/imageEnhancer.js`\n\nAll the magic for our \"messenger\" service happens in the `src/services/imageEnhancer.js` file. Let's break it down.\n\n#### 1. The Main Messenger Function: `enhanceImage`\n\nThis is the central function that `Home.jsx` calls. It orchestrates the entire process: first uploading the image and then repeatedly checking for the result.\n\n```javascript\n// src/services/imageEnhancer.js\nimport axios from \"axios\"; // A library to easily send web requests\n\n// Our backend's address and how many times we'll check\nconst BASE_URL = \"http://localhost:3001\";\nconst MAXIMUM_RETRIES = 20;\n\nexport const enhanceImage = async (file) => {\n    try {\n        const taskId = await uploadImage(file); // Step 1: Send the image & get a task ID\n        console.log(\"Image Uploaded, Task ID:\", taskId);\n\n        // Step 2: Keep asking the backend until the enhancement is ready\n        const enhancedImageData = await PollForEnhancedImage(taskId);\n        console.log(\"Enhanced Image Data Received:\", enhancedImageData);\n\n        return enhancedImageData; // Give the enhanced image URL back to Home.jsx\n    } catch (error) {\n        // If anything goes wrong, log it and let Home.jsx know\n        console.error(\"Error enhancing image:\", error.message);\n        throw error;\n    }\n};\n```\n*Explanation*: The `enhanceImage` function is like the project manager. It calls `uploadImage` to start the process and get a `taskId`. Then, it hands that `taskId` over to `PollForEnhancedImage`, which is responsible for waiting until the job is done. Once `PollForEnhancedImage` finishes, it returns the final enhanced image data.\n\n#### 2. Step 1: Sending the Image (`uploadImage`)\n\nThis function is responsible for taking your image file and sending it to our backend for processing.\n\n```javascript\n// src/services/imageEnhancer.js (inside imageEnhancer.js)\nconst uploadImage = async (file) => {\n    const formData = new FormData();\n    formData.append(\"image_file\", file); // Prepare the image to be sent\n\n    // Send the image to our backend's enhancement endpoint\n    const { data } = await axios.post(\n        `${BASE_URL}/api/enhance`, // The specific web address for enhancement\n        formData, // The image data\n        // Headers are automatically set for FormData, no API key needed here\n    );\n\n    // The backend should give us a task ID right away\n    if (!data?.taskId) {\n        throw new Error(\"Failed to get Task ID from backend!\");\n    }\n    return data.taskId; // Return the unique ID for this enhancement job\n};\n```\n*Explanation*: `uploadImage` uses `FormData` to bundle your image file in a way that can be sent over the internet. It then uses `axios.post` to send this bundle to the backend at the `/api/enhance` address. The backend immediately responds with a `taskId`, which is like a unique tracking number for your specific enhancement request.\n\n#### 3. Step 2: Repeatedly Asking for Updates (`PollForEnhancedImage`)\n\nThis is where the \"polling\" happens. This function repeatedly asks the backend for the status of your enhancement job using the `taskId`.\n\n```javascript\n// src/services/imageEnhancer.js (inside imageEnhancer.js)\nconst PollForEnhancedImage = async (taskId, retries = 0) => {\n    const result = await fetchEnhancedImage(taskId); // Ask for the latest status\n\n    // Our backend might use 'state: 4' to mean \"still processing\"\n    if (result.state === 4) {\n        console.log(`Image processing... (Attempt ${retries + 1} of ${MAXIMUM_RETRIES})`);\n\n        if (retries >= MAXIMUM_RETRIES) {\n            throw new Error(\"Enhancement took too long. Please try again.\");\n        }\n\n        // Wait for 2 seconds before asking again\n        await new Promise((resolve) => setTimeout(resolve, 2000));\n\n        // Call ourselves again to check the status after waiting\n        return PollForEnhancedImage(taskId, retries + 1);\n    }\n\n    // If 'state' is not 4, it means it's done (or failed, which backend handles)\n    return result; // This contains the enhanced image URL if successful\n};\n```\n*Explanation*: `PollForEnhancedImage` calls `fetchEnhancedImage` to get the status. If the status says `state: 4` (meaning \"processing\"), it prints a message, waits for 2 seconds using `setTimeout`, and then calls *itself* (`PollForEnhancedImage`) again to check the status. It does this over and over until the `state` is no longer `4` (meaning it's done!) or it hits the `MAXIMUM_RETRIES` limit.\n\n#### 4. Step 3: Getting the Latest Status (`fetchEnhancedImage`)\n\nThis is the simplest part of the polling process: it just asks the backend for the current status of a given `taskId`.\n\n```javascript\n// src/services/imageEnhancer.js (inside imageEnhancer.js)\nconst fetchEnhancedImage = async (taskId) => {\n    // Send a request to our backend's status endpoint with the task ID\n    const { data } = await axios.get(\n        `${BASE_URL}/api/status/${taskId}`, // The specific web address for status checks\n    );\n\n    if (!data) {\n        throw new Error(\"Did not receive status data from backend!\");\n    }\n    // The backend provides an object with status information (e.g., state, or enhanced_url)\n    return data;\n};\n```\n*Explanation*: `fetchEnhancedImage` uses `axios.get` to send a request to the backend's `/api/status/[taskId]` address. The backend responds with an object that tells us the current `state` of the enhancement job (e.g., if it's still processing, or if it's done and provides the enhanced image URL).\n\n### Conclusion\n\nIn this chapter, we've explored the **Image Enhancement Client Service**, our application's dedicated \"messenger\" for talking to the AI backend. We learned how this service takes your original image, sends it for enhancement, and then intelligently \"polls\" (repeatedly checks) the backend for updates until the enhanced image is ready. This powerful abstraction makes the complex communication process simple and reliable for the rest of our frontend application.\n\nNow that we understand how the frontend talks to the backend, it's time to see what happens on the *other side* of the conversation! In the next chapter, we'll dive into the **Backend Proxy & API Gateway**, which is the server that receives our messenger's requests and forwards them to the actual AI.\n\n[Next Chapter: Backend Proxy & API Gateway](05_backend_proxy___api_gateway_.md)\n\nRelevant Code Snippets (Code itself remains unchanged):\n--- File: backend/.env.example ---\n# API Key for the external image enhancement service\nEXTERNAL_API_KEY=YOUR_API_KEY_HERE\n\n# Optional: Port for the backend server (defaults to 3001 if not set)\nPORT=3001\n\n--- File: backend/package.json ---\n{\n  \"name\": \"backend\",\n  \"version\": \"1.0.0\",\n  \"main\": \"index.js\",\n  \"scripts\": {\n    \"test\": \"echo \\\"Error: no test specified\\\" && exit 1\",\n    \"start\": \"node server.js\"\n  },\n  \"keywords\": [],\n  \"author\": \"\",\n  \"license\": \"ISC\",\n  \"description\": \"\",\n  \"dependencies\": {\n    \"axios\": \"^1.8.4\",\n    \"cors\": \"^2.8.5\",\n    \"dotenv\": \"^16.4.7\",\n    \"express\": \"^5.1.0\",\n    \"multer\": \"^1.4.5-lts.2\"\n  }\n}\n\n\n--- File: backend/server.js ---\nrequire('dotenv').config();\nconst express = require('express');\nconst axios = require('axios');\nconst multer = require('multer');\nconst cors = require('cors');\n\nconst app = express();\nconst port = process.env.PORT || 3001; // Use port from env or default to 3001\n\n// --- External API Configuration ---\nconst EXTERNAL_API_KEY = process.env.EXTERNAL_API_KEY;\nconst EXTERNAL_BASE_URL = \"https://techhk.aoscdn.com/\";\nconst MAXIMUM_RETRIES = 20; // As defined in the original frontend code\n\nif (!EXTERNAL_API_KEY) {\n    console.error(\"FATAL ERROR: EXTERNAL_API_KEY is not defined in the environment variables.\");\n}\n\n\n// --- Middleware ---\napp.use(cors());\napp.use(express.json());\n\n// Configure Multer for file uploads (using memory storage for simplicity)\nconst storage = multer.memoryStorage();\nconst upload = multer({ storage: storage });\n\n// --- API Endpoints ---\n\n\n// POST /api/enhance - Receives image, uploads to external API, returns task ID\napp.post('/api/enhance', upload.single('image_file'), async (req, res, next) => {\n    console.log(\"Received file:\", req.file?.originalname);\n\n    if (!req.file) {\n        return res.status(400).json({ message: 'No image file uploaded.' });\n    }\n\n    if (!EXTERNAL_API_KEY) {\n        console.error(\"API Key missing in backend configuration.\");\n        return res.status(500).json({ message: 'Server configuration error.' });\n    }\n\n    const formData = new FormData();\n    // Convert buffer to Blob before appending\n    const imageBlob = new Blob([req.file.buffer], { type: req.file.mimetype });\n    formData.append('image_file', imageBlob, req.file.originalname);\n\n    try {\n        console.log(`Uploading ${req.file.originalname} to external API...`);\n        const { data } = await axios.post(\n            `${EXTERNAL_BASE_URL}/api/tasks/visual/scale`,\n            formData,\n            {\n                headers: {\n                    'X-API-KEY': EXTERNAL_API_KEY,\n                    ...formData.getHeaders?.() // Necessary for Axios with FormData in Node.js\n                },\n                maxBodyLength: Infinity, // Handle large file uploads\n                maxContentLength: Infinity\n            }\n        );\n\n        console.log(\"External API Response:\", data);\n\n        if (!data?.data?.task_id) {\n            console.error(\"Failed to get task_id from external API response:\", data);\n            throw new Error(\"Failed to upload image to external service! Task ID not found.\");\n        }\n\n        const taskId = data.data.task_id;\n        console.log(\"Image Uploaded Successfully, Task ID:\", taskId);\n        res.json({ taskId: taskId });\n\n    } catch (error) {\n        console.error(\"Error in /api/enhance:\", error.response?.data || error.message);\n        // Forward specific error message if available, otherwise generic\n        const errorMessage = error.response?.data?.message || error.message || 'Failed to process image enhancement request.';\n        const statusCode = error.response?.status || 500;\n        res.status(statusCode).json({ message: errorMessage });\n    }\n});\n\n// GET /api/status/:taskId - Polls external API for enhancement status/result\napp.get('/api/status/:taskId', async (req, res) => {\n    const { taskId } = req.params;\n    console.log(\"Checking status for task:\", taskId);\n\n    if (!EXTERNAL_API_KEY) {\n        console.error(\"API Key missing in backend configuration.\");\n        return res.status(500).json({ message: 'Server configuration error.' });\n    }\n\n    try {\n        const { data } = await axios.get(\n            `${EXTERNAL_BASE_URL}/api/tasks/visual/scale/${taskId}`,\n            {\n                headers: {\n                    'X-API-KEY': EXTERNAL_API_KEY,\n                },\n            }\n        );\n\n        console.log(`Status for task ${taskId}:`, data.data?.state, data.data?.image_url);\n\n        if (!data?.data) {\n             console.error(\"No data found for task:\", taskId, \"Response:\", data);\n            // It's possible the task isn't ready or doesn't exist, return appropriate status\n             return res.status(404).json({ message: 'Task status not found or task not yet processed.' });\n        }\n\n        // Return the relevant data from the external API response\n        // The frontend will handle the polling logic based on the 'state'\n        res.json(data.data);\n\n    } catch (error) {\n        console.error(`Error fetching status for task ${taskId}:`, error.response?.data || error.message);\n        const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch task status.';\n        const statusCode = error.response?.status || 500;\n        res.status(statusCode).json({ message: errorMessage });\n    }\n});\n\n// --- Basic Error Handling ---\napp.use((err, req, res, next) => {\n    console.error(err.stack);\n    res.status(500).send('Something broke!');\n});\n\n// --- Start Server ---\napp.listen(port, () => {\n    console.log(`Backend server listening at http://localhost:${port}`);\n});\n\nInstructions for the chapter (Generate content in English unless specified otherwise):\n- Start with a clear heading (e.g., `# Chapter 5: Backend Proxy & API Gateway\n`). Use the provided concept name.\n\n- If this is not the first chapter, begin with a brief transition from the previous chapter, referencing it with a proper Markdown link using its name.\n\n- Begin with a high-level motivation explaining what problem this abstraction solves. Start with a central use case as a concrete example. The whole chapter should guide the reader to understand how to solve this use case. Make it very minimal and friendly to beginners.\n\n- If the abstraction is complex, break it down into key concepts. Explain each concept one-by-one in a very beginner-friendly way.\n\n- Explain how to use this abstraction to solve the use case. Give example inputs and outputs for code snippets (if the output isn't values, describe at a high level what will happen).\n\n- Each code block should be BELOW 10 lines! If longer code blocks are needed, break them down into smaller pieces and walk through them one-by-one. Aggresively simplify the code to make it minimal. Use comments to skip non-important implementation details. Each code block should have a beginner friendly explanation right after it.\n\n- Describe the internal implementation to help understand what's under the hood. First provide a non-code or code-light walkthrough on what happens step-by-step when the abstraction is called. It's recommended to use a simple sequenceDiagram with a dummy example - keep it minimal with at most 5 participants to ensure clarity. If participant name has space, use: `participant QP as Query Processing`. .\n\n- Then dive deeper into code for the internal implementation with references to files. Provide example code blocks, but make them similarly simple and beginner-friendly. Explain.\n\n- IMPORTANT: When you need to refer to other core abstractions covered in other chapters, ALWAYS use proper Markdown links like this: [Chapter Title](filename.md). Use the Complete Tutorial Structure above to find the correct filename and the chapter title. Translate the surrounding text.\n\n- Use mermaid diagrams to illustrate complex concepts (```mermaid``` format). .\n\n- Heavily use analogies and examples throughout to help beginners understand.\n\n- End the chapter with a brief conclusion that summarizes what was learned and provides a transition to the next chapter. If there is a next chapter, use a proper Markdown link: [Next Chapter Title](next_chapter_filename).\n\n- Ensure the tone is welcoming and easy for a newcomer to understand.\n\n- Output *only* the Markdown content for this chapter.\n\nNow, directly provide a super beginner-friendly Markdown output (DON'T need ```markdown``` tags):\n": "# Chapter 5: Backend Proxy & API Gateway\n\nIn our previous chapter, [Image Enhancement Client Service](04_image_enhancement_client_service.md), we explored how our frontend application acts as a \"messenger\" to send your image to an AI service and then patiently waits for the enhanced result. But where does this messenger *really* send the image? It doesn't send it directly to the super-secret AI service!\n\nInstead, it sends it to our own custom \"middleman\" server, which we call the **Backend Proxy & API Gateway**. This is the behind-the-scenes hero that handles all the sensitive and complex communication with the powerful (and external) AI image enhancement service.\n\n### What is the Backend Proxy & API Gateway?\n\nImagine you have a very important, secret message to send to a high-security vault. You wouldn't just write your message on a postcard and mail it directly! You'd give it to a trusted, highly secure **courier service** or a **special agent**. This agent knows how to navigate the security, add necessary secret codes, and make sure the message gets delivered properly.\n\nIn our `Simple-AI-Image-Enhancer` application, the **Backend Proxy & API Gateway** plays this special agent role:\n\n*   **Security Guard / \"Bouncer\":** It stands between our user-facing frontend (what you see) and the external AI image enhancement service. When you upload an image, the frontend sends it here first. This ensures that the frontend never has to know or store your secret API key for the external service. Only our backend knows it, keeping it safe!\n*   **Translator & Forwarder:** It takes the image and requests from our frontend and translates them into the specific language and format that the external AI service understands. Then, it securely adds your secret API key (which the frontend doesn't see) and forwards the image to the external AI service.\n*   **Result Retriever & Deliverer:** It doesn't just send the image and forget it. It also handles polling the external service for the enhancement status (just like our frontend polls *it*). Once the enhanced image is ready, it fetches it and sends the results back to our frontend.\n\nSo, it's a central hub that simplifies communication, adds a layer of security, and manages the entire back-and-forth with the external AI provider.\n\n### Our Goal: Securely Enhancing an Image\n\nOur main goal in this chapter is to understand how the Backend Proxy & API Gateway receives an image from our frontend, securely communicates with an external AI service to get it enhanced, and then delivers the result back.\n\nRecall from [Image Enhancement Client Service](04_image_enhancement_client_service.md) that our frontend's `enhanceImage` function makes two main calls to our backend:\n\n1.  A `POST` request to `/api/enhance` to send the image.\n2.  Repeated `GET` requests to `/api/status/:taskId` to check the enhancement progress.\n\nThe Backend Proxy & API Gateway is designed to handle both of these requests.\n\n### How It Works: The Journey Through the Gateway\n\nLet's visualize the entire process, including our new \"special agent\" in the middle:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant Frontend as Frontend App\n    participant BackendProxy as Backend Proxy & API Gateway\n    participant ExternalAI as External AI Service\n\n    User->>Frontend: 1. Uploads Image\n    Frontend->>BackendProxy: 2. Sends original image (POST /api/enhance)\n    BackendProxy->>BackendProxy: 3. Adds secret API key\n    BackendProxy->>ExternalAI: 4. Forwards image with API key\n    ExternalAI-->>BackendProxy: 5. Returns Task ID\n    BackendProxy-->>Frontend: 6. Returns Task ID\n\n    loop Polling for Status\n        Frontend->>BackendProxy: 7. Requests status for Task ID (GET /api/status/:taskId)\n        BackendProxy->>ExternalAI: 8. Requests status for Task ID\n        ExternalAI-->>BackendProxy: 9. Returns current status (e.g., \"processing\", \"done\")\n        alt If still processing\n            BackendProxy-->>Frontend: 10. Returns \"processing\" status\n            Frontend: Waits & requests again\n        else If done\n            BackendProxy-->>Frontend: 10. Returns \"done\" status & Enhanced Image URL\n            break\n        end\n    end\n    Frontend-->>User: 11. Displays enhanced image\n```\n\n1.  **User Uploads:** You select an image on the frontend.\n2.  **Frontend Sends to Proxy:** The [Image Enhancement Client Service](04_image_enhancement_client_service.md) sends your image to our backend at `/api/enhance`.\n3.  **Proxy Adds Key:** Our Backend Proxy receives the image. *Crucially*, it then adds the `EXTERNAL_API_KEY` (which only it knows).\n4.  **Proxy Forwards to External AI:** With the API key, the proxy forwards the image to the actual `External AI Service`.\n5.  **External AI Responds with Task ID:** The `External AI Service` starts processing and immediately gives our proxy a `task_id` (a tracking number).\n6.  **Proxy Returns Task ID to Frontend:** Our proxy sends this `task_id` back to the frontend.\n7.  **Frontend Polls Proxy:** The frontend repeatedly asks our proxy: \"What's the status of `[Task ID]`?\" (via `/api/status/:taskId`).\n8.  **Proxy Polls External AI:** For each request from the frontend, our proxy then asks the `External AI Service` for the *real* status.\n9.  **External AI Responds with Status:** The `External AI Service` tells our proxy if it's still processing or if it's done.\n10. **Proxy Returns Status to Frontend:** Our proxy passes this status (and the enhanced image URL when ready) back to the frontend.\n11. **Frontend Displays:** Once the frontend gets the enhanced image URL, it displays the result to you!\n\n### Diving into the Code: `backend/server.js`\n\nAll of this \"special agent\" work happens in our `backend/server.js` file. This file uses a technology called `Express.js` to create our web server and define these \"routes\" (the specific addresses like `/api/enhance`).\n\nBefore we look at the routes, let's see how our backend gets its secret API key.\n\n#### 1. Loading the Secret Key (`.env` and `server.js`)\n\nOur `EXTERNAL_API_KEY` is a secret, so we don't write it directly in our code. Instead, we put it in a special file called `.env` (which is never shared publicly). Our `server.js` file then loads this secret.\n\n```javascript\n// backend/.env.example\nEXTERNAL_API_KEY=YOUR_API_KEY_HERE\nPORT=3001\n```\n\n```javascript\n// backend/server.js (top of the file)\nrequire('dotenv').config(); // Loads variables from .env into process.env\nconst express = require('express');\nconst axios = require('axios');\n// ... other imports ...\n\nconst EXTERNAL_API_KEY = process.env.EXTERNAL_API_KEY; // Get the key!\nconst EXTERNAL_BASE_URL = \"https://techhk.aoscdn.com/\";\n// ... rest of the server setup ...\n```\n*Explanation*: `require('dotenv').config()` is a crucial line. It reads the `EXTERNAL_API_KEY` from your `.env` file and makes it available through `process.env.EXTERNAL_API_KEY`. This keeps your secret API key out of your main code, making it much safer.\n\n#### 2. The Setup: Express, CORS, and File Uploads\n\nOur backend uses `Express` to handle web requests, `cors` to allow our frontend to talk to it (even though they are on different \"addresses\"), and `multer` to handle receiving image files.\n\n```javascript\n// backend/server.js (middleware section)\nconst app = express();\nconst port = process.env.PORT || 3001; \n\napp.use(cors()); // Allows our frontend (e.g., localhost:5173) to talk to this backend\napp.use(express.json()); // Allows parsing JSON data\n\n// Configure Multer to temporarily store uploaded files in memory\nconst storage = multer.memoryStorage();\nconst upload = multer({ storage: storage }); \n```\n*Explanation*:\n*   `app = express()`: Creates our web server application.\n*   `app.use(cors())`: This is super important for local development! Without it, your browser would block the frontend from sending requests to the backend because they are on different \"origins\" (different port numbers). CORS makes them friendly.\n*   `multer({ storage: storage })`: Sets up `multer` to handle file uploads. `memoryStorage()` means the file is stored in the server's temporary memory until processed, which is simple for small files.\n\n#### 3. Endpoint 1: Receiving the Image (`POST /api/enhance`)\n\nThis is the main \"receiving desk\" where our backend gets the image from the frontend and forwards it to the external AI.\n\n```javascript\n// backend/server.js (inside app.post('/api/enhance', ...))\napp.post('/api/enhance', upload.single('image_file'), async (req, res, next) => {\n    if (!req.file) { // Check if an image was actually sent\n        return res.status(400).json({ message: 'No image file uploaded.' });\n    }\n\n    const formData = new FormData();\n    // Convert the image data from memory into a format the external API likes (Blob)\n    const imageBlob = new Blob([req.file.buffer], { type: req.file.mimetype });\n    formData.append('image_file', imageBlob, req.file.originalname);\n\n    try {\n        const { data } = await axios.post(\n            `${EXTERNAL_BASE_URL}/api/tasks/visual/scale`, // External AI's image upload address\n            formData,\n            {\n                headers: {\n                    'X-API-KEY': EXTERNAL_API_KEY, // ADDING THE SECRET KEY HERE!\n                    ...formData.getHeaders?.() \n                }\n            }\n        );\n        // ... Error checking and return taskId ...\n        res.json({ taskId: data.data.task_id });\n    } catch (error) {\n        // ... Error handling ...\n    }\n});\n```\n*Explanation*:\n*   `upload.single('image_file')`: This tells `multer` to expect a single file named `image_file` (matching what our frontend sends). Once uploaded, the file data is available in `req.file`.\n*   `new Blob(...)`: The file data received by `multer` is a `Buffer`. The external API expects a `Blob`, so we convert it.\n*   `formData.append(...)`: We put the image `Blob` into a `FormData` object, ready to be sent.\n*   `'X-API-KEY': EXTERNAL_API_KEY`: **This is the critical security step!** We add the secret API key to the request headers *only here* in the backend. The frontend never sees this key.\n*   `axios.post(...)`: Sends the image and API key to the `EXTERNAL_BASE_URL` for enhancement.\n*   `res.json({ taskId: data.data.task_id })`: The external AI service returns a `task_id`, and our backend immediately sends this `task_id` back to the frontend.\n\n#### 4. Endpoint 2: Checking the Status (`GET /api/status/:taskId`)\n\nThis endpoint handles all the status check requests from our frontend. It simply asks the external AI service for the real status and returns it.\n\n```javascript\n// backend/server.js (inside app.get('/api/status/:taskId', ...))\napp.get('/api/status/:taskId', async (req, res) => {\n    const { taskId } = req.params; // Get the taskId from the URL\n    \n    if (!EXTERNAL_API_KEY) { // Check if key is available\n        return res.status(500).json({ message: 'Server configuration error.' });\n    }\n\n    try {\n        const { data } = await axios.get(\n            `${EXTERNAL_BASE_URL}/api/tasks/visual/scale/${taskId}`, // External AI's status check address\n            {\n                headers: {\n                    'X-API-KEY': EXTERNAL_API_KEY, // ADDING THE SECRET KEY AGAIN!\n                },\n            }\n        );\n        // ... Error checking ...\n        res.json(data.data); // Return the status data received from external AI\n    } catch (error) {\n        // ... Error handling ...\n    }\n});\n```\n*Explanation*:\n*   `req.params`: This is how `Express` extracts dynamic parts of the URL, in this case, the `taskId`.\n*   `'X-API-KEY': EXTERNAL_API_KEY`: Again, the secret API key is added here, protecting it from the frontend.\n*   `axios.get(...)`: Sends a request to the `External AI Service` to get the status of the `taskId`.\n*   `res.json(data.data)`: The backend receives the status (which includes `state` and possibly `image_url`) from the external AI and simply passes it back to the frontend. The frontend then decides what to do based on this status (continue polling or display the image).\n\n### Conclusion\n\nIn this chapter, we've uncovered the vital role of the **Backend Proxy & API Gateway** in our `Simple-AI-Image-Enhancer` application. We learned that it acts as a secure intermediary, shielding our sensitive API key from the frontend, translating requests, and managing all communication with the external AI image enhancement service. This architectural choice makes our application more secure, reliable, and easier to maintain.\n\nNow that we've covered the core flow of image enhancement from frontend upload to backend processing, we'll shift our focus to something entirely different but equally important for user experience: the look and feel of our application. In the next chapter, we'll dive into the **Theme Management System**.\n\n[Next Chapter: Theme Management System](06_theme_management_system_.md)", "\nWrite a very beginner-friendly tutorial chapter (in Markdown format) for the project `Simple-AI-Image-Enhancer` about the concept: \"Theme Management System\n\". This is Chapter 6.\n\nConcept Details:\n- Name: Theme Management System\n\n- Description:\nThis system is responsible for controlling the application's visual style, specifically switching between a \"light\" mode (bright colors) and a \"dark\" mode (darker colors). It acts like a \"light switch\" for the entire app. It remembers your preferred theme (even if you close and reopen the app) and applies the correct colors and backgrounds across the interface, enhancing readability and user comfort.\n\n\nComplete Tutorial Structure:\n1. [Frontend Application Core\n](01_frontend_application_core_.md)\n2. [Image Upload Interface\n](02_image_upload_interface_.md)\n3. [Image Preview & Comparison Interface\n](03_image_preview___comparison_interface_.md)\n4. [Image Enhancement Client Service\n](04_image_enhancement_client_service_.md)\n5. [Backend Proxy & API Gateway\n](05_backend_proxy___api_gateway_.md)\n6. [Theme Management System\n](06_theme_management_system_.md)\n\nContext from previous chapters:\n# Chapter 1: Frontend Application Core\n\nWelcome to the first chapter of our tutorial for the `Simple-AI-Image-Enhancer` project! In this chapter, we're going to uncover the \"brain\" of our application's user-facing side, which we call the **Frontend Application Core**.\n\n### What is the Frontend Application Core?\n\nImagine you're building a house. You don't just throw bricks and wood together; you need a blueprint, a general contractor, and a clear plan for how everything fits. The \"Frontend Application Core\" is like that general contractor for our AI Image Enhancer app.\n\nIts main job is to **orchestrate** how you, the user, interact with the application. From the moment you open the app, to uploading your photo, seeing it processed, and finally viewing the enhanced version – the Frontend Application Core is in charge of managing this entire journey.\n\n**Think of it as the control center of the whole user interface.** It makes sure that different parts of the app, like the \"upload photo\" area or the \"show enhanced image\" section, work together smoothly to give you a complete and easy-to-use experience.\n\n### Our Goal: Understanding the Enhancement Flow\n\nOur main goal in this chapter is to understand how the app guides you through the core process of enhancing an image. Specifically, we want to know how the app takes your raw input (an unenhanced image) and prepares it for enhancement, ultimately displaying the result.\n\nHere's the simple user journey we'll explore:\n1.  You open the app.\n2.  You see a place to upload an image.\n3.  You select an image file from your computer.\n4.  The app displays your original image.\n5.  After some processing, the app displays the *enhanced* version of your image.\n\nHow does our \"Frontend Application Core\" manage all these steps? Let's dive in!\n\n### The Building Blocks: `main.jsx`, `App.jsx`, and `Home.jsx`\n\nOur application's core functionality is primarily handled by three key files:\n\n1.  **`src/main.jsx`**: This is the very first file that runs when our application starts. It's like the \"power button\" for our React app.\n2.  **`src/App.jsx`**: This file represents the top-level part of our application. Think of it as the \"main house\" or the overall container that holds everything together. It sets up the basic layout and includes other major parts.\n3.  **`src/components/Home.jsx`**: This is a key \"room\" within our \"main house\" specifically dedicated to the image enhancement process. It manages the steps for uploading, displaying, and triggering the enhancement.\n\nLet's look at each of them.\n\n#### 1. `src/main.jsx`: The Starting Line\n\nThis file is responsible for \"bootstrapping\" our React application. It tells the web browser where to \"mount\" or display our app.\n\n```javascript\n// src/main.jsx\nimport { StrictMode } from 'react'\nimport { createRoot } from 'react-dom/client'\nimport './index.css' // Basic styles\nimport App from './App.jsx' // Our main application component\nimport { ThemeProvider } from './contexts/ThemeContext'; // Theme settings\n\ncreateRoot(document.getElementById('root')).render(\n  <StrictMode>\n    <ThemeProvider>\n      <App /> {/* This is where our entire app starts! */}\n    </ThemeProvider>\n  </StrictMode>,\n)\n```\n\nIn this code, `createRoot(document.getElementById('root'))` finds a special spot in our web page (an HTML element with the ID `root`) and prepares it to display our React app. Then, `.render(<App />)` tells React to put our entire `App` component inside that spot. Notice how `App` is wrapped in `ThemeProvider` – this helps us manage light and dark modes across the app, which we'll cover in [Theme Management System](06_theme_management_system_.md).\n\n#### 2. `src/App.jsx`: The Main Stage\n\n`App.jsx` is where the overall structure and feel of our application are set. It's like the main stage where all the action happens. It defines the title of our app and includes the `Home` component, which is where the main image enhancement features live.\n\n```javascript\n// src/App.jsx\nimport React, { useContext } from 'react';\nimport Home from './components/Home'; // Our core enhancement component\nimport { ThemeContext } from './contexts/ThemeContext'; // For theme switching\n\nconst App = () => {\n  const { theme, toggleTheme } = useContext(ThemeContext);\n\n  return (\n    <div className='flex flex-col items-center ...'> {/* Overall layout */}\n      {/* ... Theme button and header ... */}\n      <h1 className='text-5xl font-bold ...'>AI Image Enhancer</h1>\n      <p className='text-lg text-gray-500 ...'>Upload an image and let AI enhance it in seconds.</p>\n      <Home/> {/* This is where the core functionality resides! */}\n      {/* ... Footer ... */}\n    </div>\n  );\n};\n\nexport default App;\n```\n\nHere, `App.jsx` is mainly responsible for the very top-level appearance and for including the `Home` component. It also contains the theme switching button, which is related to our [Theme Management System](06_theme_management_system_.md). The key takeaway here is that `App` brings in `Home` to do the actual image enhancement work.\n\n#### 3. `src/components/Home.jsx`: The Orchestrator\n\nThis is where the magic of orchestrating the image enhancement process truly happens. `Home.jsx` acts as the central control for managing the original image, the enhanced image, and the loading state. It connects the \"upload\" part with the \"preview\" part.\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\nimport ImageUpload from './ImageUpload' // Component to handle uploads\nimport ImagePreview from './ImagePreview' // Component to display images\nimport { enhanceImage } from '../services/imageEnhancer' // Service for enhancement\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  const [enhancedImage, setEnhancedImage] = useState(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      // ... logic to prepare and send image for enhancement ...\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      <ImageUpload onImageUpload={handleImageUpload} /> {/* Upload area */}\n      <ImagePreview \n        originalImage={originalImage} \n        enhancedImage={enhancedImage}\n        isLoading={isLoading}\n      /> {/* Preview area */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\n`Home.jsx` is critical because it:\n*   Uses `useState` to keep track of the `originalImage` (what you uploaded), `enhancedImage` (the result from the AI), and `isLoading` (to show if the app is busy).\n*   Defines `handleImageUpload`, a function that will be called when you select an image. This function is responsible for sending the image to the enhancement service and updating the state with the results.\n*   Includes two other important components:\n    *   `ImageUpload`: This component provides the user interface for selecting an image. We'll explore it in detail in [Image Upload Interface](02_image_upload_interface_.md).\n    *   `ImagePreview`: This component displays both the original and enhanced images. We'll learn more about it in [Image Preview & Comparison Interface](03_image_preview___comparison_interface_.md).\n\n### The Frontend Core in Action: A Simple Flow\n\nLet's visualize how these pieces work together when you upload an image:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant Main as main.jsx\n    participant App as App.jsx\n    participant Home as Home.jsx\n    participant ImageUpload as ImageUpload.jsx\n    participant ImagePreview as ImagePreview.jsx\n\n    User->>Main: Launches App\n    Main->>App: Renders App component\n    App->>Home: Renders Home component (the core enhancement logic)\n    Home->>ImageUpload: Renders Image Upload Interface\n    Home->>ImagePreview: Renders Image Preview Interface\n\n    User->>ImageUpload: Selects an image file\n    ImageUpload->>Home: Calls onImageUpload(imageFile)\n    Home->>Home: Updates originalImage state\n    Home->>Home: Sets isLoading to true\n    Home->>ImagePreview: Passes updated states (originalImage, isLoading)\n    ImagePreview-->>User: Shows original image & loading indicator\n    Home->>Backend Proxy & API Gateway: Calls enhanceImage(imageFile) (via Image Enhancement Client Service)\n    Backend Proxy & API Gateway-->>Home: Returns enhanced image data\n    Home->>Home: Updates enhancedImage state\n    Home->>Home: Sets isLoading to false\n    Home->>ImagePreview: Passes updated states (originalImage, enhancedImage, isLoading)\n    ImagePreview-->>User: Shows original and enhanced images for comparison\n```\n\n1.  **Start-up:** When you launch the app, `main.jsx` starts `App.jsx`.\n2.  **Overall Layout:** `App.jsx` sets up the basic page layout and includes `Home.jsx`.\n3.  **Core Logic:** `Home.jsx` then sets up the `ImageUpload` area and the `ImagePreview` area.\n4.  **User Uploads:** When you choose an image, the `ImageUpload` component notifies `Home.jsx` about the new image.\n5.  **State Management:** `Home.jsx` takes this original image, saves it, and also sets a \"loading\" flag. It passes these pieces of information down to `ImagePreview` so you can see your original image and know the app is working.\n6.  **Enhancement Request:** `Home.jsx` then uses the `enhanceImage` function (which is part of our [Image Enhancement Client Service](04_image_enhancement_client_service_.md)) to send your image to the AI backend.\n7.  **Result Display:** Once the AI returns the enhanced image, `Home.jsx` updates its state again, including the `enhancedImage` and turning off the \"loading\" flag. This new information is then passed to `ImagePreview` so you can see the amazing transformation!\n\n### Conclusion\n\nIn this chapter, we've explored the \"Frontend Application Core\" of our AI Image Enhancer. We learned that it's the central control system that orchestrates the user's journey, from uploading an image to viewing the enhanced result. We saw how `main.jsx` kicks off the application, how `App.jsx` provides the main stage, and most importantly, how `Home.jsx` acts as the orchestrator, managing the flow and connecting the different parts of the image enhancement process.\n\nNext, we'll dive deeper into one of these connected parts: the **Image Upload Interface**. We'll learn how users can select and upload their photos to our application.\n\n[Next Chapter: Image Upload Interface](02_image_upload_interface_.md)\n---\n# Chapter 2: Image Upload Interface\n\nIn our previous chapter, [Frontend Application Core](01_frontend_application_core_.md), we learned that the `Home.jsx` component is the central \"orchestrator\" for managing the image enhancement process. It's like the conductor of an orchestra, making sure every part plays its role. One of the very first instruments in this orchestra is the **Image Upload Interface**.\n\n### What is the Image Upload Interface?\n\nImagine you want to send a letter. You don't just throw it into the air and hope it reaches its destination! You need a mailbox. In our `Simple-AI-Image-Enhancer` application, the **Image Upload Interface** is that special \"mailbox\" for your photos.\n\nThis is the specific part of the user interface where you interact to bring your image into the application. It's designed to be super easy to use:\n\n*   **Click to Browse:** You can simply click on the area, and a window will pop up, allowing you to select an image file from your computer, just like opening a document.\n*   **Drag and Drop:** Or, even simpler, you can just drag an image file directly from your computer's folders and \"drop\" it onto this area in the app.\n\nThe Image Upload Interface also has a smart guard dog: it makes sure that only valid image files (like JPGs or PNGs) are accepted. Once your image is safely inside, it then prepares it to be sent off for enhancement.\n\n### Our Goal: Bringing Your Image In\n\nOur main goal in this chapter is to understand how we can get your image file from your computer into our application, specifically into the `Home.jsx` component we talked about in Chapter 1.\n\nRecall from [Frontend Application Core](01_frontend_application_core_.md) that `Home.jsx` includes `ImageUpload` like this:\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\nimport ImageUpload from './ImageUpload' // Component to handle uploads\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  // ... other states ...\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      setOriginalImage(imageFile) // Save the image received from ImageUpload\n      // ... logic to send for enhancement ...\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      <ImageUpload onImageUpload={handleImageUpload} /> {/* This is our upload area! */}\n      {/* ... ImagePreview ... */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\nNotice the line `<ImageUpload onImageUpload={handleImageUpload} />`. This shows us that `Home.jsx` gives `ImageUpload` a special function called `handleImageUpload`. This is how `ImageUpload` will \"mail\" the selected image back to `Home.jsx`.\n\n### How It Works: The Journey of Your Image\n\nLet's trace what happens when you interact with the Image Upload Interface:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant ImageUpload as ImageUpload.jsx\n    participant Home as Home.jsx\n\n    User->>ImageUpload: Clicks or Drags & Drops image file\n    ImageUpload->>ImageUpload: Checks if it's a valid image (e.g., JPG, PNG)\n    alt If not a valid image\n        ImageUpload-->>User: Shows an \"Oops!\" message\n    else If valid image\n        ImageUpload->>Home: Calls onImageUpload(imageFile)\n        Home->>Home: Receives imageFile and prepares it for enhancement\n    end\n```\n\n1.  **You Interact:** You either click the upload area or drag an image file onto it.\n2.  **ImageUpload Catches It:** The `ImageUpload` component, which is the blueprint for our \"mailbox,\" \"catches\" the file.\n3.  **The Guard Dog Checks:** It immediately performs a quick check: \"Is this actually an image file?\" If it's not (e.g., you accidentally dropped a text file), it gently tells you.\n4.  **Delivering the Mail:** If it *is* a valid image, `ImageUpload` doesn't keep it. Instead, it takes that image file and \"mails\" it back to `Home.jsx` by calling the `onImageUpload` function that `Home.jsx` gave it.\n5.  **Home.jsx Takes Over:** Now `Home.jsx` has your image, ready to start the enhancement process!\n\n### Diving into the Code: `src/components/ImageUpload.jsx`\n\nLet's open up the `src/components/ImageUpload.jsx` file to see how this \"mailbox\" is built.\n\n#### 1. Remembering What's Happening with `useState`\n\nFirst, `ImageUpload.jsx` needs to remember a few things, like if you're currently dragging a file or what file you've selected. It uses `useState` for this.\n\n```javascript\n// src/components/ImageUpload.jsx\nimport React, { useState, useRef } from 'react'\n\nconst ImageUpload = ({ onImageUpload }) => {\n  const [isDragging, setIsDragging] = useState(false) // Is a file being dragged over the area?\n  const [fileName, setFileName] = useState('')       // What's the name of the file selected?\n  const fileInputRef = useRef(null)                   // A secret way to click a hidden file input\n\n  // ... rest of the component's code ...\n}\nexport default ImageUpload\n```\n\n*Explanation*: `useState` helps our component remember information that might change (like if `isDragging` is true or false). `useRef` is like having a direct \"handle\" to an actual part of the web page (in our case, a hidden file selector), so we can tell it to do things, like \"click yourself!\"\n\n#### 2. Handling Drag-and-Drop\n\nWhen you drag a file, your browser has its own default behavior (like trying to open the file). We need to stop that and tell our component what to do instead.\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component)\nconst handleDragOver = (e) => {\n  e.preventDefault() // Stop the browser from doing its own thing\n  setIsDragging(true) // Turn on the \"dragging\" style (e.g., blue border)\n}\n\nconst handleDragLeave = () => {\n  setIsDragging(false) // Turn off the \"dragging\" style\n}\n\nconst handleDrop = (e) => {\n  e.preventDefault() // Stop the browser\n  setIsDragging(false) // Turn off the \"dragging\" style\n  \n  if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n    const file = e.dataTransfer.files[0] // Get the dropped file\n    processFile(file) // Send it to our \"guard dog\" function\n  }\n}\n// ... rest of the component ...\n```\n\n*Explanation*: These three functions handle the drag-and-drop actions. `handleDragOver` and `handleDragLeave` simply change the look of our upload box to give you feedback (like making the border blue). When you finally `handleDrop` the file, we grab the file and send it to `processFile` to check if it's an image.\n\n#### 3. Handling Click-to-Upload\n\nNot everyone likes dragging and dropping! We also provide a way to click and browse your files.\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component)\nconst handleFileChange = (e) => {\n  if (e.target.files && e.target.files[0]) {\n    const file = e.target.files[0] // Get the selected file\n    processFile(file) // Send it to our \"guard dog\" function\n  }\n}\n\nconst handleClick = () => {\n  fileInputRef.current.click() // Secretly \"click\" the hidden file selection box\n}\n// ... rest of the component ...\n```\n\n*Explanation*: When you click anywhere on our upload box, `handleClick` makes the hidden file input (which normally opens the file selection window) pop up. Once you choose a file from that window, `handleFileChange` is activated, which then passes your chosen file to `processFile`.\n\n#### 4. The \"Guard Dog\" and Mailman: `processFile`\n\nThis is a crucial function that acts as both the validator and the delivery person.\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component)\nconst processFile = (file) => {\n  // Is it an image?\n  if (!file.type.match('image.*')) { // Checks if the file type starts with 'image/'\n    alert('Oops! Please select an image file (like JPG, PNG, WEBP).')\n    return // Stop right here if it's not an image\n  }\n  \n  setFileName(file.name) // Display the file's name in the UI\n  onImageUpload(file)    // This is where we \"mail\" the file back to Home.jsx!\n}\n// ... rest of the component ...\n```\n\n*Explanation*: `processFile` first checks if the file's `type` matches an image format. If not, it shows an alert. If it *is* an image, it updates the `fileName` to be displayed and then, most importantly, calls `onImageUpload(file)`. This is the exact moment the `ImageUpload` component hands off the selected image file to `Home.jsx` for further processing.\n\n#### 5. The Visual \"Mailbox\": JSX Structure\n\nFinally, all these pieces come together in the visual part of the component, which uses JSX (a mix of JavaScript and HTML-like tags).\n\n```javascript\n// src/components/ImageUpload.jsx (inside the component's return statement)\nreturn (\n  <div className='bg-white dark:bg-gray-800 shadow-lg rounded-2xl w-full max-w-2xl p-6'>\n    <div \n      className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-all cursor-pointer\n        ${isDragging ? 'border-blue-500 bg-blue-50 dark:bg-blue-900' : 'border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400'}`}\n      onDragOver={handleDragOver}   // What happens when dragging over\n      onDragLeave={handleDragLeave} // What happens when dragging leaves\n      onDrop={handleDrop}           // What happens when dropped\n      onClick={handleClick}         // What happens when clicked\n    >\n      <input \n        type=\"file\" \n        id=\"fileInput\" \n        ref={fileInputRef}     // Connects to our secret handle\n        className='hidden'     // Makes the default file input invisible\n        accept=\"image/*\"       // Only allows image files in the selection window\n        onChange={handleFileChange} // What happens when a file is selected\n      />\n      \n      <div className=\"flex flex-col items-center justify-center py-4\">\n        {/* An SVG icon to make it look nice */}\n        <p className='text-lg font-medium'>\n          {fileName ? `Selected: ${fileName}` : 'Click or drag to upload an image'}\n        </p>\n        <p className='text-sm text-gray-500'>\n          Supports JPG, PNG, WEBP (Max 10MB)\n        </p>\n      </div>\n    </div>\n  </div>\n)\n```\n\n*Explanation*: This is what you see! It's a nicely styled `div` (our \"mailbox\" container). It listens for your actions like `onDragOver`, `onDragLeave`, `onDrop`, and `onClick`, connecting them to the functions we just discussed. Inside it, there's a `<input type=\"file\" />` that's kept `hidden` and controlled by our JavaScript. The text dynamically changes to show the `fileName` once an image is selected, making the interface clear and user-friendly.\n\n### Conclusion\n\nIn this chapter, we've explored the **Image Upload Interface**, the crucial first step in our image enhancement journey. We learned how this component acts as a user-friendly \"mailbox,\" allowing you to easily bring your images into the application using either click-to-browse or drag-and-drop. We also saw how it validates files and, most importantly, \"mails\" the selected image back to `Home.jsx` via the `onImageUpload` function.\n\nNow that we know how to get an image *into* the app, the next logical step is to see it! In the next chapter, we'll dive into the **Image Preview & Comparison Interface**, where you'll see your original image and, eventually, its beautifully enhanced counterpart.\n\n[Next Chapter: Image Preview & Comparison Interface](03_image_preview___comparison_interface_.md)\n---\n# Chapter 3: Image Preview & Comparison Interface\n\nIn our previous chapter, [Image Upload Interface](02_image_upload_interface.md), we learned how our application provides a user-friendly \"mailbox\" for you to bring your images into the system. Now that your image has been successfully \"mailed\" into the app, where does it go next? It needs a place to be seen!\n\nThis is where the **Image Preview & Comparison Interface** comes in.\n\n### What is the Image Preview & Comparison Interface?\n\nImagine you're looking at a photo album. When you first upload a picture, you want to see that picture immediately. That's the first job of this interface: it acts like a digital \"photo album\" displaying your original image as soon as it's ready.\n\nBut it gets even better! Once our clever AI has worked its magic and enhanced your image, this interface transforms into a super-cool \"compare tool.\" It lets you see your original image right next to the new, improved version. You can even slide a bar across the image to smoothly switch between the \"before\" and \"after\" views, helping you truly appreciate the enhancement.\n\nAnd of course, once you're happy with the result, it gives you an easy way to \"take home\" your newly enhanced masterpiece by providing a convenient download button.\n\n### Our Goal: Showing Off Your Images\n\nOur main goal in this chapter is to understand how the `ImagePreview` component works to display your pictures at different stages of the enhancement process – from just being uploaded, to waiting for AI, and finally, showcasing the amazing before-and-after transformation.\n\nRecall from [Frontend Application Core](01_frontend_application_core.md) that `Home.jsx` is the orchestrator, and it uses `ImagePreview` by passing it important information:\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\n// ... other imports ...\nimport ImagePreview from './ImagePreview' // Our preview component\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  const [enhancedImage, setEnhancedImage] = useState(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  // ... handleImageUpload function (updates originalImage, sets isLoading) ...\n  // ... logic to call enhancement service (updates enhancedImage, sets isLoading false) ...\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      {/* ... ImageUpload component ... */}\n      <ImagePreview \n        originalImage={originalImage} \n        enhancedImage={enhancedImage}\n        isLoading={isLoading}\n      /> {/* This is our preview area! */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\n`Home.jsx` passes three key pieces of information (called \"props\") to `ImagePreview`:\n*   `originalImage`: The image you just uploaded.\n*   `enhancedImage`: The amazing picture after the AI has worked on it.\n*   `isLoading`: A true/false switch that tells `ImagePreview` if the AI is currently busy enhancing your image.\n\n### How It Works: The Image's Display Journey\n\nLet's trace how your image appears and changes in the `ImagePreview` interface:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant Home as Home.jsx\n    participant ImagePreview as ImagePreview.jsx\n\n    User->>Home: Uploads image\n    Home->>ImagePreview: Passes originalImage, isLoading=true\n    ImagePreview-->>User: Shows a \"Please Wait...\" screen (Loading State)\n\n    Home->>Home: Sends image for enhancement (via Image Enhancement Client Service)\n    Home->>Home: Receives enhanced image\n    Home->>ImagePreview: Passes originalImage, enhancedImage, isLoading=false\n\n    alt If original exists, but no enhanced yet (e.g., after initial upload, before enhancement result)\n        ImagePreview-->>User: Shows ONLY the original image (Initial Preview State)\n    else If both original and enhanced images are ready\n        ImagePreview-->>User: Shows the interactive Before/After comparison (Comparison State)\n    end\n```\n\n1.  **Original Arrives, AI is Busy**: As soon as you upload an image, `Home.jsx` sends your `originalImage` to `ImagePreview` and also tells it `isLoading` is `true`. `ImagePreview` immediately puts up a \"Please Wait...\" sign.\n2.  **AI Finishes**: Once the AI processing is done, `Home.jsx` updates `ImagePreview` with the `enhancedImage` and sets `isLoading` to `false`.\n3.  **The Big Reveal**: Now `ImagePreview` has both the original and enhanced images, and it switches from the \"Please Wait...\" screen to the cool comparison tool, letting you slide between them.\n\n### Diving into the Code: `src/components/ImagePreview.jsx`\n\nLet's look inside the `src/components/ImagePreview.jsx` file to see how it manages these different display \"scenarios.\"\n\n#### 1. The Component's \"Brain\": Receiving Information\n\nFirst, the `ImagePreview` component gets the `originalImage`, `enhancedImage`, and `isLoading` information from `Home.jsx`.\n\n```javascript\n// src/components/ImagePreview.jsx\nimport React from 'react';\nimport { ReactCompareSlider, ReactCompareSliderImage } from 'react-compare-slider';\nimport Loading from './Loading'; // A small component to show \"loading\"\n\nconst ImagePreview = ({ originalImage, enhancedImage, isLoading }) => {\n    // This component will change what it shows based on these three pieces of info!\n\n    // ... The rest of the logic (different display scenarios) ...\n};\n\nexport default ImagePreview;\n```\n*Explanation*: The `ImagePreview` component is a \"smart display.\" It listens to the `originalImage`, `enhancedImage`, and `isLoading` signals from `Home.jsx` to decide what to show on your screen.\n\n#### 2. Scenario 1: The \"Please Wait...\" Screen\n\nIf the `isLoading` signal is `true`, it means our AI is busy working its magic. We want to show a friendly loading animation.\n\n```javascript\n// src/components/ImagePreview.jsx (inside the component)\n    if (isLoading) {\n        return (\n            <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl flex justify-center items-center min-h-[300px]\">\n                <Loading /> {/* Our spinning wheel or message */}\n            </div>\n        );\n    }\n```\n*Explanation*: If `isLoading` is `true`, the component immediately stops here and shows a `Loading` animation (which is a separate, simple spinning wheel component). This tells you that the app is busy and you should wait.\n\n#### 3. Scenario 2: The \"First Look\" – Only Original Image\n\nWhat if you've uploaded an image, but the AI hasn't finished enhancing it yet (or perhaps it failed)? In this case, we still want to show your original picture, even if the enhanced one isn't ready.\n\n```javascript\n// src/components/ImagePreview.jsx (inside the component)\n    // If not loading, and original image exists, but no enhanced image yet:\n    if (originalImage && !enhancedImage && !isLoading) {\n         return (\n             <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl\">\n                <h2 className=\"text-xl font-semibold text-center ...\">Original Image</h2>\n                <img \n                    src={originalImage} \n                    alt=\"Original Uploaded\" \n                    className=\"max-w-full h-auto rounded mx-auto\" \n                    style={{ maxHeight: '60vh' }} \n                />\n            </div>\n         );\n    }\n```\n*Explanation*: This block checks if we have an `originalImage`, but `enhancedImage` is `null` (meaning it's not ready yet), and we're not currently `isLoading`. If all these are true, it simply displays your `originalImage` along with a \"Original Image\" title.\n\n#### 4. Scenario 3: The \"Big Reveal\" – Before & After Comparison\n\nThis is the main event! If we have both `originalImage` AND `enhancedImage`, it's time to show them side-by-side using a special comparison tool.\n\n```javascript\n// src/components/ImagePreview.jsx (inside the component)\n    // If both original and enhanced images are available:\n    if (originalImage && enhancedImage) {\n        return (\n             <div className=\"mt-8 w-full max-w-2xl p-4 bg-white dark:bg-gray-800 shadow-lg rounded-2xl\">\n                 <h2 className=\"text-xl font-semibold text-center ...\">Compare Images</h2>\n                 <ReactCompareSlider\n                     style={{ height: '70vh', width: '100%', margin: '0 auto' }}\n                    itemOne={\n                         <ReactCompareSliderImage src={originalImage} alt=\"Original Image\" />\n                     }\n                    itemTwo={\n                            <ReactCompareSliderImage src={enhancedImage} alt=\"Enhanced Image\" />\n                     }\n                />\n                 <div className=\"text-center mt-4\">\n                        <a\n                          href={enhancedImage} // Link to the enhanced image\n                          download=\"enhanced-image.png\" // Suggested filename\n                          className=\"inline-block bg-blue-500 hover:bg-blue-700 ... \"\n                        >\n                            Download Enhanced Image\n                        </a>\n                 </div>\n            </div>\n        );\n    }\n```\n*Explanation*:\n*   **`ReactCompareSlider`**: This is a powerful little tool (a \"third-party library\") that makes the cool sliding comparison effect. We give it `itemOne` (our `originalImage`) and `itemTwo` (our `enhancedImage`), and it magically creates the slider.\n*   **Download Button**: Below the slider, there's a simple `<a>` (link) tag. The `href` points directly to the `enhancedImage` data, and the `download` attribute tells your browser to save it as `enhanced-image.png` when you click it. It's like pressing \"Save\" on your masterpiece!\n\n#### 5. Fallback: Nothing to Show\n\nFinally, if none of the above conditions are met (e.g., no image has been uploaded yet, and it's not loading), the component simply returns nothing.\n\n```javascript\n// src/components/ImagePreview.jsx (inside the component)\n    // Fallback case (e.g., no images uploaded yet)\n    return null;\n}; // End of the ImagePreview component\n```\n*Explanation*: This ensures that if there's no relevant image data, our `ImagePreview` area remains empty and tidy, waiting for you to upload a picture.\n\n### Conclusion\n\nIn this chapter, we've explored the **Image Preview & Comparison Interface**, the \"photo album\" and \"compare tool\" of our application. We learned how the `ImagePreview` component intelligently displays your images at different stages: showing a loading screen when the AI is busy, displaying just the original image when first uploaded, and finally, transforming into an interactive before-and-after slider with a handy download button once the enhancement is complete.\n\nNow that we know how images are displayed, the next logical step is to understand how the application *requests* the enhancement from the AI. In the next chapter, we'll dive into the **Image Enhancement Client Service**, the part of our app that talks to the powerful AI backend.\n\n[Next Chapter: Image Enhancement Client Service](04_image_enhancement_client_service.md)\n---\n# Chapter 4: Image Enhancement Client Service\n\nIn our previous chapter, [Image Preview & Comparison Interface](03_image_preview___comparison_interface.md), we learned how our application displays images, from the initial upload to the final, amazing before-and-after comparison. But for that comparison to happen, we first need the *enhanced* image!\n\nWhere does that enhanced image come from? It comes from our powerful AI backend, which is like a super-smart artist living in a different city. Our frontend (the part of the app you see and interact with) needs a way to send your image to this artist and then patiently wait for the masterpiece to be sent back. This is where the **Image Enhancement Client Service** steps in.\n\n### What is the Image Enhancement Client Service?\n\nImagine you want to order a custom painting from an artist far away. You wouldn't just shout your request across the country! You'd hire a reliable **messenger service** or a **personal assistant** to handle all the tricky communication.\n\nIn our `Simple-AI-Image-Enhancer` application, the **Image Enhancement Client Service** is exactly that: it's the dedicated \"messenger\" on the frontend side. Its main job is to communicate with our backend server to get your images enhanced. It's the one who:\n\n1.  **Sends your original image** to the AI artist (the backend).\n2.  **Repeatedly checks in** with the artist to ask, \"Is it done yet?\" (We call this \"polling\").\n3.  **Receives the final enhanced image URL** once it's ready.\n\nBy handling all these complex steps, our \"messenger\" makes life easy for the main parts of our app (like `Home.jsx`), allowing them to focus on just *showing* you the images, not worrying about *how* they get enhanced.\n\n### Our Goal: Getting the Enhanced Image\n\nOur main goal in this chapter is to understand how our app uses this \"messenger\" service to send an image for enhancement and then patiently wait for the result. Specifically, we'll look at the `enhanceImage` function that `Home.jsx` calls when you upload a photo.\n\nRecall from [Frontend Application Core](01_frontend_application_core.md) that `Home.jsx` uses this service like so:\n\n```javascript\n// src/components/Home.jsx\nimport React, { useState } from 'react'\n// ... other imports ...\nimport { enhanceImage } from '../services/imageEnhancer' // Our \"messenger\" service\n\nconst Home = () => {\n  const [originalImage, setOriginalImage] = useState(null)\n  const [enhancedImage, setEnhancedImage] = useState(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleImageUpload = async(imageFile) => {\n    if (imageFile) {\n      setOriginalImage(URL.createObjectURL(imageFile)); // For immediate preview\n      setIsLoading(true); // Show loading spinner\n      try {\n        const enhancedResult = await enhanceImage(imageFile); // HERE'S THE CALL!\n        setEnhancedImage(enhancedResult.enhanced_url); // Get the enhanced image URL\n      } catch (error) {\n        console.error(\"Enhancement failed:\", error);\n        // Handle error, maybe show an error message to the user\n      } finally {\n        setIsLoading(false); // Hide loading spinner\n      }\n    }\n  }\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      {/* ... ImageUpload and ImagePreview components ... */}\n    </div>\n  )\n}\n\nexport default Home\n```\n\nThe line `const enhancedResult = await enhanceImage(imageFile);` is where `Home.jsx` hands off the image to our service and patiently `await`s the result.\n\n### How It Works: The Messenger's Journey\n\nLet's visualize how our \"messenger\" (the Image Enhancement Client Service) handles getting your image enhanced:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant Home as Home.jsx\n    participant ClientService as ImageEnhancerClientService\n    participant Backend as Backend Proxy & API Gateway\n\n    User->>Home: Uploads Image\n    Home->>ClientService: Calls enhanceImage(imageFile)\n    ClientService->>Backend: 1. Uploads original image\n    Backend-->>ClientService: Returns a Task ID (like a tracking number)\n    loop Check Status Repeatedly (Polling)\n        ClientService->>Backend: 2. Asks for status of Task ID\n        Backend-->>ClientService: Returns current status (e.g., \"processing\", \"done\")\n        alt If still processing\n            ClientService: Waits a few seconds\n        else If done\n            break\n        end\n    end\n    Backend-->>ClientService: Returns Enhanced Image URL\n    ClientService-->>Home: Returns Enhanced Image URL\n    Home-->>User: Displays enhanced image\n```\n\n1.  **User Uploads**: You select an image, and `Home.jsx` gets it.\n2.  **Hand-off to Messenger**: `Home.jsx` then calls `enhanceImage` in our `ClientService`, giving it your image.\n3.  **Messenger Uploads**: The `ClientService` first sends your image to the `Backend`. The `Backend` gives back a `Task ID` – a unique tracking number for your enhancement job.\n4.  **Messenger Polls (Checks Status)**: The `ClientService` then uses this `Task ID` to repeatedly ask the `Backend`: \"Is task `[Task ID]` done yet?\"\n5.  **Backend Responds**: The `Backend` responds, either saying \"Still working!\" or \"It's done! Here's the URL for your enhanced image!\"\n6.  **Messenger Delivers**: Once the `Backend` says it's done and gives the URL, the `ClientService` delivers that URL back to `Home.jsx`.\n7.  **Display**: `Home.jsx` then updates its state, and `ImagePreview` shows your beautifully enhanced image!\n\n### Diving into the Code: `src/services/imageEnhancer.js`\n\nAll the magic for our \"messenger\" service happens in the `src/services/imageEnhancer.js` file. Let's break it down.\n\n#### 1. The Main Messenger Function: `enhanceImage`\n\nThis is the central function that `Home.jsx` calls. It orchestrates the entire process: first uploading the image and then repeatedly checking for the result.\n\n```javascript\n// src/services/imageEnhancer.js\nimport axios from \"axios\"; // A library to easily send web requests\n\n// Our backend's address and how many times we'll check\nconst BASE_URL = \"http://localhost:3001\";\nconst MAXIMUM_RETRIES = 20;\n\nexport const enhanceImage = async (file) => {\n    try {\n        const taskId = await uploadImage(file); // Step 1: Send the image & get a task ID\n        console.log(\"Image Uploaded, Task ID:\", taskId);\n\n        // Step 2: Keep asking the backend until the enhancement is ready\n        const enhancedImageData = await PollForEnhancedImage(taskId);\n        console.log(\"Enhanced Image Data Received:\", enhancedImageData);\n\n        return enhancedImageData; // Give the enhanced image URL back to Home.jsx\n    } catch (error) {\n        // If anything goes wrong, log it and let Home.jsx know\n        console.error(\"Error enhancing image:\", error.message);\n        throw error;\n    }\n};\n```\n*Explanation*: The `enhanceImage` function is like the project manager. It calls `uploadImage` to start the process and get a `taskId`. Then, it hands that `taskId` over to `PollForEnhancedImage`, which is responsible for waiting until the job is done. Once `PollForEnhancedImage` finishes, it returns the final enhanced image data.\n\n#### 2. Step 1: Sending the Image (`uploadImage`)\n\nThis function is responsible for taking your image file and sending it to our backend for processing.\n\n```javascript\n// src/services/imageEnhancer.js (inside imageEnhancer.js)\nconst uploadImage = async (file) => {\n    const formData = new FormData();\n    formData.append(\"image_file\", file); // Prepare the image to be sent\n\n    // Send the image to our backend's enhancement endpoint\n    const { data } = await axios.post(\n        `${BASE_URL}/api/enhance`, // The specific web address for enhancement\n        formData, // The image data\n        // Headers are automatically set for FormData, no API key needed here\n    );\n\n    // The backend should give us a task ID right away\n    if (!data?.taskId) {\n        throw new Error(\"Failed to get Task ID from backend!\");\n    }\n    return data.taskId; // Return the unique ID for this enhancement job\n};\n```\n*Explanation*: `uploadImage` uses `FormData` to bundle your image file in a way that can be sent over the internet. It then uses `axios.post` to send this bundle to the backend at the `/api/enhance` address. The backend immediately responds with a `taskId`, which is like a unique tracking number for your specific enhancement request.\n\n#### 3. Step 2: Repeatedly Asking for Updates (`PollForEnhancedImage`)\n\nThis is where the \"polling\" happens. This function repeatedly asks the backend for the status of your enhancement job using the `taskId`.\n\n```javascript\n// src/services/imageEnhancer.js (inside imageEnhancer.js)\nconst PollForEnhancedImage = async (taskId, retries = 0) => {\n    const result = await fetchEnhancedImage(taskId); // Ask for the latest status\n\n    // Our backend might use 'state: 4' to mean \"still processing\"\n    if (result.state === 4) {\n        console.log(`Image processing... (Attempt ${retries + 1} of ${MAXIMUM_RETRIES})`);\n\n        if (retries >= MAXIMUM_RETRIES) {\n            throw new Error(\"Enhancement took too long. Please try again.\");\n        }\n\n        // Wait for 2 seconds before asking again\n        await new Promise((resolve) => setTimeout(resolve, 2000));\n\n        // Call ourselves again to check the status after waiting\n        return PollForEnhancedImage(taskId, retries + 1);\n    }\n\n    // If 'state' is not 4, it means it's done (or failed, which backend handles)\n    return result; // This contains the enhanced image URL if successful\n};\n```\n*Explanation*: `PollForEnhancedImage` calls `fetchEnhancedImage` to get the status. If the status says `state: 4` (meaning \"processing\"), it prints a message, waits for 2 seconds using `setTimeout`, and then calls *itself* (`PollForEnhancedImage`) again to check the status. It does this over and over until the `state` is no longer `4` (meaning it's done!) or it hits the `MAXIMUM_RETRIES` limit.\n\n#### 4. Step 3: Getting the Latest Status (`fetchEnhancedImage`)\n\nThis is the simplest part of the polling process: it just asks the backend for the current status of a given `taskId`.\n\n```javascript\n// src/services/imageEnhancer.js (inside imageEnhancer.js)\nconst fetchEnhancedImage = async (taskId) => {\n    // Send a request to our backend's status endpoint with the task ID\n    const { data } = await axios.get(\n        `${BASE_URL}/api/status/${taskId}`, // The specific web address for status checks\n    );\n\n    if (!data) {\n        throw new Error(\"Did not receive status data from backend!\");\n    }\n    // The backend provides an object with status information (e.g., state, or enhanced_url)\n    return data;\n};\n```\n*Explanation*: `fetchEnhancedImage` uses `axios.get` to send a request to the backend's `/api/status/[taskId]` address. The backend responds with an object that tells us the current `state` of the enhancement job (e.g., if it's still processing, or if it's done and provides the enhanced image URL).\n\n### Conclusion\n\nIn this chapter, we've explored the **Image Enhancement Client Service**, our application's dedicated \"messenger\" for talking to the AI backend. We learned how this service takes your original image, sends it for enhancement, and then intelligently \"polls\" (repeatedly checks) the backend for updates until the enhanced image is ready. This powerful abstraction makes the complex communication process simple and reliable for the rest of our frontend application.\n\nNow that we understand how the frontend talks to the backend, it's time to see what happens on the *other side* of the conversation! In the next chapter, we'll dive into the **Backend Proxy & API Gateway**, which is the server that receives our messenger's requests and forwards them to the actual AI.\n\n[Next Chapter: Backend Proxy & API Gateway](05_backend_proxy___api_gateway_.md)\n---\n# Chapter 5: Backend Proxy & API Gateway\n\nIn our previous chapter, [Image Enhancement Client Service](04_image_enhancement_client_service.md), we explored how our frontend application acts as a \"messenger\" to send your image to an AI service and then patiently waits for the enhanced result. But where does this messenger *really* send the image? It doesn't send it directly to the super-secret AI service!\n\nInstead, it sends it to our own custom \"middleman\" server, which we call the **Backend Proxy & API Gateway**. This is the behind-the-scenes hero that handles all the sensitive and complex communication with the powerful (and external) AI image enhancement service.\n\n### What is the Backend Proxy & API Gateway?\n\nImagine you have a very important, secret message to send to a high-security vault. You wouldn't just write your message on a postcard and mail it directly! You'd give it to a trusted, highly secure **courier service** or a **special agent**. This agent knows how to navigate the security, add necessary secret codes, and make sure the message gets delivered properly.\n\nIn our `Simple-AI-Image-Enhancer` application, the **Backend Proxy & API Gateway** plays this special agent role:\n\n*   **Security Guard / \"Bouncer\":** It stands between our user-facing frontend (what you see) and the external AI image enhancement service. When you upload an image, the frontend sends it here first. This ensures that the frontend never has to know or store your secret API key for the external service. Only our backend knows it, keeping it safe!\n*   **Translator & Forwarder:** It takes the image and requests from our frontend and translates them into the specific language and format that the external AI service understands. Then, it securely adds your secret API key (which the frontend doesn't see) and forwards the image to the external AI service.\n*   **Result Retriever & Deliverer:** It doesn't just send the image and forget it. It also handles polling the external service for the enhancement status (just like our frontend polls *it*). Once the enhanced image is ready, it fetches it and sends the results back to our frontend.\n\nSo, it's a central hub that simplifies communication, adds a layer of security, and manages the entire back-and-forth with the external AI provider.\n\n### Our Goal: Securely Enhancing an Image\n\nOur main goal in this chapter is to understand how the Backend Proxy & API Gateway receives an image from our frontend, securely communicates with an external AI service to get it enhanced, and then delivers the result back.\n\nRecall from [Image Enhancement Client Service](04_image_enhancement_client_service.md) that our frontend's `enhanceImage` function makes two main calls to our backend:\n\n1.  A `POST` request to `/api/enhance` to send the image.\n2.  Repeated `GET` requests to `/api/status/:taskId` to check the enhancement progress.\n\nThe Backend Proxy & API Gateway is designed to handle both of these requests.\n\n### How It Works: The Journey Through the Gateway\n\nLet's visualize the entire process, including our new \"special agent\" in the middle:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant Frontend as Frontend App\n    participant BackendProxy as Backend Proxy & API Gateway\n    participant ExternalAI as External AI Service\n\n    User->>Frontend: 1. Uploads Image\n    Frontend->>BackendProxy: 2. Sends original image (POST /api/enhance)\n    BackendProxy->>BackendProxy: 3. Adds secret API key\n    BackendProxy->>ExternalAI: 4. Forwards image with API key\n    ExternalAI-->>BackendProxy: 5. Returns Task ID\n    BackendProxy-->>Frontend: 6. Returns Task ID\n\n    loop Polling for Status\n        Frontend->>BackendProxy: 7. Requests status for Task ID (GET /api/status/:taskId)\n        BackendProxy->>ExternalAI: 8. Requests status for Task ID\n        ExternalAI-->>BackendProxy: 9. Returns current status (e.g., \"processing\", \"done\")\n        alt If still processing\n            BackendProxy-->>Frontend: 10. Returns \"processing\" status\n            Frontend: Waits & requests again\n        else If done\n            BackendProxy-->>Frontend: 10. Returns \"done\" status & Enhanced Image URL\n            break\n        end\n    end\n    Frontend-->>User: 11. Displays enhanced image\n```\n\n1.  **User Uploads:** You select an image on the frontend.\n2.  **Frontend Sends to Proxy:** The [Image Enhancement Client Service](04_image_enhancement_client_service.md) sends your image to our backend at `/api/enhance`.\n3.  **Proxy Adds Key:** Our Backend Proxy receives the image. *Crucially*, it then adds the `EXTERNAL_API_KEY` (which only it knows).\n4.  **Proxy Forwards to External AI:** With the API key, the proxy forwards the image to the actual `External AI Service`.\n5.  **External AI Responds with Task ID:** The `External AI Service` starts processing and immediately gives our proxy a `task_id` (a tracking number).\n6.  **Proxy Returns Task ID to Frontend:** Our proxy sends this `task_id` back to the frontend.\n7.  **Frontend Polls Proxy:** The frontend repeatedly asks our proxy: \"What's the status of `[Task ID]`?\" (via `/api/status/:taskId`).\n8.  **Proxy Polls External AI:** For each request from the frontend, our proxy then asks the `External AI Service` for the *real* status.\n9.  **External AI Responds with Status:** The `External AI Service` tells our proxy if it's still processing or if it's done.\n10. **Proxy Returns Status to Frontend:** Our proxy passes this status (and the enhanced image URL when ready) back to the frontend.\n11. **Frontend Displays:** Once the frontend gets the enhanced image URL, it displays the result to you!\n\n### Diving into the Code: `backend/server.js`\n\nAll of this \"special agent\" work happens in our `backend/server.js` file. This file uses a technology called `Express.js` to create our web server and define these \"routes\" (the specific addresses like `/api/enhance`).\n\nBefore we look at the routes, let's see how our backend gets its secret API key.\n\n#### 1. Loading the Secret Key (`.env` and `server.js`)\n\nOur `EXTERNAL_API_KEY` is a secret, so we don't write it directly in our code. Instead, we put it in a special file called `.env` (which is never shared publicly). Our `server.js` file then loads this secret.\n\n```javascript\n// backend/.env.example\nEXTERNAL_API_KEY=YOUR_API_KEY_HERE\nPORT=3001\n```\n\n```javascript\n// backend/server.js (top of the file)\nrequire('dotenv').config(); // Loads variables from .env into process.env\nconst express = require('express');\nconst axios = require('axios');\n// ... other imports ...\n\nconst EXTERNAL_API_KEY = process.env.EXTERNAL_API_KEY; // Get the key!\nconst EXTERNAL_BASE_URL = \"https://techhk.aoscdn.com/\";\n// ... rest of the server setup ...\n```\n*Explanation*: `require('dotenv').config()` is a crucial line. It reads the `EXTERNAL_API_KEY` from your `.env` file and makes it available through `process.env.EXTERNAL_API_KEY`. This keeps your secret API key out of your main code, making it much safer.\n\n#### 2. The Setup: Express, CORS, and File Uploads\n\nOur backend uses `Express` to handle web requests, `cors` to allow our frontend to talk to it (even though they are on different \"addresses\"), and `multer` to handle receiving image files.\n\n```javascript\n// backend/server.js (middleware section)\nconst app = express();\nconst port = process.env.PORT || 3001; \n\napp.use(cors()); // Allows our frontend (e.g., localhost:5173) to talk to this backend\napp.use(express.json()); // Allows parsing JSON data\n\n// Configure Multer to temporarily store uploaded files in memory\nconst storage = multer.memoryStorage();\nconst upload = multer({ storage: storage }); \n```\n*Explanation*:\n*   `app = express()`: Creates our web server application.\n*   `app.use(cors())`: This is super important for local development! Without it, your browser would block the frontend from sending requests to the backend because they are on different \"origins\" (different port numbers). CORS makes them friendly.\n*   `multer({ storage: storage })`: Sets up `multer` to handle file uploads. `memoryStorage()` means the file is stored in the server's temporary memory until processed, which is simple for small files.\n\n#### 3. Endpoint 1: Receiving the Image (`POST /api/enhance`)\n\nThis is the main \"receiving desk\" where our backend gets the image from the frontend and forwards it to the external AI.\n\n```javascript\n// backend/server.js (inside app.post('/api/enhance', ...))\napp.post('/api/enhance', upload.single('image_file'), async (req, res, next) => {\n    if (!req.file) { // Check if an image was actually sent\n        return res.status(400).json({ message: 'No image file uploaded.' });\n    }\n\n    const formData = new FormData();\n    // Convert the image data from memory into a format the external API likes (Blob)\n    const imageBlob = new Blob([req.file.buffer], { type: req.file.mimetype });\n    formData.append('image_file', imageBlob, req.file.originalname);\n\n    try {\n        const { data } = await axios.post(\n            `${EXTERNAL_BASE_URL}/api/tasks/visual/scale`, // External AI's image upload address\n            formData,\n            {\n                headers: {\n                    'X-API-KEY': EXTERNAL_API_KEY, // ADDING THE SECRET KEY HERE!\n                    ...formData.getHeaders?.() \n                }\n            }\n        );\n        // ... Error checking and return taskId ...\n        res.json({ taskId: data.data.task_id });\n    } catch (error) {\n        // ... Error handling ...\n    }\n});\n```\n*Explanation*:\n*   `upload.single('image_file')`: This tells `multer` to expect a single file named `image_file` (matching what our frontend sends). Once uploaded, the file data is available in `req.file`.\n*   `new Blob(...)`: The file data received by `multer` is a `Buffer`. The external API expects a `Blob`, so we convert it.\n*   `formData.append(...)`: We put the image `Blob` into a `FormData` object, ready to be sent.\n*   `'X-API-KEY': EXTERNAL_API_KEY`: **This is the critical security step!** We add the secret API key to the request headers *only here* in the backend. The frontend never sees this key.\n*   `axios.post(...)`: Sends the image and API key to the `EXTERNAL_BASE_URL` for enhancement.\n*   `res.json({ taskId: data.data.task_id })`: The external AI service returns a `task_id`, and our backend immediately sends this `task_id` back to the frontend.\n\n#### 4. Endpoint 2: Checking the Status (`GET /api/status/:taskId`)\n\nThis endpoint handles all the status check requests from our frontend. It simply asks the external AI service for the real status and returns it.\n\n```javascript\n// backend/server.js (inside app.get('/api/status/:taskId', ...))\napp.get('/api/status/:taskId', async (req, res) => {\n    const { taskId } = req.params; // Get the taskId from the URL\n    \n    if (!EXTERNAL_API_KEY) { // Check if key is available\n        return res.status(500).json({ message: 'Server configuration error.' });\n    }\n\n    try {\n        const { data } = await axios.get(\n            `${EXTERNAL_BASE_URL}/api/tasks/visual/scale/${taskId}`, // External AI's status check address\n            {\n                headers: {\n                    'X-API-KEY': EXTERNAL_API_KEY, // ADDING THE SECRET KEY AGAIN!\n                },\n            }\n        );\n        // ... Error checking ...\n        res.json(data.data); // Return the status data received from external AI\n    } catch (error) {\n        // ... Error handling ...\n    }\n});\n```\n*Explanation*:\n*   `req.params`: This is how `Express` extracts dynamic parts of the URL, in this case, the `taskId`.\n*   `'X-API-KEY': EXTERNAL_API_KEY`: Again, the secret API key is added here, protecting it from the frontend.\n*   `axios.get(...)`: Sends a request to the `External AI Service` to get the status of the `taskId`.\n*   `res.json(data.data)`: The backend receives the status (which includes `state` and possibly `image_url`) from the external AI and simply passes it back to the frontend. The frontend then decides what to do based on this status (continue polling or display the image).\n\n### Conclusion\n\nIn this chapter, we've uncovered the vital role of the **Backend Proxy & API Gateway** in our `Simple-AI-Image-Enhancer` application. We learned that it acts as a secure intermediary, shielding our sensitive API key from the frontend, translating requests, and managing all communication with the external AI image enhancement service. This architectural choice makes our application more secure, reliable, and easier to maintain.\n\nNow that we've covered the core flow of image enhancement from frontend upload to backend processing, we'll shift our focus to something entirely different but equally important for user experience: the look and feel of our application. In the next chapter, we'll dive into the **Theme Management System**.\n\n[Next Chapter: Theme Management System](06_theme_management_system_.md)\n\nRelevant Code Snippets (Code itself remains unchanged):\n--- File: src/App.jsx ---\nimport React, { useContext } from 'react';\nimport Home from './components/Home';\nimport { ThemeContext } from './contexts/ThemeContext';\n\nconst App = () => {\n  const { theme, toggleTheme } = useContext(ThemeContext);\n\n  return (\n    <div className='flex flex-col items-center justify-center min-h-screen bg-gray-200 dark:bg-gray-900 py-8 px-4 relative'>\n      <button\n        onClick={toggleTheme}\n        className=\"absolute top-4 right-4 p-2 rounded-md bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-gray-200\"\n      >\n        {theme === 'light' ? 'Switch to Dark Mode' : 'Switch to Light Mode'}\n      </button>\n      <div className='text-center mb-8'>\n        <h1 className='text-5xl font-bold text-gray-800 dark:text-gray-100 mb-2'>AI Image Enhancer</h1>\n        <p className='text-lg text-gray-500 dark:text-gray-400'>Upload an image and let AI enhance it in seconds.</p>\n      </div>\n      <Home/>\n      <div className='text-lg text-gray-500 dark:text-gray-400 mt-6'>\n        Powered by @Learning-On-Peak\n      </div>\n    </div>\n  );\n};\n\nexport default App;\n\n--- File: src/contexts/ThemeContext.jsx ---\nimport React, { createContext, useState, useEffect } from 'react';\n\n// Create the context\nexport const ThemeContext = createContext();\n\n// Create the provider component\nexport const ThemeProvider = ({ children }) => {\n  const [theme, setTheme] = useState('light');\n\n  // Function to toggle theme\n  const toggleTheme = () => {\n    setTheme((prevTheme) => (prevTheme === 'light' ? 'dark' : 'light'));\n  };\n\n  // Effect to load theme from localStorage and apply it\n  useEffect(() => {\n    const storedTheme = localStorage.getItem('theme');\n    if (storedTheme) {\n      setTheme(storedTheme);\n    }\n  }, []);\n\n  // Effect to update localStorage and HTML element class when theme changes\n  useEffect(() => {\n    localStorage.setItem('theme', theme);\n    if (theme === 'dark') {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n  }, [theme]);\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\n\n--- File: tailwind.config.js ---\n/** @type {import('tailwindcss').Config} */\nexport default {\n  content: [\n    \"./index.html\",\n    \"./src/**/*.{js,ts,jsx,tsx}\",\n  ],\n  theme: {\n    extend: {},\n  },\n  plugins: [],\n  darkMode: 'class',\n}\n\n\nInstructions for the chapter (Generate content in English unless specified otherwise):\n- Start with a clear heading (e.g., `# Chapter 6: Theme Management System\n`). Use the provided concept name.\n\n- If this is not the first chapter, begin with a brief transition from the previous chapter, referencing it with a proper Markdown link using its name.\n\n- Begin with a high-level motivation explaining what problem this abstraction solves. Start with a central use case as a concrete example. The whole chapter should guide the reader to understand how to solve this use case. Make it very minimal and friendly to beginners.\n\n- If the abstraction is complex, break it down into key concepts. Explain each concept one-by-one in a very beginner-friendly way.\n\n- Explain how to use this abstraction to solve the use case. Give example inputs and outputs for code snippets (if the output isn't values, describe at a high level what will happen).\n\n- Each code block should be BELOW 10 lines! If longer code blocks are needed, break them down into smaller pieces and walk through them one-by-one. Aggresively simplify the code to make it minimal. Use comments to skip non-important implementation details. Each code block should have a beginner friendly explanation right after it.\n\n- Describe the internal implementation to help understand what's under the hood. First provide a non-code or code-light walkthrough on what happens step-by-step when the abstraction is called. It's recommended to use a simple sequenceDiagram with a dummy example - keep it minimal with at most 5 participants to ensure clarity. If participant name has space, use: `participant QP as Query Processing`. .\n\n- Then dive deeper into code for the internal implementation with references to files. Provide example code blocks, but make them similarly simple and beginner-friendly. Explain.\n\n- IMPORTANT: When you need to refer to other core abstractions covered in other chapters, ALWAYS use proper Markdown links like this: [Chapter Title](filename.md). Use the Complete Tutorial Structure above to find the correct filename and the chapter title. Translate the surrounding text.\n\n- Use mermaid diagrams to illustrate complex concepts (```mermaid``` format). .\n\n- Heavily use analogies and examples throughout to help beginners understand.\n\n- End the chapter with a brief conclusion that summarizes what was learned and provides a transition to the next chapter. If there is a next chapter, use a proper Markdown link: [Next Chapter Title](next_chapter_filename).\n\n- Ensure the tone is welcoming and easy for a newcomer to understand.\n\n- Output *only* the Markdown content for this chapter.\n\nNow, directly provide a super beginner-friendly Markdown output (DON'T need ```markdown``` tags):\n": "# Chapter 6: Theme Management System\n\nIn our journey so far, we've explored the core machinery of the `Simple-AI-Image-Enhancer`: from how you upload images ([Image Upload Interface](02_image_upload_interface.md)), to how they're previewed ([Image Preview & Comparison Interface](03_image_preview___comparison_interface.md)), and how our frontend talks to the AI backend ([Image Enhancement Client Service](04_image_enhancement_client_service.md)) through a secure gateway ([Backend Proxy & API Gateway](05_backend_proxy___api_gateway.md)). All these pieces are about *what* the app does.\n\nNow, let's talk about *how* the app looks and feels. Imagine you're reading a book. Sometimes, in a bright room, you prefer white pages with dark text. But in a dim room or late at night, your eyes might prefer dark pages with light text. Our app is designed to be just as flexible, and that's thanks to the **Theme Management System**.\n\n### What is the Theme Management System?\n\nThink of the **Theme Management System** as the app's own \"light switch\" or \"mood setter.\" Its main job is to control the entire application's visual style, allowing you to easily switch between a \"light mode\" (bright colors, white backgrounds) and a \"dark mode\" (darker colors, black or dark gray backgrounds).\n\nThis system does much more than just flip colors:\n*   **Aesthetic Choice:** It gives you the power to choose a visual style that you find most comfortable or appealing.\n*   **Readability & Comfort:** Dark mode, for example, can reduce eye strain in low-light conditions.\n*   **Memory Like an Elephant:** It remembers your preferred theme! Even if you close the app and open it again later, it will automatically apply your last chosen theme, so you don't have to switch it every time.\n*   **Consistent Look:** It makes sure that all parts of the app (text, backgrounds, buttons) adopt the correct colors for the chosen theme, keeping everything looking tidy and consistent.\n\nOur goal in this chapter is to understand how this \"light switch\" system works, allowing our app to dynamically change its appearance.\n\n### Our Goal: Flipping the App's Light Switch\n\nOur main goal is to understand how we can switch the entire application between light and dark modes, and how the app remembers our choice.\n\nRecall from [Frontend Application Core](01_frontend_application_core.md) that `main.jsx` wraps our entire `App` component with something called `ThemeProvider`:\n\n```javascript\n// src/main.jsx\n// ... other imports ...\nimport App from './App.jsx'\nimport { ThemeProvider } from './contexts/ThemeContext'; // Theme settings\n\ncreateRoot(document.getElementById('root')).render(\n  <StrictMode>\n    <ThemeProvider> {/* Our app starts here, wrapped in ThemeProvider */}\n      <App />\n    </ThemeProvider>\n  </StrictMode>,\n)\n```\nThis `ThemeProvider` is the heart of our Theme Management System. It's like the main power source for our light switch.\n\nAlso, `App.jsx` itself has a button to toggle the theme:\n\n```javascript\n// src/App.jsx\nimport React, { useContext } from 'react';\nimport Home from './components/Home';\nimport { ThemeContext } from './contexts/ThemeContext'; // For theme switching\n\nconst App = () => {\n  const { theme, toggleTheme } = useContext(ThemeContext); // Get theme info\n\n  return (\n    <div className='flex flex-col items-center ...'>\n      <button // The light switch button!\n        onClick={toggleTheme}\n        className=\"absolute top-4 right-4 p-2 rounded-md bg-gray-300 dark:bg-gray-700 ...\"\n      >\n        {theme === 'light' ? 'Switch to Dark Mode' : 'Switch to Light Mode'}\n      </button>\n      {/* ... rest of App.jsx ... */}\n    </div>\n  );\n};\n```\nNotice `const { theme, toggleTheme } = useContext(ThemeContext);` and the `onClick={toggleTheme}` on the button. This is how any part of our app can \"ask\" for the current theme and \"tell\" the system to switch it.\n\n### How It Works: The Light Switch in Action\n\nLet's trace what happens when you click the \"light switch\" button:\n\n```mermaid\nsequenceDiagram\n    participant User\n    participant App as App.jsx\n    participant ThemeContext as ThemeContext.jsx\n    participant Browser as Browser/localStorage\n\n    User->>App: Clicks \"Switch Theme\" button\n    App->>ThemeContext: Calls toggleTheme() function\n    ThemeContext->>ThemeContext: Updates current theme state (e.g., from 'light' to 'dark')\n    ThemeContext->>Browser: Saves new theme to localStorage (remembers choice)\n    ThemeContext->>Browser: Adds/removes 'dark' class to HTML document (changes appearance)\n    Browser->>App: App re-renders with new theme classes\n    App-->>User: App displays with new theme (e.g., dark mode)\n```\n\n1.  **You Click:** You click the \"Switch to Dark Mode\" button in `App.jsx`.\n2.  **`toggleTheme` is Called:** `App.jsx` tells the `ThemeContext` (our central theme manager) to `toggleTheme`.\n3.  **Theme State Updates:** `ThemeContext` internally updates its stored theme (e.g., from 'light' to 'dark').\n4.  **Preference Saved:** `ThemeContext` immediately saves this new theme ('dark') into your browser's `localStorage` so it remembers for next time.\n5.  **Appearance Changes:** `ThemeContext` then tells the main HTML element of your web page (`document.documentElement`) to either add or remove a special `dark` class.\n6.  **App Re-renders:** Because this `dark` class is now present (or absent), our styling system (Tailwind CSS) automatically applies the correct light or dark colors to all elements.\n7.  **New Look!** The app instantly changes its visual style to the new theme!\n\n### Diving into the Code: The Theme Management System's Heartbeat\n\nThe core of our Theme Management System lives in `src/contexts/ThemeContext.jsx` and relies on a special configuration in `tailwind.config.js`.\n\n#### 1. `src/contexts/ThemeContext.jsx`: The Theme Manager\n\nThis file is where the theme's state is stored, and the logic for changing and remembering it lives.\n\n```javascript\n// src/contexts/ThemeContext.jsx\nimport React, { createContext, useState, useEffect } from 'react';\n\n// 1. Create the Theme \"Channel\"\nexport const ThemeContext = createContext();\n\n// 2. The Theme \"Provider\" (The Manager itself)\nexport const ThemeProvider = ({ children }) => {\n  const [theme, setTheme] = useState('light'); // Keeps track of current theme\n\n  // Function to flip the switch\n  const toggleTheme = () => {\n    setTheme((prevTheme) => (prevTheme === 'light' ? 'dark' : 'light'));\n  };\n\n  // 3. Effect: Load theme from previous visit\n  useEffect(() => {\n    const storedTheme = localStorage.getItem('theme');\n    if (storedTheme) {\n      setTheme(storedTheme); // Set theme from what was remembered\n    }\n  }, []); // [] means this runs only once when the app starts\n\n  // 4. Effect: Apply theme and remember it when it changes\n  useEffect(() => {\n    localStorage.setItem('theme', theme); // Save current theme\n    if (theme === 'dark') {\n      document.documentElement.classList.add('dark'); // Add 'dark' class to main HTML\n    } else {\n      document.documentElement.classList.remove('dark'); // Remove 'dark' class\n    }\n  }, [theme]); // [theme] means this runs whenever 'theme' changes\n\n  return (\n    // 5. Share the theme and toggle function with all wrapped components\n    <ThemeContext.Provider value={{ theme, toggleTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n```\n*Explanation*:\n1.  **`createContext()`**: This creates a special \"channel\" (`ThemeContext`) through which components can communicate about the theme without directly passing information down many levels.\n2.  **`ThemeProvider`**: This component wraps our entire application (as seen in `main.jsx`). It manages the actual `theme` state (`useState`).\n3.  **`toggleTheme`**: This simple function just flips the `theme` state between 'light' and 'dark'.\n4.  **`useEffect` (Load)**: When the `ThemeProvider` first appears, this `useEffect` checks if you previously saved a theme in `localStorage` (your browser's small, local storage). If found, it uses that theme. This is how your preference is remembered.\n5.  **`useEffect` (Save & Apply)**: Whenever the `theme` state *changes* (because you clicked the button or a stored theme was loaded), this `useEffect` runs:\n    *   It saves the new `theme` to `localStorage` for future visits.\n    *   It adds or removes the `dark` class from the very top `<html>` element of your web page (`document.documentElement`). This is the magic trigger for visual changes!\n6.  **`ThemeContext.Provider`**: This is how `ThemeProvider` makes the current `theme` and the `toggleTheme` function available to *any* component inside it (like `App.jsx`), so they can use `useContext(ThemeContext)` to access them.\n\n#### 2. `tailwind.config.js`: The Styling Brain\n\nOur app uses a styling tool called Tailwind CSS. For themes, Tailwind CSS has a special setting.\n\n```javascript\n// tailwind.config.js\n/** @type {import('tailwindcss').Config} */\nexport default {\n  content: [\n    \"./index.html\",\n    \"./src/**/*.{js,ts,jsx,tsx}\",\n  ],\n  theme: {\n    extend: {},\n  },\n  plugins: [],\n  darkMode: 'class', // <--- This is the key!\n}\n```\n*Explanation*: The `darkMode: 'class'` line tells Tailwind CSS: \"Hey, when the `dark` class is present on the `<html>` element (or any parent element), apply my 'dark mode' styles!\"\n\n#### 3. Styling Components with `dark:` Prefix\n\nNow, how do our components actually *use* this system to look different? We use special `dark:` prefixes in our Tailwind CSS classes.\n\nTake a look at `App.jsx` again:\n\n```javascript\n// src/App.jsx (simplified)\nconst App = () => {\n  // ... get theme and toggleTheme ...\n\n  return (\n    <div className='bg-gray-200 dark:bg-gray-900'> {/* Background changes */}\n      <h1 className='text-gray-800 dark:text-gray-100'>AI Image Enhancer</h1> {/* Text color changes */}\n      {/* ... button and other elements also use dark: prefixes ... */}\n    </div>\n  );\n};\n```\n*Explanation*:\n*   `bg-gray-200 dark:bg-gray-900`: This means: \"Normally, use a `gray-200` background. BUT, if the `dark` class is present on a parent element (like `<html>`), then use a `gray-900` background instead!\"\n*   `text-gray-800 dark:text-gray-100`: Similarly, this changes the text color from a dark gray to a very light gray when dark mode is active.\n\nEvery element that needs to change its appearance between light and dark modes simply uses these `dark:` prefixed classes. When `ThemeContext.jsx` adds or removes the `dark` class from the `<html>` tag, Tailwind CSS automatically picks up these styles and updates the whole app's look!\n\n### Conclusion\n\nIn this chapter, we've explored the **Theme Management System**, which acts as the \"light switch\" for our `Simple-AI-Image-Enhancer` application. We learned how `ThemeContext.jsx` manages the theme state, remembers your preference using `localStorage`, and dynamically changes the app's appearance by adding or removing a `dark` class on the main HTML element. We also saw how Tailwind CSS, with its `darkMode: 'class'` setting and `dark:` prefixed styles, makes applying these visual changes across the application incredibly efficient and straightforward.\n\nThis completes our tutorial on the core components of the `Simple-AI-Image-Enhancer`. You now have a solid understanding of how the frontend, backend, and various internal systems work together to provide a seamless image enhancement experience!"}