from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from clerk_backend_api import Clerk
from jose import jwt, JWTError
import requests
import json
import logging
from typing import Dict, Any, Optional
from ..config import settings
from ..supabase_client import supabase

logger = logging.getLogger(__name__)
security = HTTPBearer()

# Initialize Clerk with proper backend API
clerk = Clerk(bearer_auth=settings.CLERK_SECRET_KEY)


async def verify_clerk_jwt_with_public_key(token: str) -> Dict[str, Any]:
    """
    Step 6-7: Verify JWT signature with Clerk's Public key and get validation result
    """
    try:
        # Get Clerk's public key for JWT verification
        jwks_url = f"https://api.clerk.com/v1/jwks"

        # For production, you should cache the JWKS and implement proper key rotation
        # For now, we'll use Clerk's verification endpoint
        headers = {
            "Authorization": f"Bearer {settings.CLERK_SECRET_KEY}",
            "Content-Type": "application/json"
        }

        # Use Clerk's verify endpoint
        verify_url = "https://api.clerk.com/v1/sessions/verify"
        response = requests.post(
            verify_url,
            headers=headers,
            json={"token": token}
        )

        if response.status_code == 200:
            # Token is valid, decode it to get payload
            payload = jwt.decode(token, options={"verify_signature": False})
            logger.info(f"Clerk JWT verification successful for user: {payload.get('sub')}")
            return payload
        else:
            logger.error(f"Clerk JWT verification failed: {response.status_code} - {response.text}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token: Clerk verification failed"
            )

    except requests.RequestException as e:
        logger.error(f"Error connecting to Clerk verification service: {e}")
        # Fallback to local JWT decode for development
        try:
            payload = jwt.decode(token, options={"verify_signature": False})
            logger.warning("Using fallback JWT decode without signature verification")
            return payload
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token format"
            )
    except Exception as e:
        logger.error(f"Unexpected error during JWT verification: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token verification failed"
        )


async def initialize_user_in_supabase(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Step 9-10: Initialize user in Supabase and return success
    """
    try:
        clerk_user_id = user_data.get("sub")
        email = user_data.get("email")

        # Handle case when Supabase is not configured
        if not supabase:
            logger.warning("Supabase not configured, returning mock user profile")
            return {
                "id": 1,
                "clerk_user_id": clerk_user_id,
                "github_username": user_data.get("username") or email.split("@")[0] if email else None,
                "github_avatar_url": user_data.get("picture"),
                "is_active": True
            }

        # Check if user profile already exists
        existing_profile = supabase.table("user_profiles").select("*").eq("clerk_user_id", clerk_user_id).execute()

        if existing_profile.data:
            # User exists, update last seen
            updated_profile = supabase.table("user_profiles").update({
                "updated_at": "now()"
            }).eq("clerk_user_id", clerk_user_id).execute()

            logger.info(f"Updated existing user profile for Clerk user: {clerk_user_id}")
            return existing_profile.data[0]
        else:
            # Create new user profile
            new_profile_data = {
                "clerk_user_id": clerk_user_id,
                "github_username": user_data.get("username") or email.split("@")[0] if email else None,
                "github_avatar_url": user_data.get("picture"),
                "is_active": True
            }

            created_profile = supabase.table("user_profiles").insert(new_profile_data).execute()

            if created_profile.data:
                logger.info(f"Created new user profile for Clerk user: {clerk_user_id}")
                return created_profile.data[0]
            else:
                logger.error(f"Failed to create user profile in Supabase: {created_profile}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to initialize user in database"
                )

    except Exception as e:
        logger.error(f"Error initializing user in Supabase: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database initialization failed: {str(e)}"
        )


async def verify_clerk_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """
    Steps 5-8: Extract token, verify with Clerk, and extract user_id from JWT payload
    """
    # Step 5: Backend extracts the token
    token = credentials.credentials
    logger.info(f"Extracted JWT token from request header")

    # Steps 6-7: Verify JWT signature with Clerk's Public key
    payload = await verify_clerk_jwt_with_public_key(token)

    # Step 8: Extract user_id from JWT Payload
    user_id = payload.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token: no user ID found in payload"
        )

    logger.info(f"Successfully extracted user_id from JWT: {user_id}")
    return payload


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """
    Complete flow: Steps 5-10 - Verify token and initialize user in Supabase
    """
    # Steps 5-8: Verify token and extract user data
    payload = await verify_clerk_token(credentials)

    # Steps 9-10: Initialize user in Supabase
    user_profile = await initialize_user_in_supabase(payload)

    # Return combined user data
    return {
        "id": payload.get("sub"),
        "clerk_user_id": payload.get("sub"),
        "email": payload.get("email"),
        "username": payload.get("username") or payload.get("email", "").split("@")[0],
        "first_name": payload.get("given_name"),
        "last_name": payload.get("family_name"),
        "profile_id": user_profile.get("id"),
        "github_username": user_profile.get("github_username"),
        "github_avatar_url": user_profile.get("github_avatar_url"),
        "metadata": payload.get("public_metadata", {})
    }