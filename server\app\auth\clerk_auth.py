from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from clerk_sdk import Clerk
from jose import jwt, JWTError
import json
from ..config import settings

security = HTTPBearer()

# Initialize Clerk
clerk = Clerk(api_key=settings.CLERK_SECRET_KEY)

def verify_clerk_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Verify Clerk JWT token and return user data."""
    token = credentials.credentials
    
    try:
        # Verify the JWT using Clerk's JWT verification key
        # You'll need to implement proper JWT verification here
        # For now, we'll decode without verification for development
        payload = jwt.decode(
            token,
            options={"verify_signature": False}  # Disable for development only
        )
        
        return payload
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

def get_current_user(payload: dict = Depends(verify_clerk_token)):
    """Get current user information from verified token."""
    user_id = payload.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token: no user ID found"
        )
    
    return {
        "id": user_id,
        "email": payload.get("email"),
        "username": payload.get("username"),
        "metadata": payload.get("public_metadata", {})
    } 