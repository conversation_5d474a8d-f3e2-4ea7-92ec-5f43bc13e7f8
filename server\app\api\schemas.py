from typing import List, Optional, Dict, Any
from datetime import datetime, date
from pydantic import BaseModel, EmailStr, Field, root_validator

# Core schemas
class UserBase(BaseModel):
    username: str
    email: Optional[EmailStr] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: bool = True

class User(UserBase):
    id: int

    class Config:
        from_attributes = True

class OrganizationBase(BaseModel):
    name: str
    github_id: str
    github_url: str

class Organization(OrganizationBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class UserProfileBase(BaseModel):
    github_id: str
    github_username: str
    github_avatar_url: Optional[str] = None
    is_active: bool = True

class UserProfile(UserProfileBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class RepositoryBase(BaseModel):
    name: str
    full_name: str
    github_id: str
    github_url: str
    description: Optional[str] = None
    is_private: bool = False

class Repository(RepositoryBase):
    id: int
    organization_id: Optional[int] = None
    owner_id: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class UserRepositoryAccessBase(BaseModel):
    user_id: int
    repository_id: int
    access_level: str

class UserRepositoryAccess(UserRepositoryAccessBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class SettingBase(BaseModel):
    key: str
    value: str
    scope: str
    user_id: Optional[int] = None
    repository_id: Optional[int] = None
    organization_id: Optional[int] = None

class Setting(SettingBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# GitHub schemas
class PullRequestBase(BaseModel):
    repository_id: int
    github_id: int
    number: int
    title: str
    description: Optional[str] = None
    state: str
    base_branch: str
    head_branch: str
    is_draft: bool = False

class PullRequest(PullRequestBase):
    id: int
    created_by_id: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None
    merged_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class PRCommentBase(BaseModel):
    pull_request_id: int
    body: str
    path: Optional[str] = None
    position: Optional[int] = None
    is_ai_generated: bool = False
    ai_model: Optional[str] = None

class PRComment(PRCommentBase):
    id: int
    github_id: Optional[int] = None
    user_id: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class PRReviewBase(BaseModel):
    pull_request_id: int
    status: str
    body: Optional[str] = None
    is_ai_generated: bool = False
    ai_status: Optional[str] = None
    ai_model: Optional[str] = None

class PRReview(PRReviewBase):
    id: int
    github_id: Optional[int] = None
    user_id: Optional[int] = None
    created_at: datetime
    submitted_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class GithubFileBase(BaseModel):
    repository_id: int
    path: str
    content: Optional[str] = None
    sha: str
    size: int
    last_modified: datetime

class GithubFile(GithubFileBase):
    id: int

    class Config:
        from_attributes = True

class GithubActionBase(BaseModel):
    action_type: str
    status: str = "queued"
    input_data: Optional[Dict[str, Any]] = None
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    user_id: Optional[int] = None
    repository_id: Optional[int] = None
    pull_request_id: Optional[int] = None

class GithubAction(GithubActionBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True

class GithubWebhookBase(BaseModel):
    event_type: str
    payload: Dict[str, Any]
    processed: bool = False
    repository_id: Optional[int] = None

class GithubWebhook(GithubWebhookBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True

# Perplexity schemas
class APIKeyConfigBase(BaseModel):
    name: str
    api_key: str
    is_active: bool = True
    user_id: Optional[int] = None

class APIKeyConfig(APIKeyConfigBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class PerplexitySearch(BaseModel):
    query: str
    model: str = "sonar-pro"
    use_cache: bool = True
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    repository_id: Optional[int] = None

class PerplexityChat(BaseModel):
    messages: List[Dict[str, Any]]
    model: str = "sonar-pro"
    use_cache: bool = True
    stream: bool = False
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    repository_id: Optional[int] = None

class PerplexityResponse(BaseModel):
    response: str
    tokens_used: int
    duration_ms: int
    model: str

# Analytics schemas
class QueryLogBase(BaseModel):
    user_id: int
    query_id: int
    pull_request_id: Optional[int] = None

class QueryLog(QueryLogBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True

class UserActivityBase(BaseModel):
    user_id: int
    action: str
    details: Dict[str, Any] = {}

class UserActivity(UserActivityBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True

class UsageMetricBase(BaseModel):
    metric: str
    value: int
    date: date

class UsageMetric(UsageMetricBase):
    id: int

    class Config:
        from_attributes = True

# Tutor schemas
class TutorRequest(BaseModel):
    repository_id: int
    include_patterns: Optional[List[str]] = None
    exclude_patterns: Optional[List[str]] = None
    language: str = "english"
    max_abstractions: int = 10
    use_cache: bool = True

# New hybrid repository fetching schemas
class RepositoryUrlRequest(BaseModel):
    """Schema for fetching public repositories by URL."""
    repository_url: str
    github_token: Optional[str] = None  # Optional token for private repos
    include_patterns: Optional[List[str]] = None
    exclude_patterns: Optional[List[str]] = None
    language: str = "english"
    max_abstractions: int = 10
    use_cache: bool = True

class RepositoryFetchResponse(BaseModel):
    """Response schema for repository fetching."""
    repository_url: str
    is_private: bool
    files_count: int
    message: str

class TutorResponse(BaseModel):
    github_action_id: int
    status: str
    message: str

class TutorFile(BaseModel):
    path: str
    content: str

class TutorStatus(BaseModel):
    github_action_id: int
    status: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

    class Config:
        from_attributes = True

# Repository Analysis schemas
class RepositoryAnalysisBase(BaseModel):
    repository_id: int
    analysis_type: str
    status: str = "pending"

class RepositoryAnalysis(RepositoryAnalysisBase):
    id: int
    dependencies_count: int = 0
    security_vulnerabilities_count: int = 0
    technology_score: float = 0.0
    started_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    analysis_data: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True

class DependencyAnalysisBase(BaseModel):
    analysis_id: int
    package_name: str
    current_version: Optional[str] = None
    latest_version: Optional[str] = None
    package_manager: str

class DependencyAnalysis(DependencyAnalysisBase):
    id: int
    is_outdated: bool = False
    versions_behind: int = 0
    has_vulnerabilities: bool = False
    vulnerability_count: int = 0
    sonar_analysis: Optional[Dict[str, Any]] = None
    alternative_packages: Optional[List[Dict[str, Any]]] = None
    recent_discussions: Optional[List[Dict[str, Any]]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class TechnologyTrendBase(BaseModel):
    analysis_id: int
    technology_name: str
    technology_type: str
    usage_percentage: float = 0.0

class TechnologyTrend(TechnologyTrendBase):
    id: int
    trend_direction: str
    trend_score: float = 0.0
    popularity_rank: Optional[int] = None
    recent_articles: Optional[List[Dict[str, Any]]] = None
    comparisons: Optional[List[Dict[str, Any]]] = None
    migration_guides: Optional[List[Dict[str, Any]]] = None
    community_sentiment: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class SecurityFindingBase(BaseModel):
    analysis_id: int
    severity: str
    package_name: str
    title: str
    description: str

class SecurityFinding(SecurityFindingBase):
    id: int
    cve_id: Optional[str] = None
    affected_versions: Optional[str] = None
    solution: Optional[str] = None
    related_discussions: Optional[List[Dict[str, Any]]] = None
    fix_examples: Optional[List[Dict[str, Any]]] = None
    created_at: datetime

    class Config:
        from_attributes = True

class IssueAugmentationBase(BaseModel):
    repository_id: int
    issue_number: int
    issue_title: str
    issue_body: Optional[str] = None

class IssueAugmentation(IssueAugmentationBase):
    id: int
    github_issue_id: Optional[int] = None
    issue_labels: Optional[List[str]] = None
    analysis_status: str = "pending"
    similar_issues: Optional[List[Dict[str, Any]]] = None
    potential_solutions: Optional[List[Dict[str, Any]]] = None
    relevant_documentation: Optional[List[Dict[str, Any]]] = None
    stack_overflow_links: Optional[List[Dict[str, Any]]] = None
    suggested_comment: Optional[str] = None
    comment_posted: bool = False
    github_comment_id: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# Request schemas for repository analysis
class RepositoryAnalysisRequest(BaseModel):
    analysis_type: str = "full"  # 'dependency', 'technology_trend', 'security', 'full'

class IssueAnalysisRequest(BaseModel):
    issue_number: int
    issue_title: str
    issue_body: Optional[str] = None
    issue_labels: Optional[List[str]] = None

class RepositoryAnalysisResponse(BaseModel):
    analysis: RepositoryAnalysis
    dependencies: Optional[List[DependencyAnalysis]] = None
    technologies: Optional[List[TechnologyTrend]] = None
    security_findings: Optional[List[SecurityFinding]] = None
