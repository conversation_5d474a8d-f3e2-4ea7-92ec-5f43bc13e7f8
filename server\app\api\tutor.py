from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
import os
import logging
import json
import tempfile
from datetime import datetime
from typing import Dict, Any, Optional, List

from ..database import get_db
from ..models.github import GithubAction
from ..supabase_client import supabase
from . import schemas

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import our local tutor modules
from ..utils.tutor import create_tutorial_flow, crawl_github_files
from ..auth.clerk_auth import get_current_user

router = APIRouter()

@router.post("/generate-tutorial/", response_model=schemas.TutorResponse)
async def start_generate_tutorial(
    request: schemas.TutorRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Start generating a tutorial for a GitHub repository.
    This is an async endpoint that will start the process in the background.
    Note: This endpoint is deprecated. Use /generate-tutorial-from-url/ instead.
    """
    logger.info(f"--- TUTOR_API start_generate_tutorial: ENTERING. Request for repo_id: {request.repository_id} ---")
    logger.info(f"--- TUTOR_API start_generate_tutorial: Authenticated user: {current_user['username']} ({current_user['id']}) ---")

    # This endpoint is deprecated since we're moving to URL-based approach
    raise HTTPException(
        status_code=status.HTTP_410_GONE,
        detail="This endpoint is deprecated. Please use /generate-tutorial-from-url/ with a repository URL instead."
    )

@router.post("/generate-tutorial-from-url/", response_model=schemas.TutorResponse)
async def start_generate_tutorial_from_url(
    request: schemas.RepositoryUrlRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Start generating a tutorial for a GitHub repository using a URL.
    This supports both public repositories (no token needed) and private repositories (with token).

    Following the API request flow:
    1. User triggers generate tutorial endpoint ✓
    2-3. Frontend gets session token from Clerk ✓
    4. Frontend POST request with JWT token in header ✓
    5-8. Backend extracts and verifies token with Clerk ✓ (handled in get_current_user)
    9-10. Initialize user in Supabase ✓ (handled in get_current_user)
    11. Generate tutorials and store in database ✓ (this function)
    12. Return success to frontend ✓
    """
    logger.info(f"=== TUTORIAL GENERATION FLOW START ===")
    logger.info(f"Step 11: Generating tutorial for repo: {request.repository_url}")
    logger.info(f"Authenticated Clerk user: {current_user['username']} ({current_user['clerk_user_id']})")
    logger.info(f"Supabase profile_id: {current_user['profile_id']}")

    # Validate the repository URL format
    if not request.repository_url.startswith(('https://github.com/', '**************:')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid GitHub repository URL. Must start with 'https://github.com/' or '**************:'"
        )

    try:
        # Step 11: Store tutorial generation request in Supabase
        tutorial_data = {
            "user_profile_id": current_user["profile_id"],
            "clerk_user_id": current_user["clerk_user_id"],
            "repository_url": request.repository_url,
            "action_type": "generate_tutorial_from_url",
            "status": "queued",
            "input_data": {
                "repository_url": request.repository_url,
                "has_github_token": bool(request.github_token),
                "include_patterns": request.include_patterns,
                "exclude_patterns": request.exclude_patterns,
                "language": request.language,
                "max_abstractions": request.max_abstractions,
                "use_cache": request.use_cache,
                "clerk_user_id": current_user["clerk_user_id"]
            }
        }

        tutorial_id = None

        # Insert into Supabase github_actions table if available
        if supabase:
            supabase_result = supabase.table("github_actions").insert(tutorial_data).execute()

            if supabase_result.data:
                tutorial_record = supabase_result.data[0]
                tutorial_id = tutorial_record["id"]
                logger.info(f"Created tutorial record in Supabase with ID: {tutorial_id}")
            else:
                logger.warning("Failed to create tutorial record in Supabase, falling back to legacy DB")
        else:
            logger.warning("Supabase not available, using legacy database only")

        # Also create a legacy record for compatibility (optional)
        github_action = GithubAction(
            action_type="generate_tutorial_from_url",
            status="queued",
            input_data=tutorial_data["input_data"],
            result_data=None,
            user_id=None,
            repository_id=None
        )

        db.add(github_action)
        db.commit()
        db.refresh(github_action)

        # Schedule the tutorial generation in the background
        background_tasks.add_task(
            generate_tutorial_with_supabase,
            supabase_tutorial_id=tutorial_id,
            legacy_github_action_id=github_action.id,
            repository_url=request.repository_url,
            include_patterns=request.include_patterns,
            exclude_patterns=request.exclude_patterns,
            language=request.language,
            max_abstractions=request.max_abstractions,
            use_cache=request.use_cache,
            github_token=request.github_token,
            clerk_user_id=current_user["clerk_user_id"],
            db=db
        )

        logger.info(f"=== TUTORIAL GENERATION FLOW: Background task scheduled ===")

        # Step 12: Return success to frontend
        return {
            "github_action_id": tutorial_id or github_action.id,  # Return Supabase ID or legacy ID
            "status": "queued",
            "message": f"Tutorial generation started from URL - stored in {'Supabase' if tutorial_id else 'legacy database'}"
        }

    except Exception as e:
        logger.error(f"Error in tutorial generation flow: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start tutorial generation: {str(e)}"
        )

@router.post("/test-repository-access/", response_model=schemas.RepositoryFetchResponse)
async def test_repository_access(
    request: schemas.RepositoryUrlRequest,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Test access to a GitHub repository without generating a tutorial.
    This helps users verify if their repository URL and token (if needed) work correctly.
    """
    logger.info(f"--- TUTOR_API test_repository_access: ENTERING. Request for repo_url: {request.repository_url} ---")
    logger.info(f"--- TUTOR_API test_repository_access: Authenticated user: {current_user['username']} ({current_user['id']}) ---")

    # Validate the repository URL format
    if not request.repository_url.startswith(('https://github.com/', '**************:')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid GitHub repository URL. Must start with 'https://github.com/' or '**************:'"
        )

    try:
        # Test repository access using the crawl function
        result = crawl_github_files(
            repo_url=request.repository_url,
            token=request.github_token,
            max_file_size=1000,  # Small size for testing
            enable_two_phase=False  # Disable for quick test
        )

        if "error" in result.get("stats", {}):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Repository access failed: {result['stats']['error']}"
            )

        files_count = result.get("stats", {}).get("downloaded_count", 0)
        is_private = bool(request.github_token)  # Assume private if token provided

        return {
            "repository_url": request.repository_url,
            "is_private": is_private,
            "files_count": files_count,
            "message": f"Successfully accessed repository. Found {files_count} files."
        }

    except Exception as e:
        logger.error(f"--- TUTOR_API test_repository_access: Error testing access: {str(e)} ---")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to access repository: {str(e)}"
        )

@router.get("/tutorial-status/{github_action_id}", response_model=schemas.TutorStatus)
async def get_tutorial_status(
    github_action_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get the status of a tutorial generation process from Supabase.
    """
    logger.info(f"--- TUTOR_API get_tutorial_status: ENTERING. tutorial_id: {github_action_id} ---")
    logger.info(f"--- TUTOR_API get_tutorial_status: Authenticated user: {current_user['username']} ({current_user['clerk_user_id']}) ---")

    try:
        # First try to get from Supabase
        supabase_result = supabase.table("github_actions").select("*").eq("id", github_action_id).execute()

        if supabase_result.data:
            tutorial_record = supabase_result.data[0]

            # Check if user has access to this tutorial
            if tutorial_record.get("clerk_user_id") != current_user["clerk_user_id"]:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You don't have permission to access this tutorial"
                )

            logger.info(f"Found tutorial in Supabase: {tutorial_record['status']}")
            return {
                "github_action_id": tutorial_record["id"],
                "status": tutorial_record["status"],
                "result": tutorial_record.get("result_data"),
                "error": tutorial_record.get("error_message")
            }

        # Fallback to legacy database
        github_action = db.query(GithubAction).filter(GithubAction.id == github_action_id).first()
        if not github_action:
            logger.warning(f"--- TUTOR_API get_tutorial_status: Tutorial ID {github_action_id} not found in either database ---")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tutorial generation task not found"
            )

        # Check if the user has permission to access this tutorial (legacy)
        user_has_access = False
        if github_action.input_data and github_action.input_data.get("clerk_user_id") == current_user["clerk_user_id"]:
            user_has_access = True
        elif github_action.user_id and str(github_action.user_id) == current_user["clerk_user_id"]:
            user_has_access = True

        if not user_has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to access this tutorial"
            )

        logger.info(f"Found tutorial in legacy DB: {github_action.status}")
        return {
            "github_action_id": github_action.id,
            "status": github_action.status,
            "result": github_action.result_data,
            "error": github_action.error_message
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting tutorial status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tutorial status: {str(e)}"
        )

@router.get("/repository-tutorials/{repository_id}", response_model=List[schemas.TutorStatus])
async def get_repository_tutorials(
    repository_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all tutorials generated for a specific repository.
    Note: This endpoint is deprecated. Use /user-tutorials/ instead.
    """
    logger.info(f"--- TUTOR_API get_repository_tutorials: ENTERING. repo_id={repository_id} ---")
    logger.info(f"--- TUTOR_API get_repository_tutorials: Authenticated user: {current_user['username']} ({current_user['id']}) ---")

    # This endpoint is deprecated since we're moving to URL-based approach
    raise HTTPException(
        status_code=status.HTTP_410_GONE,
        detail="This endpoint is deprecated. Please use /user-tutorials/ to get all tutorials for the current user."
    )

@router.get("/user-tutorials/", response_model=List[schemas.TutorStatus])
async def get_user_tutorials(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all tutorials generated by the current user from Supabase.
    """
    logger.info(f"--- TUTOR_API get_user_tutorials: ENTERING for user: {current_user['username']} ({current_user['clerk_user_id']}) ---")

    try:
        user_tutorials = []

        # Get tutorials from Supabase
        supabase_result = supabase.table("github_actions").select("*").eq("clerk_user_id", current_user["clerk_user_id"]).execute()

        if supabase_result.data:
            for tutorial in supabase_result.data:
                user_tutorials.append({
                    "github_action_id": tutorial["id"],
                    "status": tutorial["status"],
                    "result": tutorial.get("result_data"),
                    "error": tutorial.get("error_message")
                })
            logger.info(f"Found {len(supabase_result.data)} tutorials in Supabase")

        # Also get legacy tutorials for backward compatibility
        github_actions = db.query(GithubAction).filter(
            GithubAction.action_type.in_(["generate_tutorial", "generate_tutorial_from_url"])
        ).all()

        legacy_count = 0
        for action in github_actions:
            user_has_access = False

            # Check for new URL-based tutorials
            if action.input_data and action.input_data.get("clerk_user_id") == current_user["clerk_user_id"]:
                user_has_access = True
            # Check for legacy tutorials (if user_id matches clerk user id)
            elif action.user_id and str(action.user_id) == current_user["clerk_user_id"]:
                user_has_access = True

            if user_has_access:
                # Avoid duplicates (check if this tutorial is already in Supabase results)
                if not any(t["github_action_id"] == action.id for t in user_tutorials):
                    user_tutorials.append({
                        "github_action_id": action.id,
                        "status": action.status,
                        "result": action.result_data,
                        "error": action.error_message
                    })
                    legacy_count += 1

        logger.info(f"Found {legacy_count} additional tutorials in legacy DB")
        logger.info(f"--- TUTOR_API get_user_tutorials: Total {len(user_tutorials)} tutorials for user ---")

        # Sort by ID (most recent first)
        user_tutorials.sort(key=lambda x: x["github_action_id"], reverse=True)

        return user_tutorials

    except Exception as e:
        logger.error(f"Error getting user tutorials: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user tutorials: {str(e)}"
        )

async def generate_tutorial_with_supabase(
    supabase_tutorial_id: int,
    legacy_github_action_id: int,
    repository_url: str,
    include_patterns: Optional[List[str]] = None,
    exclude_patterns: Optional[List[str]] = None,
    language: str = "english",
    max_abstractions: int = 10,
    use_cache: bool = True,
    github_token: Optional[str] = None,
    clerk_user_id: str = None,
    db: Session = None
):
    """
    Background task to generate a tutorial and store results in Supabase.
    This follows the complete API flow including storing results in Supabase.
    """
    logger.info(f"=== TUTORIAL GENERATION BACKGROUND TASK START ===")
    logger.info(f"Supabase tutorial ID: {supabase_tutorial_id}")
    logger.info(f"Repository URL: {repository_url}")
    logger.info(f"Clerk user ID: {clerk_user_id}")

    try:
        # Update status to 'in_progress' in both Supabase and legacy DB
        supabase.table("github_actions").update({
            "status": "in_progress",
            "updated_at": "now()"
        }).eq("id", supabase_tutorial_id).execute()

        if legacy_github_action_id:
            github_action = db.query(GithubAction).filter(GithubAction.id == legacy_github_action_id).first()
            if github_action:
                github_action.status = "in_progress"
                db.commit()

        # Create a temporary output directory
        output_dir = tempfile.mkdtemp(prefix="tutor_output_")
        logger.info(f"Created temporary output directory: {output_dir}")

        # Convert include/exclude patterns to sets if provided
        include_set = set(include_patterns) if include_patterns else None
        exclude_set = set(exclude_patterns) if exclude_patterns else None

        # Initialize shared dictionary with inputs
        shared = {
            "repo_url": repository_url,
            "local_dir": None,
            "project_name": None,  # Will be derived from URL
            "github_token": github_token,
            "output_dir": output_dir,
            "include_patterns": include_set,
            "exclude_patterns": exclude_set,
            "max_file_size": 100000,  # 100KB
            "language": language,
            "use_cache": use_cache,
            "max_abstraction_num": max_abstractions,
            "files": [],
            "abstractions": [],
            "relationships": {},
            "chapter_order": [],
            "chapters": [],
            "final_output_dir": None
        }

        # Create and run the tutorial flow
        tutorial_flow = create_tutorial_flow()
        tutorial_flow.run(shared)

        # Prepare result data
        result_data = None
        if shared.get("final_output_dir"):
            # Get list of generated files
            generated_files = []
            for root, _, files in os.walk(shared["final_output_dir"]):
                for file in files:
                    file_path = os.path.join(root, file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    relative_path = os.path.relpath(file_path, shared["final_output_dir"])
                    generated_files.append({
                        "path": relative_path,
                        "content": content
                    })

            result_data = {
                "output_dir": shared["final_output_dir"],
                "files": generated_files,
                "project_name": shared.get("project_name", ""),
                "abstractions_count": len(shared.get("abstractions", [])),
                "files_processed": len(shared.get("files", [])),
                "generation_completed_at": datetime.utcnow().isoformat()
            }

            # Update Supabase with success
            supabase.table("github_actions").update({
                "status": "completed",
                "result_data": result_data,
                "updated_at": "now()"
            }).eq("id", supabase_tutorial_id).execute()

            # Update legacy DB
            if legacy_github_action_id and github_action:
                github_action.result_data = result_data
                github_action.status = "completed"
                db.commit()

            logger.info(f"=== TUTORIAL GENERATION COMPLETED SUCCESSFULLY ===")
            logger.info(f"Generated {len(generated_files)} files")
            logger.info(f"Processed {len(shared.get('files', []))} source files")

        else:
            # Update with failure
            error_msg = "Failed to generate tutorial - no output directory"

            supabase.table("github_actions").update({
                "status": "failed",
                "error_message": error_msg,
                "updated_at": "now()"
            }).eq("id", supabase_tutorial_id).execute()

            if legacy_github_action_id and github_action:
                github_action.status = "failed"
                github_action.error_message = error_msg
                db.commit()

            logger.error(f"=== TUTORIAL GENERATION FAILED: {error_msg} ===")

    except Exception as e:
        error_msg = str(e)
        logger.error(f"=== TUTORIAL GENERATION ERROR: {error_msg} ===")

        # Update both databases with error
        try:
            supabase.table("github_actions").update({
                "status": "failed",
                "error_message": error_msg,
                "updated_at": "now()"
            }).eq("id", supabase_tutorial_id).execute()
        except Exception as supabase_error:
            logger.error(f"Failed to update Supabase with error: {supabase_error}")

        if db and legacy_github_action_id:
            try:
                github_action = db.query(GithubAction).filter(GithubAction.id == legacy_github_action_id).first()
                if github_action:
                    github_action.status = "failed"
                    github_action.error_message = error_msg
                    db.commit()
            except Exception as db_error:
                logger.error(f"Failed to update legacy DB with error: {db_error}")

        raise

async def generate_tutorial(
    github_action_id: int,
    repository_url: str,
    include_patterns: Optional[List[str]] = None,
    exclude_patterns: Optional[List[str]] = None,
    language: str = "english",
    max_abstractions: int = 10,
    use_cache: bool = True,
    github_token: Optional[str] = None,
    db: Session = None
):
    """
    Background task to generate a tutorial for a GitHub repository.
    """
    logger.info(f"Starting tutorial generation for {repository_url}")

    try:
        # Update the action status to 'in_progress'
        github_action = db.query(GithubAction).filter(GithubAction.id == github_action_id).first()
        github_action.status = "in_progress"
        db.commit()

        # Create a temporary output directory
        output_dir = tempfile.mkdtemp(prefix="tutor_output_")
        logger.info(f"Created temporary output directory: {output_dir}")

        # Convert include/exclude patterns to sets if provided
        include_set = set(include_patterns) if include_patterns else None
        exclude_set = set(exclude_patterns) if exclude_patterns else None

        # Initialize shared dictionary with inputs
        shared = {
            "repo_url": repository_url,
            "local_dir": None,
            "project_name": None,  # Will be derived from URL
            "github_token": github_token,
            "output_dir": output_dir,
            "include_patterns": include_set,
            "exclude_patterns": exclude_set,
            "max_file_size": 100000,  # 100KB
            "language": language,
            "use_cache": use_cache,
            "max_abstraction_num": max_abstractions,
            "files": [],
            "abstractions": [],
            "relationships": {},
            "chapter_order": [],
            "chapters": [],
            "final_output_dir": None
        }

        # Create and run the tutorial flow
        tutorial_flow = create_tutorial_flow()
        tutorial_flow.run(shared)

        # Update the action with the result
        if shared.get("final_output_dir"):
            # Get list of generated files
            generated_files = []
            for root, _, files in os.walk(shared["final_output_dir"]):
                for file in files:
                    file_path = os.path.join(root, file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    relative_path = os.path.relpath(file_path, shared["final_output_dir"])
                    generated_files.append({
                        "path": relative_path,
                        "content": content
                    })

            # Update action result
            github_action.result_data = {
                "output_dir": shared["final_output_dir"],
                "files": generated_files,
                "project_name": shared.get("project_name", "")
            }
            github_action.status = "completed"
        else:
            github_action.status = "failed"
            github_action.error_message = "Failed to generate tutorial - no output directory"

        db.commit()
        logger.info(f"Tutorial generation completed for {repository_url}")

    except Exception as e:
        logger.error(f"Error generating tutorial: {str(e)}")
        if db:
            github_action = db.query(GithubAction).filter(GithubAction.id == github_action_id).first()
            if github_action:
                github_action.status = "failed"
                github_action.error_message = str(e)
                db.commit()
        raise
