from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
import os
import logging
import json
import tempfile
from datetime import datetime
from typing import Dict, Any, Optional, List

from ..database import get_db
from ..models.github import GithubAction
from . import schemas

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import our local tutor modules
from ..utils.tutor import create_tutorial_flow, crawl_github_files
from ..auth.clerk_auth import get_current_user

router = APIRouter()

@router.post("/generate-tutorial/", response_model=schemas.TutorResponse)
async def start_generate_tutorial(
    request: schemas.TutorRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Start generating a tutorial for a GitHub repository.
    This is an async endpoint that will start the process in the background.
    Note: This endpoint is deprecated. Use /generate-tutorial-from-url/ instead.
    """
    logger.info(f"--- TUTOR_API start_generate_tutorial: ENTERING. Request for repo_id: {request.repository_id} ---")
    logger.info(f"--- TUTOR_API start_generate_tutorial: Authenticated user: {current_user['username']} ({current_user['id']}) ---")

    # This endpoint is deprecated since we're moving to URL-based approach
    raise HTTPException(
        status_code=status.HTTP_410_GONE,
        detail="This endpoint is deprecated. Please use /generate-tutorial-from-url/ with a repository URL instead."
    )

@router.post("/generate-tutorial-from-url/", response_model=schemas.TutorResponse)
async def start_generate_tutorial_from_url(
    request: schemas.RepositoryUrlRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Start generating a tutorial for a GitHub repository using a URL.
    This supports both public repositories (no token needed) and private repositories (with token).
    """
    logger.info(f"--- TUTOR_API start_generate_tutorial_from_url: ENTERING. Request for repo_url: {request.repository_url} ---")
    logger.info(f"--- TUTOR_API start_generate_tutorial_from_url: Authenticated user: {current_user['username']} ({current_user['id']}) ---")

    # Validate the repository URL format
    if not request.repository_url.startswith(('https://github.com/', '**************:')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid GitHub repository URL. Must start with 'https://github.com/' or '**************:'"
        )

    # Create a GithubAction record to track the tutorial generation
    github_action = GithubAction(
        action_type="generate_tutorial_from_url",
        status="queued",
        input_data={
            "repository_url": request.repository_url,
            "has_github_token": bool(request.github_token),
            "include_patterns": request.include_patterns,
            "exclude_patterns": request.exclude_patterns,
            "language": request.language,
            "max_abstractions": request.max_abstractions,
            "use_cache": request.use_cache,
            "clerk_user_id": current_user["id"]
        },
        result_data=None,
        user_id=None,  # We'll use clerk_user_id instead
        repository_id=None  # No repository ID since we're working with URLs directly
    )

    db.add(github_action)
    db.commit()
    db.refresh(github_action)

    # Schedule the tutorial generation in the background
    background_tasks.add_task(
        generate_tutorial,
        github_action_id=github_action.id,
        repository_url=request.repository_url,
        include_patterns=request.include_patterns,
        exclude_patterns=request.exclude_patterns,
        language=request.language,
        max_abstractions=request.max_abstractions,
        use_cache=request.use_cache,
        github_token=request.github_token,  # Use the provided token
        db=db
    )

    return {
        "github_action_id": github_action.id,
        "status": "queued",
        "message": "Tutorial generation started from URL"
    }

@router.post("/test-repository-access/", response_model=schemas.RepositoryFetchResponse)
async def test_repository_access(
    request: schemas.RepositoryUrlRequest,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Test access to a GitHub repository without generating a tutorial.
    This helps users verify if their repository URL and token (if needed) work correctly.
    """
    logger.info(f"--- TUTOR_API test_repository_access: ENTERING. Request for repo_url: {request.repository_url} ---")
    logger.info(f"--- TUTOR_API test_repository_access: Authenticated user: {current_user['username']} ({current_user['id']}) ---")

    # Validate the repository URL format
    if not request.repository_url.startswith(('https://github.com/', '**************:')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid GitHub repository URL. Must start with 'https://github.com/' or '**************:'"
        )

    try:
        # Test repository access using the crawl function
        result = crawl_github_files(
            repo_url=request.repository_url,
            token=request.github_token,
            max_file_size=1000,  # Small size for testing
            enable_two_phase=False  # Disable for quick test
        )

        if "error" in result.get("stats", {}):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Repository access failed: {result['stats']['error']}"
            )

        files_count = result.get("stats", {}).get("downloaded_count", 0)
        is_private = bool(request.github_token)  # Assume private if token provided

        return {
            "repository_url": request.repository_url,
            "is_private": is_private,
            "files_count": files_count,
            "message": f"Successfully accessed repository. Found {files_count} files."
        }

    except Exception as e:
        logger.error(f"--- TUTOR_API test_repository_access: Error testing access: {str(e)} ---")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to access repository: {str(e)}"
        )

@router.get("/tutorial-status/{github_action_id}", response_model=schemas.TutorStatus)
async def get_tutorial_status(
    github_action_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get the status of a tutorial generation process.
    """
    logger.info(f"--- TUTOR_API get_tutorial_status: ENTERING. github_action_id: {github_action_id} ---")
    logger.info(f"--- TUTOR_API get_tutorial_status: Authenticated user: {current_user['username']} ({current_user['id']}) ---")

    github_action = db.query(GithubAction).filter(GithubAction.id == github_action_id).first()
    if not github_action:
        logger.warning(f"--- TUTOR_API get_tutorial_status: GithubAction ID {github_action_id} not found ---")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tutorial generation task not found"
        )

    # Check if the user has permission to access this tutorial
    # For new URL-based tutorials, check clerk_user_id in input_data
    # For legacy tutorials, check user_id
    user_has_access = False
    if github_action.input_data and github_action.input_data.get("clerk_user_id") == current_user["id"]:
        user_has_access = True
    elif github_action.user_id and str(github_action.user_id) == current_user["id"]:
        user_has_access = True

    if not user_has_access:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to access this tutorial"
        )

    return {
        "github_action_id": github_action.id,
        "status": github_action.status,
        "result": github_action.result_data,
        "error": github_action.error_message
    }

@router.get("/repository-tutorials/{repository_id}", response_model=List[schemas.TutorStatus])
async def get_repository_tutorials(
    repository_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all tutorials generated for a specific repository.
    Note: This endpoint is deprecated. Use /user-tutorials/ instead.
    """
    logger.info(f"--- TUTOR_API get_repository_tutorials: ENTERING. repo_id={repository_id} ---")
    logger.info(f"--- TUTOR_API get_repository_tutorials: Authenticated user: {current_user['username']} ({current_user['id']}) ---")

    # This endpoint is deprecated since we're moving to URL-based approach
    raise HTTPException(
        status_code=status.HTTP_410_GONE,
        detail="This endpoint is deprecated. Please use /user-tutorials/ to get all tutorials for the current user."
    )

@router.get("/user-tutorials/", response_model=List[schemas.TutorStatus])
async def get_user_tutorials(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all tutorials generated by the current user.
    """
    logger.info(f"--- TUTOR_API get_user_tutorials: ENTERING for user: {current_user['username']} ({current_user['id']}) ---")

    # Get all tutorials for this user (both legacy and new URL-based)
    github_actions = db.query(GithubAction).filter(
        GithubAction.action_type.in_(["generate_tutorial", "generate_tutorial_from_url"])
    ).all()

    # Filter by user access
    user_tutorials = []
    for action in github_actions:
        user_has_access = False

        # Check for new URL-based tutorials
        if action.input_data and action.input_data.get("clerk_user_id") == current_user["id"]:
            user_has_access = True
        # Check for legacy tutorials (if user_id matches clerk user id)
        elif action.user_id and str(action.user_id) == current_user["id"]:
            user_has_access = True

        if user_has_access:
            user_tutorials.append({
                "github_action_id": action.id,
                "status": action.status,
                "result": action.result_data,
                "error": action.error_message
            })

    logger.info(f"--- TUTOR_API get_user_tutorials: Found {len(user_tutorials)} tutorials for user ---")
    return user_tutorials

async def generate_tutorial(
    github_action_id: int,
    repository_url: str,
    include_patterns: Optional[List[str]] = None,
    exclude_patterns: Optional[List[str]] = None,
    language: str = "english",
    max_abstractions: int = 10,
    use_cache: bool = True,
    github_token: Optional[str] = None,
    db: Session = None
):
    """
    Background task to generate a tutorial for a GitHub repository.
    """
    logger.info(f"Starting tutorial generation for {repository_url}")

    try:
        # Update the action status to 'in_progress'
        github_action = db.query(GithubAction).filter(GithubAction.id == github_action_id).first()
        github_action.status = "in_progress"
        db.commit()

        # Create a temporary output directory
        output_dir = tempfile.mkdtemp(prefix="tutor_output_")
        logger.info(f"Created temporary output directory: {output_dir}")

        # Convert include/exclude patterns to sets if provided
        include_set = set(include_patterns) if include_patterns else None
        exclude_set = set(exclude_patterns) if exclude_patterns else None

        # Initialize shared dictionary with inputs
        shared = {
            "repo_url": repository_url,
            "local_dir": None,
            "project_name": None,  # Will be derived from URL
            "github_token": github_token,
            "output_dir": output_dir,
            "include_patterns": include_set,
            "exclude_patterns": exclude_set,
            "max_file_size": 100000,  # 100KB
            "language": language,
            "use_cache": use_cache,
            "max_abstraction_num": max_abstractions,
            "files": [],
            "abstractions": [],
            "relationships": {},
            "chapter_order": [],
            "chapters": [],
            "final_output_dir": None
        }

        # Create and run the tutorial flow
        tutorial_flow = create_tutorial_flow()
        tutorial_flow.run(shared)

        # Update the action with the result
        if shared.get("final_output_dir"):
            # Get list of generated files
            generated_files = []
            for root, _, files in os.walk(shared["final_output_dir"]):
                for file in files:
                    file_path = os.path.join(root, file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    relative_path = os.path.relpath(file_path, shared["final_output_dir"])
                    generated_files.append({
                        "path": relative_path,
                        "content": content
                    })

            # Update action result
            github_action.result_data = {
                "output_dir": shared["final_output_dir"],
                "files": generated_files,
                "project_name": shared.get("project_name", "")
            }
            github_action.status = "completed"
        else:
            github_action.status = "failed"
            github_action.error_message = "Failed to generate tutorial - no output directory"

        db.commit()
        logger.info(f"Tutorial generation completed for {repository_url}")

    except Exception as e:
        logger.error(f"Error generating tutorial: {str(e)}")
        if db:
            github_action = db.query(GithubAction).filter(GithubAction.id == github_action_id).first()
            if github_action:
                github_action.status = "failed"
                github_action.error_message = str(e)
                db.commit()
        raise
