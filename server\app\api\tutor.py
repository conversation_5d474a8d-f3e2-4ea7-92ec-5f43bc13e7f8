from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
import os
import logging
import json
import tempfile
from datetime import datetime
from typing import Dict, Any, Optional, List

from ..database import get_db
from ..dependencies import get_current_user_profile
from ..models.core import UserProfile, Repository
from ..models.github import GithubAction
from . import schemas

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import our local tutor modules
from ..utils.tutor import create_tutorial_flow, crawl_github_files
from ..auth.router import oauth2_scheme, get_current_user

router = APIRouter()

@router.post("/generate-tutorial/", response_model=schemas.TutorResponse)
async def start_generate_tutorial(
    request: schemas.TutorRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Start generating a tutorial for a GitHub repository.
    This is an async endpoint that will start the process in the background.
    """
    logger.info(f"--- TUTOR_API start_generate_tutorial: ENTERING. Request for repo_id: {request.repository_id} ---")

    _current_user = await get_current_user(token=token, db=db)
    if not _current_user:
        logger.error("--- TUTOR_API start_generate_tutorial: Could not get current_user from token ---")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token or user not found")

    current_user_profile = await get_current_user_profile(current_user=_current_user, db=db)
    if not current_user_profile:
        logger.error(f"--- TUTOR_API start_generate_tutorial: Could not get user profile for user_id: {_current_user.id} ---")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User profile not found")

    logger.info(f"--- TUTOR_API start_generate_tutorial: Manually resolved profile for user: {current_user_profile.github_username} ---")

    # Validate repository access
    repository = db.query(Repository).filter(Repository.id == request.repository_id).first()
    if not repository:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Repository not found"
        )

    # Create a GithubAction record to track the tutorial generation
    github_action = GithubAction(
        action_type="generate_tutorial",
        status="queued",
        input_data={
            "repository_id": request.repository_id,
            "repository_url": repository.github_url,
            "include_patterns": request.include_patterns,
            "exclude_patterns": request.exclude_patterns,
            "language": request.language,
            "max_abstractions": request.max_abstractions,
            "use_cache": request.use_cache
        },
        result_data=None,
        user_id=current_user_profile.user_id,
        repository_id=repository.id
    )

    db.add(github_action)
    db.commit()
    db.refresh(github_action)

    # Schedule the tutorial generation in the background
    background_tasks.add_task(
        generate_tutorial,
        github_action_id=github_action.id,
        repository_url=repository.github_url,
        include_patterns=request.include_patterns,
        exclude_patterns=request.exclude_patterns,
        language=request.language,
        max_abstractions=request.max_abstractions,
        use_cache=request.use_cache,
        github_token=os.environ.get('GITHUB_TOKEN'),
        db=db
    )

    return {
        "github_action_id": github_action.id,
        "status": "queued",
        "message": "Tutorial generation started"
    }

@router.post("/generate-tutorial-from-url/", response_model=schemas.TutorResponse)
async def start_generate_tutorial_from_url(
    request: schemas.RepositoryUrlRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Start generating a tutorial for a GitHub repository using a URL.
    This supports both public repositories (no token needed) and private repositories (with token).
    """
    logger.info(f"--- TUTOR_API start_generate_tutorial_from_url: ENTERING. Request for repo_url: {request.repository_url} ---")

    _current_user = await get_current_user(token=token, db=db)
    if not _current_user:
        logger.error("--- TUTOR_API start_generate_tutorial_from_url: Could not get current_user from token ---")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token or user not found")

    current_user_profile = await get_current_user_profile(current_user=_current_user, db=db)
    if not current_user_profile:
        logger.error(f"--- TUTOR_API start_generate_tutorial_from_url: Could not get user profile for user_id: {_current_user.id} ---")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User profile not found")

    logger.info(f"--- TUTOR_API start_generate_tutorial_from_url: Manually resolved profile for user: {current_user_profile.github_username} ---")

    # Validate the repository URL format
    if not request.repository_url.startswith(('https://github.com/', '**************:')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid GitHub repository URL. Must start with 'https://github.com/' or '**************:'"
        )

    # Create a GithubAction record to track the tutorial generation
    github_action = GithubAction(
        action_type="generate_tutorial_from_url",
        status="queued",
        input_data={
            "repository_url": request.repository_url,
            "has_github_token": bool(request.github_token),
            "include_patterns": request.include_patterns,
            "exclude_patterns": request.exclude_patterns,
            "language": request.language,
            "max_abstractions": request.max_abstractions,
            "use_cache": request.use_cache
        },
        result_data=None,
        user_id=current_user_profile.user_id,
        repository_id=None  # No repository ID since we're working with URLs directly
    )

    db.add(github_action)
    db.commit()
    db.refresh(github_action)

    # Schedule the tutorial generation in the background
    background_tasks.add_task(
        generate_tutorial,
        github_action_id=github_action.id,
        repository_url=request.repository_url,
        include_patterns=request.include_patterns,
        exclude_patterns=request.exclude_patterns,
        language=request.language,
        max_abstractions=request.max_abstractions,
        use_cache=request.use_cache,
        github_token=request.github_token,  # Use the provided token
        db=db
    )

    return {
        "github_action_id": github_action.id,
        "status": "queued",
        "message": "Tutorial generation started from URL"
    }

@router.post("/test-repository-access/", response_model=schemas.RepositoryFetchResponse)
async def test_repository_access(
    request: schemas.RepositoryUrlRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Test access to a GitHub repository without generating a tutorial.
    This helps users verify if their repository URL and token (if needed) work correctly.
    """
    logger.info(f"--- TUTOR_API test_repository_access: ENTERING. Request for repo_url: {request.repository_url} ---")

    _current_user = await get_current_user(token=token, db=db)
    if not _current_user:
        logger.error("--- TUTOR_API test_repository_access: Could not get current_user from token ---")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token or user not found")

    # Validate the repository URL format
    if not request.repository_url.startswith(('https://github.com/', '**************:')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid GitHub repository URL. Must start with 'https://github.com/' or '**************:'"
        )

    try:
        # Test repository access using the crawl function
        result = crawl_github_files(
            repo_url=request.repository_url,
            token=request.github_token,
            max_file_size=1000,  # Small size for testing
            enable_two_phase=False  # Disable for quick test
        )

        if "error" in result.get("stats", {}):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Repository access failed: {result['stats']['error']}"
            )

        files_count = result.get("stats", {}).get("downloaded_count", 0)
        is_private = bool(request.github_token)  # Assume private if token provided

        return {
            "repository_url": request.repository_url,
            "is_private": is_private,
            "files_count": files_count,
            "message": f"Successfully accessed repository. Found {files_count} files."
        }

    except Exception as e:
        logger.error(f"--- TUTOR_API test_repository_access: Error testing access: {str(e)} ---")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to access repository: {str(e)}"
        )

@router.get("/tutorial-status/{github_action_id}", response_model=schemas.TutorStatus)
async def get_tutorial_status(
    github_action_id: int,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get the status of a tutorial generation process.
    """
    logger.info(f"--- TUTOR_API get_tutorial_status: ENTERING. github_action_id: {github_action_id} ---")

    _current_user = await get_current_user(token=token, db=db)
    if not _current_user:
        logger.error("--- TUTOR_API get_tutorial_status: Could not get current_user from token ---")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token or user not found")

    current_user_profile = await get_current_user_profile(current_user=_current_user, db=db)
    if not current_user_profile:
        logger.error(f"--- TUTOR_API get_tutorial_status: Could not get user profile for user_id: {_current_user.id} ---")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User profile not found")

    logger.info(f"--- TUTOR_API get_tutorial_status: Manually resolved profile for user: {current_user_profile.github_username} ---")

    github_action = db.query(GithubAction).filter(GithubAction.id == github_action_id).first()
    if not github_action:
        logger.warning(f"--- TUTOR_API get_tutorial_status: GithubAction ID {github_action_id} not found ---")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tutorial generation task not found"
        )

    if github_action.user_id != current_user_profile.user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to access this tutorial"
        )

    return {
        "github_action_id": github_action.id,
        "status": github_action.status,
        "result": github_action.result_data,
        "error": github_action.error_message
    }

@router.get("/repository-tutorials/{repository_id}", response_model=List[schemas.TutorStatus])
async def get_repository_tutorials(
    repository_id: int,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get all tutorials generated for a specific repository.
    """
    logger.info(f"--- TUTOR_API get_repository_tutorials: ENTERING. repo_id={repository_id} ---")

    _current_user = await get_current_user(token=token, db=db)
    if not _current_user:
        logger.error("--- TUTOR_API get_repository_tutorials: Could not get current_user from token ---")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token or user not found")

    current_user_profile = await get_current_user_profile(current_user=_current_user, db=db)
    if not current_user_profile:
        logger.error(f"--- TUTOR_API get_repository_tutorials: Could not get user profile for user_id: {_current_user.id} ---")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User profile not found")

    logger.info(f"--- TUTOR_API get_repository_tutorials: Manually resolved profile for user: {current_user_profile.github_username} ---")

    repository = db.query(Repository).filter(Repository.id == repository_id).first()
    if not repository:
        logger.warning(f"--- TUTOR_API get_repository_tutorials: Repository ID {repository_id} not found ---")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Repository not found"
        )

    logger.info(f"--- TUTOR_API get_repository_tutorials: Fetching tutorial actions for repo_id={repository_id} ---")
    github_actions = db.query(GithubAction).filter(
        GithubAction.repository_id == repository_id,
        GithubAction.action_type == "generate_tutorial"
    ).all()

    logger.info(f"--- TUTOR_API get_repository_tutorials: Found {len(github_actions)} tutorial actions for repo_id={repository_id} ---")

    response_data = [
        {
            "github_action_id": action.id,
            "status": action.status,
            "result": action.result_data,
            "error": action.error_message
        } for action in github_actions
    ]
    logger.info(f"--- TUTOR_API get_repository_tutorials: EXITING. Returning {len(response_data)} items. ---")
    return response_data

async def generate_tutorial(
    github_action_id: int,
    repository_url: str,
    include_patterns: Optional[List[str]] = None,
    exclude_patterns: Optional[List[str]] = None,
    language: str = "english",
    max_abstractions: int = 10,
    use_cache: bool = True,
    github_token: Optional[str] = None,
    db: Session = None
):
    """
    Background task to generate a tutorial for a GitHub repository.
    """
    logger.info(f"Starting tutorial generation for {repository_url}")

    try:
        # Update the action status to 'in_progress'
        github_action = db.query(GithubAction).filter(GithubAction.id == github_action_id).first()
        github_action.status = "in_progress"
        db.commit()

        # Create a temporary output directory
        output_dir = tempfile.mkdtemp(prefix="tutor_output_")
        logger.info(f"Created temporary output directory: {output_dir}")

        # Convert include/exclude patterns to sets if provided
        include_set = set(include_patterns) if include_patterns else None
        exclude_set = set(exclude_patterns) if exclude_patterns else None

        # Initialize shared dictionary with inputs
        shared = {
            "repo_url": repository_url,
            "local_dir": None,
            "project_name": None,  # Will be derived from URL
            "github_token": github_token,
            "output_dir": output_dir,
            "include_patterns": include_set,
            "exclude_patterns": exclude_set,
            "max_file_size": 100000,  # 100KB
            "language": language,
            "use_cache": use_cache,
            "max_abstraction_num": max_abstractions,
            "files": [],
            "abstractions": [],
            "relationships": {},
            "chapter_order": [],
            "chapters": [],
            "final_output_dir": None
        }

        # Create and run the tutorial flow
        tutorial_flow = create_tutorial_flow()
        tutorial_flow.run(shared)

        # Update the action with the result
        if shared.get("final_output_dir"):
            # Get list of generated files
            generated_files = []
            for root, _, files in os.walk(shared["final_output_dir"]):
                for file in files:
                    file_path = os.path.join(root, file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    relative_path = os.path.relpath(file_path, shared["final_output_dir"])
                    generated_files.append({
                        "path": relative_path,
                        "content": content
                    })

            # Update action result
            github_action.result_data = {
                "output_dir": shared["final_output_dir"],
                "files": generated_files,
                "project_name": shared.get("project_name", "")
            }
            github_action.status = "completed"
        else:
            github_action.status = "failed"
            github_action.error_message = "Failed to generate tutorial - no output directory"

        db.commit()
        logger.info(f"Tutorial generation completed for {repository_url}")

    except Exception as e:
        logger.error(f"Error generating tutorial: {str(e)}")
        if db:
            github_action = db.query(GithubAction).filter(GithubAction.id == github_action_id).first()
            if github_action:
                github_action.status = "failed"
                github_action.error_message = str(e)
                db.commit()
        raise
