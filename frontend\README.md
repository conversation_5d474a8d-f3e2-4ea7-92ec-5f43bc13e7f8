# Frontend Application

This directory contains the Next.js frontend application for the GitHub Enterprise + Perplexity API integration. It provides the user interface for interacting with the system's features.

## Recent Development Summary

The following key frontend-specific improvements and fixes have been recently implemented:

- **Login Function TypeError Fix:**
  - Corrected a type definition error in `AuthContextType` located in `frontend/src/contexts/AuthContext.tsx`.
  - The `login` function, which accepts an optional second `userData` argument, was previously declared with only one argument in its type definition. This caused a "Expected 1 arguments, but got 2" error during GitHub authentication callbacks.
  - The type definition in `AuthContextType` has been updated to accurately reflect the function's signature: `login: (token: string, userData?: any) => void;`.

- **Centralized Token Storage Logic:**
  - Refactored token handling during the authentication callback process.
  - Previously, token storage logic (e.g., saving to `localStorage`) was present in `frontend/src/app/auth/callback/page.tsx`.
  - This logic has been removed from the callback page and centralized within the `login` method of `AuthContext.tsx`.
  - This change ensures that token management is handled consistently by the authentication context, reducing redundancy and improving maintainability.

These updates enhance the reliability and robustness of the frontend authentication flow.

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
