# GitHub Enterprise + Perplexity API Integration

This project integrates GitHub Enterprise with Perplexity API to provide AI-powered features for developers. It includes a frontend application for user interaction and a backend server to handle API requests, authentication, and data synchronization with GitHub.

## Project Structure

- `frontend/`: Contains the Next.js frontend application.
- `server/`: Contains the FastAPI backend server.

## Recent Development Summary

The following key activities and fixes have been recently implemented:

### Authentication and Authorization:
- **GitHub Logout (404 Fix):** Resolved an issue where the `/api/github/auth/logout/` endpoint was missing, causing a 404 error. The route was added to `server/app/auth/router.py`.
- **Frontend Login Function TypeError:** Corrected a type definition mismatch in `AuthContextType` (`frontend/src/contexts/AuthContext.tsx`) where the `login` function's optional `userData` parameter was not declared, leading to a "Expected 1 arguments, but got 2" error.
- **GitHub Authentication Flow Review & Refinement:**
    - Conducted a comprehensive review of both OAuth and Personal Access Token (PAT) authentication flows.
    - **Frontend:** Centralized token storage logic from `frontend/src/app/auth/callback/page.tsx` into `AuthContext.tsx` to eliminate redundancy.
    - **Backend:** Optimized token validation in `server/app/auth/github.py` by modifying `validate_token()` to return user data and `get_or_create_user()` to accept this pre-fetched data. This improvement was applied to both PAT and OAuth flows in `server/app/auth/router.py`, enhancing efficiency and robustness.

### Repository Fetching and Synchronization:
- **Repository Fetching (422 Unprocessable Entity Fix):**
    - Investigated and resolved a 422 error encountered when fetching repositories via the `/api/github/repositories/` endpoint.
    - Initial attempts involved correcting mock data generation in `server/app/api/github.py` to include `created_at` and `updated_at` fields.
    - Extensive logging revealed the root cause to be a Pydantic validation error (`loc: ["query", "kwargs"], msg: "Field required"`) triggered by `Depends(get_current_user_profile)` in the `get_repositories` function signature in `server/app/api/github.py`.
    - Refactored `get_repositories` to manually resolve `current_user_profile` within the function body, successfully resolving the 422 error.
- **Repository Query Logic:** Addressed an issue in `server/app/api/core.py` where the database query for repositories (using a `union`) could return empty results. Refactored `get_repositories` to collect all relevant repository IDs (user-owned, organization-based, explicit access) into a set and then query by these unique IDs.
- **GitHub Repository Synchronization:**
    - Implemented functionality to fetch and synchronize a user's repositories from GitHub into the local database upon their login or initial creation.
    - Added `get_user_repositories` to `GitHubClient` (in `server/app/auth/github.py`) to fetch repository data from the GitHub API.
    - Added `sync_user_repositories` to `GitHubTokenAuth` (in `server/app/auth/github.py`) to process these repositories, filter for user-owned ones, and then create or update them in the local database via `get_or_create_repository`. This method is now called from `get_or_create_user`.
    - Fixed a `NameError: name 'datetime' is not defined` in `server/app/auth/github.py` by adding the necessary import.

### General Maintenance:
- **Logging and Debugging Code Removal:** Cleaned up extensive diagnostic logging, print statements, and comments that were added during the troubleshooting phases across multiple files (`server/app/api/core.py`, `server/app/api/github.py`, `server/app/main.py`, etc.).
- **TypeError with Logger:** Resolved a `TypeError: Logger._log() got an unexpected keyword argument 'flush'` by removing `flush=True` from `logging` calls in `server/app/api/core.py` and `server/app/api/github.py`.

This series of updates has significantly improved the stability, correctness, and efficiency of the authentication and data synchronization processes within the application.

## Features

- AI-powered code search and understanding
- Automated PR reviews and suggestions
- Repository-specific context for AI queries
- Codebase learning assistance
- AI chat agent for repository-aware assistance
- Analytics and usage tracking
- **NEW: AI-powered Tutorial Generation** - Generate comprehensive, beginner-friendly tutorials for any GitHub repository

## Tech Stack

- **Backend**: FastAPI
- **Frontend**: Next.js
- **Database**: SQLite (can be configured for PostgreSQL)
- **Caching**: In-memory cache with options for Redis
- **Deployment**: Docker and Docker Compose
- **Authentication**: GitHub OAuth and Personal Access Token
- **AI**: Perplexity API and Google Gemini API (for tutorial generation)

## Getting Started

### Prerequisites

- Python 3.11+
- Node.js 18+
- Docker and Docker Compose (optional for containerized setup)
- GitHub Personal Access Token (with 'repo', 'read:user', and 'read:org' scopes)
- Perplexity API key
- Google Gemini API key (for tutorial generation)

### Setup

1. Clone the repository

```
git clone https://github.com/yourusername/github-perplexity-integration.git
cd github-perplexity-integration
```

2. Create and configure environment variables

```
cp .env.example .env
# Edit .env file with your settings
```

3. Start with Docker Compose (recommended)

```
docker-compose up -d
```

This will start:
- PostgreSQL database
- Redis server
- Django backend
- Celery worker
- React frontend

4. Or, manual setup:

**Backend:**
```
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
python manage.py migrate
python manage.py create_superuser
python manage.py runserver
```

**Frontend:**
```
cd frontend
npm install
npm start
```

5. Access the application

- Frontend: http://localhost:3000
- Backend API: http://localhost:8000/api/
- API Documentation: http://localhost:8000/api/docs/

## Usage

1. Log in with your GitHub Personal Access Token
   - Generate a token at https://github.com/settings/tokens with 'repo', 'read:user', and 'read:org' scopes
   - Enter the token on the login page
2. Your repositories will be automatically available based on your GitHub token permissions
3. Use AI-powered features:
   - Generate PR reviews
   - Search code with natural language
   - Ask questions about your codebase
   - Get AI assistance on your workflow
   - Generate comprehensive tutorials for your repositories

### Tutorial Generation

The tutorial generation feature uses the Tutor module to analyze GitHub repositories and create beginner-friendly tutorials:

1. Navigate to your repository details page
2. Click on "Generate Tutorial"
3. Configure options:
   - Language: Choose from multiple languages (English, Spanish, Chinese, etc.)
   - Include/Exclude Patterns: Specify which files to include or exclude (e.g., "*.py,*.js" or "tests/*,docs/*")
   - Max Abstractions: Control how many key concepts are identified (5-20)
4. Wait for the tutorial to be generated (this may take several minutes depending on repository size)
5. View and navigate through the tutorial chapters

## Environment Variables

See `.env.example` for all required variables.

Key variables:
- `PERPLEXITY_API_KEY`: Your Perplexity API key 
- `GEMINI_API_KEY`: Your Google Gemini API key (for tutorial generation)
- `DATABASE_URL`: Database connection URL
- `SECRET_KEY`: Secret key for security

## Development

### Backend Structure

- `app/models`: Data models and database schema
- `app/api`: API endpoints and business logic
- `app/auth`: Authentication and GitHub integration
- `app/dependencies`: Dependency injection and shared utilities
- `tutor`: Tutorial generation module powered by Google's Gemini API

### Frontend Structure

- `src/components`: Reusable UI components
- `src/app`: Next.js app router pages
- `src/lib`: API client services and utilities
- `src/contexts`: React contexts for state management
- `src/hooks`: Custom React hooks

## License

MIT

## Credits

- Built with Django and React
- Powered by Perplexity AI
- Integrates with GitHub Enterprise