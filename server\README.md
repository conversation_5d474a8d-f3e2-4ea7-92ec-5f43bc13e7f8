# FastAPI Backend for GitHub Enterprise + Perplexity API Integration

This is the FastAPI backend for the GitHub Enterprise + Perplexity API Integration project. It provides a modern, high-performance API that replaces the previous Django backend.

## Features

- **FastAPI Framework**: High-performance, easy-to-use framework with automatic OpenAPI documentation
- **SQLAlchemy ORM**: Modern database access with SQLAlchemy
- **JWT Authentication**: Secure authentication with JWT tokens
- **GitHub Integration**: Seamless integration with GitHub Enterprise
- **Perplexity API**: AI-powered code search and understanding
- **Analytics**: Usage tracking and metrics

## Getting Started

### Prerequisites

- Python 3.11+
- PostgreSQL
- Redis (optional for caching)

### Installation

1. Create a virtual environment:

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:

```bash
pip install -r requirements.txt
```

3. Configure environment variables:

Create a `.env` file in the server directory with the following variables:

```
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/github_perplexity
SECRET_KEY=your-secret-key
DEBUG=True
CORS_ALLOWED_ORIGINS=http://localhost:3000
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
PERPLEXITY_API_KEY=your-perplexity-api-key
```

4. Run the server:

```bash
python run.py
```

The server will be available at http://localhost:8000.

### API Documentation

FastAPI automatically generates OpenAPI documentation for the API. You can access it at:

- Swagger UI: http://localhost:8000/api/docs
- ReDoc: http://localhost:8000/api/redoc

## Project Structure

- `app/`: Main application package
  - `main.py`: FastAPI application entry point
  - `config.py`: Configuration settings
  - `database.py`: Database connection and session management
  - `dependencies.py`: Dependency injection
  - `auth/`: Authentication
    - `jwt.py`: JWT token handling
    - `github.py`: GitHub authentication
    - `router.py`: Auth routes
  - `api/`: API routes
    - `core.py`: Core routes (users, profiles, etc.)
    - `github.py`: GitHub integration routes
    - `perplexity.py`: Perplexity API routes
    - `analytics.py`: Analytics routes
  - `models/`: SQLAlchemy models
    - `core.py`: Core models
    - `github.py`: GitHub models
    - `perplexity.py`: Perplexity models
    - `analytics.py`: Analytics models

## Development

### Running Tests

```bash
pytest
```

### Database Migrations

This project uses Alembic for database migrations. To create a new migration:

```bash
alembic revision --autogenerate -m "Description of changes"
```

To apply migrations:

```bash
alembic upgrade head
```

## License

MIT

# Backend Server

This directory contains the FastAPI backend server for the GitHub Enterprise + Perplexity API integration. It handles API requests, user authentication, data synchronization with GitHub, and business logic.

## Recent Development Summary

The following key backend-specific improvements and fixes have been recently implemented:

### Authentication and Authorization:
- **GitHub Logout Route (404 Fix):** Added the previously missing `/api/github/auth/logout/` endpoint to `server/app/auth/router.py`, resolving a 404 error.
- **Optimized Token Validation:** Enhanced the efficiency and robustness of both PAT and OAuth token validation in `server/app/auth/github.py`. The `validate_token()` method now returns user data, and `get_or_create_user()` accepts this pre-fetched data, reducing redundant API calls.

### Repository Management:
- **Repository Fetching (422 Unprocessable Entity Fix):** 
    - Resolved a 422 error for the `/api/github/repositories/` endpoint.
    - The issue was traced to a Pydantic validation error (`loc: ["query", "kwargs"], msg: "Field required"`) caused by the `Depends(get_current_user_profile)` dependency in the `get_repositories` function signature (`server/app/api/github.py`).
    - Refactored `get_repositories` to manually resolve `current_user_profile` within the function body, eliminating the Pydantic error.
- **Corrected Repository Query Logic:** Addressed an issue in `server/app/api/core.py` where the database query for repositories could return empty results. The logic in `get_repositories` was refactored to collect all unique repository IDs (from user ownership, organization membership, and explicit access) into a set before querying the database.
- **GitHub Repository Synchronization:**
    - Implemented a feature to synchronize a user's repositories from GitHub into the local database during the `get_or_create_user` process (typically upon login or first-time user creation).
    - This involved adding `get_user_repositories` to `GitHubClient` for fetching data from the GitHub API and `sync_user_repositories` to `GitHubTokenAuth` (both in `server/app/auth/github.py`) to process and save these repositories.
    - Fixed a `NameError: name 'datetime' is not defined` in `server/app/auth/github.py` by adding the `from datetime import datetime` import.

### General Maintenance and Error Handling:
- **Diagnostic Code Removal:** Removed extensive diagnostic logging (print statements, detailed log messages) and comments that were added during previous debugging phases across various files, including `server/app/api/core.py`, `server/app/api/github.py`, and `server/app/main.py`.
- **Logger TypeError Fix:** Resolved a `TypeError: Logger._log() got an unexpected keyword argument 'flush'` by removing the `flush=True` argument from calls to the `logging` module in `server/app/api/core.py` and `server/app/api/github.py`.

These updates have significantly improved the stability, correctness, and efficiency of the backend services.

## API Endpoints
