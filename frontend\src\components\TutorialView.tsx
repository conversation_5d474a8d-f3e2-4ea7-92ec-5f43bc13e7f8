'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import mermaid from 'mermaid';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  ExternalLink, 
  BookOpen, 
  FileText, 
  Loader2,
  ChevronRight
} from 'lucide-react';

interface TutorialChapter {
  title: string;
  filename: string;
}

interface TutorialData {
  title: string;
  description: string;
  sourceRepository: string;
  flowchart: string;
  chapters: TutorialChapter[];
}

interface TutorialViewProps {
  tutorialData: string; // Raw markdown string from the backend
  onBackClick?: () => void;
}

const TutorialView: React.FC<TutorialViewProps> = ({ tutorialData, onBackClick }) => {
  const [parsedData, setParsedData] = useState<TutorialData | null>(null);
  const [activeChapter, setActiveChapter] = useState<string | null>(null);
  const flowchartRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  // Initialize mermaid
  useEffect(() => {
    mermaid.initialize({
      startOnLoad: true,
      theme: document.documentElement.classList.contains('dark') ? 'dark' : 'default',
      securityLevel: 'loose'
    });
  }, []);

  useEffect(() => {
    if (tutorialData) {
      const parsed = parseTutorialData(tutorialData);
      setParsedData(parsed);
      
      // Set first chapter as active by default
      if (parsed.chapters.length > 0) {
        setActiveChapter(parsed.chapters[0].filename);
      }
    }
  }, [tutorialData]);

  // Render mermaid diagram when flowchart data changes or component mounts
  useEffect(() => {
    if (parsedData?.flowchart && flowchartRef.current && !activeChapter) {
      try {
        mermaid.render('flowchart-diagram', parsedData.flowchart).then(({ svg }) => {
          if (flowchartRef.current) {
            flowchartRef.current.innerHTML = svg;
          }
        });
      } catch (error) {
        console.error('Failed to render mermaid diagram:', error);
        if (flowchartRef.current) {
          flowchartRef.current.innerHTML = '<p class="text-destructive">Failed to render diagram</p>';
        }
      }
    }
  }, [parsedData, activeChapter]);

  const parseTutorialData = (markdownContent: string): TutorialData => {
    // Initialize tutorial data structure
    const data: TutorialData = {
      title: '',
      description: '',
      sourceRepository: '',
      flowchart: '',
      chapters: []
    };

    // Extract title - should be the first heading
    const titleMatch = markdownContent.match(/^# (.*)/m);
    if (titleMatch) {
      data.title = titleMatch[1].replace('Tutorial: ', '');
    }

    // Extract description - text between the title and the Source Repository link
    const descriptionMatch = markdownContent.match(/^# .*\n\n([\s\S]*?)(?=\n\nSource Repository)/m);
    if (descriptionMatch) {
      data.description = descriptionMatch[1].trim();
    }

    // Extract source repository link
    const repoMatch = markdownContent.match(/Source Repository: \[(.*?)\]\((.*?)\)/);
    if (repoMatch) {
      data.sourceRepository = repoMatch[2];
    }

    // Extract flowchart - content between flowchart TD and Chapters heading
    const flowchartMatch = markdownContent.match(/```(?:mermaid)?\s*\n(flowchart TD[\s\S]*?)```/);
    if (flowchartMatch) {
      data.flowchart = flowchartMatch[1].trim();
    }

    // Extract chapters
    const chaptersSection = markdownContent.match(/## Chapters\s*\n\n([\s\S]*?)(?=\n\n---)/);
    if (chaptersSection) {
      const chaptersList = chaptersSection[1];
      const chapterMatches = chaptersList.matchAll(/\d+\. \[(.*?)\]\((.*?)\)/g);
      
      for (const match of chapterMatches) {
        data.chapters.push({
          title: match[1].trim(),
          filename: match[2]
        });
      }
    }

    return data;
  };

  const handleChapterClick = (filename: string) => {
    setActiveChapter(filename);
    // In a real implementation, you would fetch the chapter content here
    // For now, we're just changing the active chapter
  };

  if (!parsedData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
          <p className="text-muted-foreground">Loading tutorial...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/50">
      <div className="container mx-auto px-4 py-8">
        {/* Tutorial Header */}
        <Card className="mb-8">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5 text-primary" />
                  <Badge variant="secondary">Tutorial</Badge>
                </div>
                <CardTitle className="text-3xl font-bold">{parsedData.title}</CardTitle>
                <CardDescription className="text-lg">
                  {parsedData.description}
                </CardDescription>
              </div>
              <div className="flex gap-2">
                {onBackClick && (
                  <Button variant="outline" onClick={onBackClick}>
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back
                  </Button>
                )}
                {parsedData.sourceRepository && (
                  <Button asChild>
                    <a
                      href={parsedData.sourceRepository}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <ExternalLink className="mr-2 h-4 w-4" />
                      View Source
                    </a>
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Tutorial Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar with Chapters */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Chapters ({parsedData.chapters.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {parsedData.chapters.map((chapter, index) => (
                  <Button
                    key={chapter.filename}
                    variant={activeChapter === chapter.filename ? "default" : "ghost"}
                    className="w-full justify-start text-left h-auto p-3"
                    onClick={() => handleChapterClick(chapter.filename)}
                  >
                    <div className="flex items-start gap-3 w-full">
                      <Badge variant="outline" className="min-w-[24px] h-6 flex items-center justify-center text-xs">
                        {index + 1}
                      </Badge>
                      <div className="flex-1 text-sm">
                        {chapter.title}
                      </div>
                      <ChevronRight className="h-4 w-4 opacity-50" />
                    </div>
                  </Button>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3">
            <Card>
              <CardContent className="p-6">
                {/* Flowchart */}
                {parsedData.flowchart && !activeChapter && (
                  <div className="space-y-6">
                    <div>
                      <h2 className="text-2xl font-semibold mb-2">Project Structure</h2>
                      <p className="text-muted-foreground">
                        Visual overview of the project structure and components.
                      </p>
                    </div>
                    <Separator />
                    <div className="bg-muted/50 p-6 rounded-lg overflow-auto">
                      <div ref={flowchartRef} className="mermaid-diagram min-h-[300px] flex items-center justify-center"></div>
                    </div>
                  </div>
                )}

                {/* Chapter Content */}
                {activeChapter ? (
                  <div className="space-y-6">
                    <div>
                      <h2 className="text-2xl font-semibold mb-2">
                        {parsedData.chapters.find(ch => ch.filename === activeChapter)?.title}
                      </h2>
                      <Badge variant="outline">Chapter Content</Badge>
                    </div>
                    <Separator />
                    <div className="prose prose-neutral dark:prose-invert max-w-none">
                      <p className="text-muted-foreground bg-muted/50 p-4 rounded-lg border-l-4 border-primary">
                        <strong>Note:</strong> In a complete implementation, the content of "{activeChapter}" would be loaded here.
                        Currently, this is a placeholder as the chapter content would need to be fetched from the backend.
                      </p>
                      <h3>What you'll learn in this chapter:</h3>
                      <ul>
                        <li>Key concepts and implementation details</li>
                        <li>Code examples and best practices</li>
                        <li>Step-by-step instructions</li>
                        <li>Common pitfalls and how to avoid them</li>
                      </ul>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    <div>
                      <h2 className="text-2xl font-semibold mb-2">Tutorial Overview</h2>
                      <p className="text-muted-foreground">
                        Select a chapter from the sidebar to view its content.
                      </p>
                    </div>
                    <Separator />
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <h3 className="font-semibold">What you'll learn:</h3>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          <li>• Comprehensive understanding of {parsedData.title}</li>
                          <li>• Practical implementation examples</li>
                          <li>• Best practices and patterns</li>
                          <li>• Real-world applications</li>
                        </ul>
                      </div>
                      <div className="space-y-2">
                        <h3 className="font-semibold">Tutorial Stats:</h3>
                        <div className="space-y-1 text-sm text-muted-foreground">
                          <div className="flex justify-between">
                            <span>Chapters:</span>
                            <Badge variant="secondary">{parsedData.chapters.length}</Badge>
                          </div>
                          <div className="flex justify-between">
                            <span>Level:</span>
                            <Badge variant="outline">Intermediate</Badge>
                          </div>
                          <div className="flex justify-between">
                            <span>Estimated Time:</span>
                            <span>{Math.ceil(parsedData.chapters.length * 0.5)}h</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TutorialView; 