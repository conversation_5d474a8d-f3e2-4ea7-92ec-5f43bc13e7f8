from sqlalchemy import <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, DateTime, Text, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..database import Base

class APIKeyConfig(Base):
    """API key configuration model."""
    __tablename__ = "api_key_configs"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    api_key = Column(String)
    is_active = Column(Boolean, default=True)
    user_id = Column(Integer, ForeignKey("user_profiles.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("UserProfile", backref="api_keys")

class PerplexityQuery(Base):
    """Perplexity query model."""
    __tablename__ = "perplexity_queries"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, <PERSON>Key("user_profiles.id"), nullable=True)
    repository_id = Column(Integer, ForeignKey("repositories.id"), nullable=True)
    model = Column(String, default="sonar-pro")
    prompt = Column(Text)
    context = Column(JSON, nullable=True)
    response = Column(Text)
    tokens_used = Column(Integer, default=0)
    duration_ms = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("UserProfile", backref="perplexity_queries")
    repository = relationship("Repository", backref="perplexity_queries")
    logs = relationship("QueryLog", back_populates="query", cascade="all, delete-orphan")

class PerplexityRate(Base):
    """Perplexity rate model."""
    __tablename__ = "perplexity_rates"
    
    id = Column(Integer, primary_key=True, index=True)
    model = Column(String, unique=True)
    tokens_per_second = Column(Integer)
    cost_per_1k_tokens = Column(Integer)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class PerplexityCache(Base):
    """Perplexity cache model."""
    __tablename__ = "perplexity_caches"
    
    id = Column(Integer, primary_key=True, index=True)
    query_hash = Column(String, unique=True, index=True)
    model = Column(String)
    request = Column(JSON)
    response = Column(JSON)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    accessed_at = Column(DateTime(timezone=True), onupdate=func.now())
