-- Supabase Database Schema for Perplexity Clone with <PERSON> Authentication
-- This file contains all the SQL queries to set up the complete database schema

-- ============================================================================
-- CORE TABLES
-- ============================================================================

-- 1. User Profiles Table (linked to Clerk users)
CREATE TABLE user_profiles (
    id BIGSERIAL PRIMARY KEY,
    clerk_user_id TEXT UNIQUE NOT NULL, -- Clerk user ID
    github_id TEXT UNIQUE,
    github_access_token TEXT,
    github_username TEXT,
    github_avatar_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_user_profiles_clerk_user_id ON user_profiles(clerk_user_id);
CREATE INDEX idx_user_profiles_github_id ON user_profiles(github_id);
CREATE INDEX idx_user_profiles_github_username ON user_profiles(github_username);

-- 2. Organizations Table
CREATE TABLE organizations (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    github_id TEXT UNIQUE,
    github_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_organizations_github_id ON organizations(github_id);
CREATE INDEX idx_organizations_name ON organizations(name);

-- 3. User Organization Junction Table
CREATE TABLE user_organization (
    user_profile_id BIGINT REFERENCES user_profiles(id) ON DELETE CASCADE,
    organization_id BIGINT REFERENCES organizations(id) ON DELETE CASCADE,
    PRIMARY KEY (user_profile_id, organization_id)
);

-- 4. Repositories Table
CREATE TABLE repositories (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    full_name TEXT UNIQUE NOT NULL,
    github_id TEXT UNIQUE NOT NULL,
    github_url TEXT,
    description TEXT,
    organization_id BIGINT REFERENCES organizations(id),
    owner_id BIGINT REFERENCES user_profiles(id),
    is_private BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_repositories_full_name ON repositories(full_name);
CREATE INDEX idx_repositories_github_id ON repositories(github_id);
CREATE INDEX idx_repositories_name ON repositories(name);

-- 5. User Repository Access Table
CREATE TABLE user_repository_accesses (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES user_profiles(id) ON DELETE CASCADE,
    repository_id BIGINT REFERENCES repositories(id) ON DELETE CASCADE,
    access_level TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, repository_id)
);

-- ============================================================================
-- GITHUB INTEGRATION TABLES
-- ============================================================================

-- 6. Pull Requests Table
CREATE TABLE pull_requests (
    id BIGSERIAL PRIMARY KEY,
    repository_id BIGINT REFERENCES repositories(id) ON DELETE CASCADE,
    github_id BIGINT UNIQUE NOT NULL,
    number INTEGER NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    state TEXT NOT NULL,
    created_by_id BIGINT REFERENCES user_profiles(id),
    base_branch TEXT NOT NULL,
    head_branch TEXT NOT NULL,
    is_draft BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    closed_at TIMESTAMPTZ,
    merged_at TIMESTAMPTZ
);

CREATE INDEX idx_pull_requests_github_id ON pull_requests(github_id);
CREATE INDEX idx_pull_requests_repository_id ON pull_requests(repository_id);
CREATE INDEX idx_pull_requests_number ON pull_requests(number);

-- 7. PR Comments Table
CREATE TABLE pr_comments (
    id BIGSERIAL PRIMARY KEY,
    pull_request_id BIGINT REFERENCES pull_requests(id) ON DELETE CASCADE,
    github_id BIGINT UNIQUE,
    user_id BIGINT REFERENCES user_profiles(id),
    body TEXT NOT NULL,
    path TEXT,
    position INTEGER,
    is_ai_generated BOOLEAN DEFAULT false,
    ai_model TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_pr_comments_pull_request_id ON pr_comments(pull_request_id);
CREATE INDEX idx_pr_comments_github_id ON pr_comments(github_id);

-- 8. PR Reviews Table
CREATE TABLE pr_reviews (
    id BIGSERIAL PRIMARY KEY,
    pull_request_id BIGINT REFERENCES pull_requests(id) ON DELETE CASCADE,
    github_id BIGINT UNIQUE,
    user_id BIGINT REFERENCES user_profiles(id),
    status TEXT NOT NULL,
    body TEXT,
    is_ai_generated BOOLEAN DEFAULT false,
    ai_status TEXT,
    ai_model TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    submitted_at TIMESTAMPTZ
);

CREATE INDEX idx_pr_reviews_pull_request_id ON pr_reviews(pull_request_id);
CREATE INDEX idx_pr_reviews_github_id ON pr_reviews(github_id);

-- 9. PR Diffs Table
CREATE TABLE pr_diffs (
    id BIGSERIAL PRIMARY KEY,
    pull_request_id BIGINT UNIQUE REFERENCES pull_requests(id) ON DELETE CASCADE,
    diff_content TEXT NOT NULL,
    num_files INTEGER NOT NULL,
    additions INTEGER NOT NULL,
    deletions INTEGER NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 10. GitHub Files Table
CREATE TABLE github_files (
    id BIGSERIAL PRIMARY KEY,
    repository_id BIGINT REFERENCES repositories(id) ON DELETE CASCADE,
    path TEXT NOT NULL,
    content TEXT,
    sha TEXT NOT NULL,
    size INTEGER NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_github_files_repository_id ON github_files(repository_id);
CREATE INDEX idx_github_files_path ON github_files(path);

-- 11. GitHub Actions Table
CREATE TABLE github_actions (
    id BIGSERIAL PRIMARY KEY,
    pull_request_id BIGINT REFERENCES pull_requests(id) ON DELETE CASCADE,
    github_id BIGINT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    status TEXT NOT NULL,
    conclusion TEXT,
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_github_actions_pull_request_id ON github_actions(pull_request_id);
CREATE INDEX idx_github_actions_github_id ON github_actions(github_id);

-- 12. GitHub Webhooks Table
CREATE TABLE github_webhooks (
    id BIGSERIAL PRIMARY KEY,
    repository_id BIGINT REFERENCES repositories(id) ON DELETE CASCADE,
    github_id BIGINT UNIQUE NOT NULL,
    event_type TEXT NOT NULL,
    payload JSONB NOT NULL,
    processed BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_github_webhooks_repository_id ON github_webhooks(repository_id);
CREATE INDEX idx_github_webhooks_event_type ON github_webhooks(event_type);
CREATE INDEX idx_github_webhooks_processed ON github_webhooks(processed);

-- ============================================================================
-- PERPLEXITY INTEGRATION TABLES
-- ============================================================================

-- 13. API Key Configs Table
CREATE TABLE api_key_configs (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    api_key TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    user_id BIGINT REFERENCES user_profiles(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_api_key_configs_user_id ON api_key_configs(user_id);

-- 14. Perplexity Queries Table
CREATE TABLE perplexity_queries (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES user_profiles(id),
    repository_id BIGINT REFERENCES repositories(id),
    model TEXT DEFAULT 'sonar-pro',
    prompt TEXT NOT NULL,
    context JSONB,
    response TEXT NOT NULL,
    tokens_used INTEGER DEFAULT 0,
    duration_ms INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_perplexity_queries_user_id ON perplexity_queries(user_id);
CREATE INDEX idx_perplexity_queries_repository_id ON perplexity_queries(repository_id);
CREATE INDEX idx_perplexity_queries_created_at ON perplexity_queries(created_at);

-- 15. Perplexity Rates Table
CREATE TABLE perplexity_rates (
    id BIGSERIAL PRIMARY KEY,
    model TEXT UNIQUE NOT NULL,
    tokens_per_second INTEGER NOT NULL,
    cost_per_1k_tokens INTEGER NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 16. Perplexity Cache Table
CREATE TABLE perplexity_caches (
    id BIGSERIAL PRIMARY KEY,
    query_hash TEXT UNIQUE NOT NULL,
    model TEXT NOT NULL,
    request JSONB NOT NULL,
    response JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    accessed_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_perplexity_caches_query_hash ON perplexity_caches(query_hash);
CREATE INDEX idx_perplexity_caches_created_at ON perplexity_caches(created_at);

-- ============================================================================
-- ANALYTICS TABLES
-- ============================================================================

-- 17. Query Logs Table
CREATE TABLE query_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES user_profiles(id),
    query_id BIGINT REFERENCES perplexity_queries(id),
    pull_request_id BIGINT REFERENCES pull_requests(id),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_query_logs_user_id ON query_logs(user_id);
CREATE INDEX idx_query_logs_query_id ON query_logs(query_id);
CREATE INDEX idx_query_logs_created_at ON query_logs(created_at);

-- 18. User Activities Table
CREATE TABLE user_activities (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES user_profiles(id),
    action TEXT NOT NULL,
    details JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_user_activities_user_id ON user_activities(user_id);
CREATE INDEX idx_user_activities_action ON user_activities(action);
CREATE INDEX idx_user_activities_created_at ON user_activities(created_at);

-- 19. Usage Metrics Table
CREATE TABLE usage_metrics (
    id BIGSERIAL PRIMARY KEY,
    metric TEXT NOT NULL,
    value INTEGER DEFAULT 0,
    date DATE NOT NULL,
    UNIQUE(metric, date)
);

CREATE INDEX idx_usage_metrics_metric ON usage_metrics(metric);
CREATE INDEX idx_usage_metrics_date ON usage_metrics(date);

-- ============================================================================
-- REPOSITORY ANALYSIS TABLES
-- ============================================================================

-- 20. Repository Analyses Table
CREATE TABLE repository_analyses (
    id BIGSERIAL PRIMARY KEY,
    repository_id BIGINT REFERENCES repositories(id) ON DELETE CASCADE,
    analysis_type TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    dependencies_count INTEGER DEFAULT 0,
    security_vulnerabilities_count INTEGER DEFAULT 0,
    technology_score REAL DEFAULT 0.0,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    error_message TEXT,
    analysis_data JSONB
);

CREATE INDEX idx_repository_analyses_repository_id ON repository_analyses(repository_id);
CREATE INDEX idx_repository_analyses_status ON repository_analyses(status);
CREATE INDEX idx_repository_analyses_analysis_type ON repository_analyses(analysis_type);

-- 21. Dependency Analyses Table
CREATE TABLE dependency_analyses (
    id BIGSERIAL PRIMARY KEY,
    analysis_id BIGINT REFERENCES repository_analyses(id) ON DELETE CASCADE,
    package_name TEXT NOT NULL,
    current_version TEXT,
    latest_version TEXT,
    package_manager TEXT NOT NULL,
    is_outdated BOOLEAN DEFAULT false,
    versions_behind INTEGER DEFAULT 0,
    has_vulnerabilities BOOLEAN DEFAULT false,
    vulnerability_count INTEGER DEFAULT 0,
    sonar_analysis JSONB,
    alternative_packages JSONB,
    recent_discussions JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(analysis_id, package_name)
);

CREATE INDEX idx_dependency_analyses_analysis_id ON dependency_analyses(analysis_id);
CREATE INDEX idx_dependency_analyses_package_name ON dependency_analyses(package_name);

-- 22. Technology Trends Table
CREATE TABLE technology_trends (
    id BIGSERIAL PRIMARY KEY,
    analysis_id BIGINT REFERENCES repository_analyses(id) ON DELETE CASCADE,
    technology_name TEXT NOT NULL,
    technology_type TEXT NOT NULL,
    usage_percentage REAL DEFAULT 0.0,
    trend_direction TEXT NOT NULL,
    trend_score REAL DEFAULT 0.0,
    popularity_rank INTEGER,
    recent_articles JSONB,
    comparisons JSONB,
    migration_guides JSONB,
    community_sentiment JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(analysis_id, technology_name)
);

CREATE INDEX idx_technology_trends_analysis_id ON technology_trends(analysis_id);
CREATE INDEX idx_technology_trends_technology_name ON technology_trends(technology_name);

-- 23. Security Findings Table
CREATE TABLE security_findings (
    id BIGSERIAL PRIMARY KEY,
    analysis_id BIGINT REFERENCES repository_analyses(id) ON DELETE CASCADE,
    cve_id TEXT,
    severity TEXT NOT NULL,
    package_name TEXT NOT NULL,
    affected_versions TEXT,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    solution TEXT,
    related_discussions JSONB,
    fix_examples JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_security_findings_analysis_id ON security_findings(analysis_id);
CREATE INDEX idx_security_findings_cve_id ON security_findings(cve_id);
CREATE INDEX idx_security_findings_severity ON security_findings(severity);

-- 24. Issue Augmentations Table
CREATE TABLE issue_augmentations (
    id BIGSERIAL PRIMARY KEY,
    repository_id BIGINT REFERENCES repositories(id) ON DELETE CASCADE,
    issue_number INTEGER NOT NULL,
    github_issue_id BIGINT,
    issue_title TEXT NOT NULL,
    issue_body TEXT,
    issue_labels JSONB,
    analysis_status TEXT DEFAULT 'pending',
    similar_issues JSONB,
    potential_solutions JSONB,
    relevant_documentation JSONB,
    stack_overflow_links JSONB,
    suggested_comment TEXT,
    comment_posted BOOLEAN DEFAULT false,
    github_comment_id BIGINT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(repository_id, issue_number)
);

CREATE INDEX idx_issue_augmentations_repository_id ON issue_augmentations(repository_id);
CREATE INDEX idx_issue_augmentations_github_issue_id ON issue_augmentations(github_issue_id);

-- 25. Settings Table
CREATE TABLE settings (
    id BIGSERIAL PRIMARY KEY,
    key TEXT NOT NULL,
    value TEXT NOT NULL,
    scope TEXT NOT NULL,
    user_id BIGINT REFERENCES user_profiles(id),
    repository_id BIGINT REFERENCES repositories(id),
    organization_id BIGINT REFERENCES organizations(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(key, scope, user_id, repository_id, organization_id)
);

CREATE INDEX idx_settings_key ON settings(key);
CREATE INDEX idx_settings_scope ON settings(scope);

-- ============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- ============================================================================

-- Enable RLS on user-specific tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_key_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE perplexity_queries ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;

-- Example policies for user_profiles (adapt based on your Clerk integration)
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT USING (clerk_user_id = auth.jwt() ->> 'sub');

CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE USING (clerk_user_id = auth.jwt() ->> 'sub');

CREATE POLICY "Users can insert own profile" ON user_profiles
    FOR INSERT WITH CHECK (clerk_user_id = auth.jwt() ->> 'sub');

-- Example policies for api_key_configs
CREATE POLICY "Users can manage own API keys" ON api_key_configs
    FOR ALL USING (
        user_id IN (
            SELECT id FROM user_profiles 
            WHERE clerk_user_id = auth.jwt() ->> 'sub'
        )
    );

-- Example policies for perplexity_queries
CREATE POLICY "Users can view own queries" ON perplexity_queries
    FOR SELECT USING (
        user_id IN (
            SELECT id FROM user_profiles 
            WHERE clerk_user_id = auth.jwt() ->> 'sub'
        )
    );

CREATE POLICY "Users can insert own queries" ON perplexity_queries
    FOR INSERT WITH CHECK (
        user_id IN (
            SELECT id FROM user_profiles 
            WHERE clerk_user_id = auth.jwt() ->> 'sub'
        )
    );

-- Example policies for user_activities
CREATE POLICY "Users can view own activities" ON user_activities
    FOR SELECT USING (
        user_id IN (
            SELECT id FROM user_profiles 
            WHERE clerk_user_id = auth.jwt() ->> 'sub'
        )
    );

CREATE POLICY "Users can insert own activities" ON user_activities
    FOR INSERT WITH CHECK (
        user_id IN (
            SELECT id FROM user_profiles 
            WHERE clerk_user_id = auth.jwt() ->> 'sub'
        )
    );

-- Example policies for settings
CREATE POLICY "Users can manage own settings" ON settings
    FOR ALL USING (
        user_id IN (
            SELECT id FROM user_profiles 
            WHERE clerk_user_id = auth.jwt() ->> 'sub'
        )
    );

-- ============================================================================
-- FUNCTIONS AND TRIGGERS
-- ============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_repositories_updated_at BEFORE UPDATE ON repositories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_repository_accesses_updated_at BEFORE UPDATE ON user_repository_accesses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_pull_requests_updated_at BEFORE UPDATE ON pull_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_pr_comments_updated_at BEFORE UPDATE ON pr_comments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_pr_diffs_updated_at BEFORE UPDATE ON pr_diffs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_github_files_updated_at BEFORE UPDATE ON github_files FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_github_actions_updated_at BEFORE UPDATE ON github_actions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_api_key_configs_updated_at BEFORE UPDATE ON api_key_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_dependency_analyses_updated_at BEFORE UPDATE ON dependency_analyses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_technology_trends_updated_at BEFORE UPDATE ON technology_trends FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_issue_augmentations_updated_at BEFORE UPDATE ON issue_augmentations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- INITIAL DATA SETUP (OPTIONAL)
-- ============================================================================

-- Insert default Perplexity rate data
INSERT INTO perplexity_rates (model, tokens_per_second, cost_per_1k_tokens) VALUES
('sonar-pro', 100, 10),
('sonar-medium', 150, 5),
('sonar-small', 200, 2)
ON CONFLICT (model) DO NOTHING;

-- Insert default usage metrics
INSERT INTO usage_metrics (metric, value, date) VALUES
('total_queries', 0, CURRENT_DATE),
('total_users', 0, CURRENT_DATE),
('total_repositories', 0, CURRENT_DATE)
ON CONFLICT (metric, date) DO NOTHING;