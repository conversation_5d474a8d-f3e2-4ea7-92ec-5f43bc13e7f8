'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import NavigationBar from '@/components/NavigationBar';

interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: Date;
}

export default function Chat() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [messages, setMessages] = useState<Message[]>([
    {
      role: 'system',
      content: 'I am an AI assistant that can help you with coding questions, architecture decisions, and best practices.'
    },
    {
      role: 'assistant',
      content: 'Hello! I\'m your AI coding assistant. How can I help you today?',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  useEffect(() => {
    // Redirect to sign-in if not authenticated
    if (isLoaded && !user) {
      router.push('/sign-in');
    }
  }, [user, isLoaded, router]);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();

    if (!inputMessage.trim()) return;

    // Add user message
    const userMessage: Message = {
      role: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simulate AI response after a delay
    setTimeout(() => {
      let responseContent = '';

      if (inputMessage.toLowerCase().includes('react') && inputMessage.toLowerCase().includes('state')) {
        responseContent = "When structuring React components with proper state management, consider these best practices:\n\n1. **Keep state as local as possible** - Only lift state up when necessary\n\n2. **Use appropriate state management tools**:\n   - Local component state with useState for simple UI state\n   - Context API for state that needs to be accessed by many components\n   - Redux/Zustand for complex global state\n\n3. **Separate UI state from business logic** - UI state (like isModalOpen) should be kept separate from data state\n\n4. **Consider using reducers** - useReducer can help manage complex state logic\n\n5. **Immutable state updates** - Always create new state objects rather than mutating existing ones\n\nHere's a simple example of a well-structured component:\n\n```jsx\nfunction UserProfile() {\n  // Local UI state\n  const [isEditing, setIsEditing] = useState(false);\n  \n  // Data state\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  \n  useEffect(() => {\n    // Fetch user data\n    async function fetchUser() {\n      try {\n        setLoading(true);\n        const data = await api.getUser();\n        setUser(data);\n      } catch (err) {\n        setError(err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n    \n    fetchUser();\n  }, []);\n  \n  // Event handlers\n  const handleEdit = () => setIsEditing(true);\n  const handleSave = async (userData) => {\n    // Save logic\n    setIsEditing(false);\n  };\n  \n  // Render logic based on state\n  if (loading) return <Spinner />;\n  if (error) return <ErrorMessage message={error} />;\n  \n  return (\n    <div>\n      {isEditing ? (\n        <UserForm user={user} onSave={handleSave} />\n      ) : (\n        <UserDisplay user={user} onEdit={handleEdit} />\n      )}\n    </div>\n  );\n}\n```\n\nWould you like me to explain any specific aspect of state management in more detail?";
      } else {
        responseContent = "I'd be happy to help with that. Could you provide more details about what you're trying to accomplish?";
      }

      const assistantMessage: Message = {
        role: 'assistant',
        content: responseContent,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);
      setIsTyping(false);
    }, 1500);
  };

  if (!isLoaded) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to sign-in
  }

  return (
    <>
      <NavigationBar />
      <div className="pt-16 pb-6"> {/* Padding for the fixed navbar */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              AI Chat Assistant
            </h1>

            <p className="text-gray-600 dark:text-gray-300 mb-8">
              Chat with an AI assistant that understands your codebase. Get help with coding problems,
              architecture decisions, best practices, and more.
            </p>

            <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
              {/* Chat messages */}
              <div className="h-[500px] overflow-y-auto p-4 space-y-4">
                {messages.map((message, index) => (
                  <div
                    key={index}
                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg px-4 py-2 ${
                        message.role === 'user'
                          ? 'bg-blue-500 text-white'
                          : message.role === 'system'
                            ? 'bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200'
                            : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                      }`}
                    >
                      <div className="whitespace-pre-line prose dark:prose-invert prose-sm max-w-none">
                        {message.content}
                      </div>
                      {message.timestamp && (
                        <div className={`text-xs mt-1 ${message.role === 'user' ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'}`}>
                          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {isTyping && (
                  <div className="flex justify-start">
                    <div className="bg-gray-100 dark:bg-gray-700 rounded-lg px-4 py-2 text-gray-800 dark:text-gray-200">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 rounded-full bg-gray-500 animate-bounce"></div>
                        <div className="w-2 h-2 rounded-full bg-gray-500 animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        <div className="w-2 h-2 rounded-full bg-gray-500 animate-bounce" style={{ animationDelay: '0.4s' }}></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Chat input */}
              <div className="border-t border-gray-200 dark:border-gray-700 p-4">
                <form onSubmit={handleSendMessage} className="flex space-x-2">
                  <input
                    type="text"
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    placeholder="Type your message..."
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    disabled={isTyping}
                  />
                  <button
                    type="submit"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    disabled={!inputMessage.trim() || isTyping}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                    </svg>
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
