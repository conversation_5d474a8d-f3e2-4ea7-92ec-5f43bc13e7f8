import requests
import base64
import os
import tempfile
import time
import fnmatch
import json
import re
from typing import Union, Set, List, Dict, Tuple, Any, Optional
from urllib.parse import urlparse
from .call_llm import call_llm


def parse_github_url(repo_url: str) -> Tuple[str, str, Optional[str], Optional[str]]:
    """
    Parse GitHub URL to extract owner, repo, ref (branch/commit), and path.

    Returns:
        Tuple of (owner, repo, ref, path)
    """
    parsed_url = urlparse(repo_url)
    path_parts = parsed_url.path.strip('/').split('/')

    if len(path_parts) < 2:
        raise ValueError(f"Invalid GitHub URL: {repo_url}")

    owner = path_parts[0]
    repo = path_parts[1]
    ref = None
    path = None

    # Check if URL contains tree/blob reference
    if len(path_parts) > 3 and path_parts[2] in ['tree', 'blob']:
        ref = path_parts[3]
        if len(path_parts) > 4:
            path = '/'.join(path_parts[4:])

    return owner, repo, ref, path


def get_repository_metadata(owner: str, repo: str, token: Optional[str] = None) -> Dict[str, Any]:
    """Get repository metadata including primary language."""
    headers = {"Accept": "application/vnd.github.v3+json"}
    if token:
        headers["Authorization"] = f"token {token}"

    url = f"https://api.github.com/repos/{owner}/{repo}"
    response = requests.get(url, headers=headers)

    if response.status_code == 404:
        if not token:
            raise ValueError(
                f"Repository not found or is private. "
                f"If this is a private repository, please provide a valid GitHub token."
            )
        else:
            raise ValueError(
                f"Repository not found or insufficient permissions with the provided token."
            )

    if response.status_code != 200:
        raise ValueError(f"Error fetching repository metadata: {response.status_code} - {response.text}")

    return response.json()


def get_repository_tree(owner: str, repo: str, ref: str = "main", token: Optional[str] = None) -> List[Dict[str, Any]]:
    """Get complete file tree of the repository in one request."""
    headers = {"Accept": "application/vnd.github.v3+json"}
    if token:
        headers["Authorization"] = f"token {token}"

    # Try the provided ref first
    url = f"https://api.github.com/repos/{owner}/{repo}/git/trees/{ref}?recursive=1"
    response = requests.get(url, headers=headers)

    # If ref doesn't work, try common default branches
    if response.status_code == 404:
        for default_ref in ["master", "main", "develop"]:
            if default_ref != ref:
                url = f"https://api.github.com/repos/{owner}/{repo}/git/trees/{default_ref}?recursive=1"
                response = requests.get(url, headers=headers)
                if response.status_code == 200:
                    break

    if response.status_code == 403 and 'rate limit exceeded' in response.text.lower():
        reset_time = int(response.headers.get('X-RateLimit-Reset', 0))
        wait_time = max(reset_time - time.time(), 0) + 1
        print(f"Rate limit exceeded. Waiting for {wait_time:.0f} seconds...")
        time.sleep(wait_time)
        return get_repository_tree(owner, repo, ref, token)

    if response.status_code != 200:
        raise ValueError(f"Error fetching repository tree: {response.status_code} - {response.text}")

    data = response.json()
    return data.get("tree", [])


def download_file_content(owner: str, repo: str, path: str, ref: Optional[str] = None, token: Optional[str] = None) -> Optional[str]:
    """Download content of a single file from GitHub."""
    headers = {"Accept": "application/vnd.github.v3+json"}
    if token:
        headers["Authorization"] = f"token {token}"

    url = f"https://api.github.com/repos/{owner}/{repo}/contents/{path}"
    params = {"ref": ref} if ref else {}

    response = requests.get(url, headers=headers, params=params)

    if response.status_code == 403 and 'rate limit exceeded' in response.text.lower():
        reset_time = int(response.headers.get('X-RateLimit-Reset', 0))
        wait_time = max(reset_time - time.time(), 0) + 1
        print(f"Rate limit exceeded. Waiting for {wait_time:.0f} seconds...")
        time.sleep(wait_time)
        return download_file_content(owner, repo, path, ref, token)

    if response.status_code != 200:
        print(f"Failed to download {path}: {response.status_code}")
        return None

    content_data = response.json()

    # Handle base64 encoded content
    if content_data.get("encoding") == "base64" and "content" in content_data:
        try:
            content = base64.b64decode(content_data["content"]).decode('utf-8', errors='replace')
            return content
        except Exception as e:
            print(f"Failed to decode {path}: {e}")
            return None

    # Try download_url if available
    if "download_url" in content_data and content_data["download_url"]:
        file_response = requests.get(content_data["download_url"], headers=headers)
        if file_response.status_code == 200:
            return file_response.text

    return None


def phase1_initial_analysis(
    owner: str,
    repo: str,
    ref: Optional[str] = None,
    token: Optional[str] = None,
    max_file_size: int = 100000,  # 100KB for initial phase
    specific_path: Optional[str] = None
) -> Tuple[Dict[str, str], List[Tuple[str, int]]]:
    """
    Phase 1: Initial structure analysis with rules-based filtering and LLM-guided selection.

    Returns:
        Tuple of (files_dict, skipped_files_list)
    """
    print("Phase 1: Initial structure analysis...")

    # Get repository metadata
    try:
        repo_data = get_repository_metadata(owner, repo, token)
        primary_language = repo_data.get("language", "Unknown")
        print(f"Repository primary language: {primary_language}")
    except Exception as e:
        print(f"Warning: Could not fetch repository metadata: {e}")
        primary_language = "Unknown"

    # Get complete file tree
    try:
        tree_items = get_repository_tree(owner, repo, ref or "main", token)
        print(f"Found {len(tree_items)} items in repository tree")
    except Exception as e:
        print(f"Error fetching repository tree: {e}")
        return {}

    # Filter to only files (not directories) and apply path filter if specified
    file_items = []
    for item in tree_items:
        if item["type"] == "blob":
            if specific_path:
                # Only include files under the specific path
                if item["path"].startswith(specific_path):
                    file_items.append(item)
            else:
                file_items.append(item)

    print(f"Found {len(file_items)} files" + (f" under path '{specific_path}'" if specific_path else ""))

    # Apply rules-based selection first
    selected_files = []

    # 1. Always include documentation and configuration files
    doc_patterns = [
        "readme", "readme.md", "readme.txt", "readme.rst",
        "contributing.md", "changelog.md", "license", "license.md",
        "setup.py", "setup.cfg", "pyproject.toml", "requirements.txt",
        "package.json", "package-lock.json", "yarn.lock",
        "cargo.toml", "go.mod", "go.sum",
        "pom.xml", "build.gradle", "build.gradle.kts",
        "gemfile", "gemfile.lock",
        ".gitignore", ".dockerignore", "dockerfile", "docker-compose.yml",
        "makefile", "cmakelists.txt"
    ]

    for item in file_items:
        path_lower = item["path"].lower()
        filename_lower = os.path.basename(path_lower)

        # Check documentation patterns
        if any(filename_lower == pattern or filename_lower.startswith(pattern + ".") for pattern in doc_patterns):
            if item.get("size", 0) < max_file_size:
                selected_files.append(item["path"])
                continue

        # Include files in docs/ directory
        if path_lower.startswith("docs/") and path_lower.endswith((".md", ".rst", ".txt")):
            if item.get("size", 0) < max_file_size:
                selected_files.append(item["path"])

    print(f"Selected {len(selected_files)} documentation/config files")

    # 2. Include language-specific important files based on patterns
    language_patterns = {
        "Python": {
            "extensions": [".py"],
            "important_files": ["__init__.py", "main.py", "app.py", "setup.py", "manage.py"],
            "important_dirs": ["src/", "lib/", "app/", "core/", "api/", "models/", "views/", "utils/"]
        },
        "JavaScript": {
            "extensions": [".js", ".jsx", ".ts", ".tsx"],
            "important_files": ["index.js", "index.ts", "app.js", "app.ts", "main.js", "main.ts"],
            "important_dirs": ["src/", "lib/", "components/", "pages/", "api/", "utils/", "hooks/"]
        },
        "TypeScript": {
            "extensions": [".ts", ".tsx"],
            "important_files": ["index.ts", "app.ts", "main.ts"],
            "important_dirs": ["src/", "lib/", "components/", "pages/", "api/", "utils/", "hooks/"]
        },
        "Java": {
            "extensions": [".java"],
            "important_files": ["Main.java", "Application.java"],
            "important_dirs": ["src/main/java/", "src/main/", "src/"]
        },
        "Go": {
            "extensions": [".go"],
            "important_files": ["main.go", "server.go", "app.go"],
            "important_dirs": ["cmd/", "pkg/", "internal/", "api/"]
        },
        "Rust": {
            "extensions": [".rs"],
            "important_files": ["main.rs", "lib.rs", "mod.rs"],
            "important_dirs": ["src/", "lib/"]
        },
        "C++": {
            "extensions": [".cpp", ".cc", ".cxx", ".hpp", ".h"],
            "important_files": ["main.cpp", "main.cc"],
            "important_dirs": ["src/", "include/", "lib/"]
        },
        "C#": {
            "extensions": [".cs"],
            "important_files": ["Program.cs", "Startup.cs"],
            "important_dirs": ["src/", "Controllers/", "Models/", "Services/"]
        },
        "Ruby": {
            "extensions": [".rb"],
            "important_files": ["app.rb", "application.rb", "server.rb"],
            "important_dirs": ["app/", "lib/", "config/"]
        }
    }

    # Get patterns for the primary language
    lang_config = language_patterns.get(primary_language, {})
    if not lang_config:
        # Try to detect from file extensions if primary language is unknown
        extension_counts = {}
        for item in file_items:
            ext = os.path.splitext(item["path"])[1].lower()
            extension_counts[ext] = extension_counts.get(ext, 0) + 1

        # Find most common extension and map to language
        if extension_counts:
            most_common_ext = max(extension_counts, key=extension_counts.get)
            for lang, config in language_patterns.items():
                if most_common_ext in config.get("extensions", []):
                    lang_config = config
                    primary_language = lang
                    break

    # Select files based on language patterns
    if lang_config:
        extensions = lang_config.get("extensions", [])
        important_files = lang_config.get("important_files", [])
        important_dirs = lang_config.get("important_dirs", [])

        for item in file_items:
            if item["path"] in selected_files:
                continue

            path = item["path"]
            filename = os.path.basename(path)
            ext = os.path.splitext(path)[1].lower()

            # Check if it's an important file
            if filename.lower() in important_files:
                if item.get("size", 0) < max_file_size:
                    selected_files.append(path)
                    continue

            # Check if it's in an important directory with the right extension
            if ext in extensions:
                for important_dir in important_dirs:
                    if important_dir in path:
                        if item.get("size", 0) < max_file_size:
                            selected_files.append(path)
                            break

    # Remove duplicates
    selected_files = list(set(selected_files))
    print(f"Selected {len(selected_files)} files after language-specific filtering")

    # 3. Use LLM to identify additional important files
    if len(file_items) > len(selected_files):
        # Prepare file structure for LLM (limit to prevent token overflow)
        file_structure = []
        for item in file_items[:200]:  # Limit to first 200 files
            if item["path"] not in selected_files:
                file_structure.append({
                    "path": item["path"],
                    "size": item.get("size", 0)
                })

        if file_structure:
            prompt = f"""You are analyzing a GitHub repository structure to determine which files are most important for understanding the codebase.

Repository: {owner}/{repo}
Primary Language: {primary_language}

Here is a list of files that haven't been selected yet (showing paths and sizes in bytes):
{json.dumps(file_structure, indent=2)}

I've already selected these files using standard rules:
{json.dumps(selected_files[:50], indent=2)}{"..." if len(selected_files) > 50 else ""}

Please identify up to 20 additional files that appear most important for understanding the core functionality.
Consider:
- Entry points (main files, index files)
- Core modules and services
- Key domain models and business logic
- Important API endpoints or routes
- Critical utility functions
- Test files that demonstrate usage

Return ONLY a JSON array of file paths, nothing else. Example:
["src/core/engine.py", "src/api/routes.py", "src/models/user.py"]
"""

            try:
                llm_response = call_llm(prompt, use_cache=True)
                # Try to extract JSON from the response
                json_match = re.search(r'\[.*?\]', llm_response, re.DOTALL)
                if json_match:
                    additional_files = json.loads(json_match.group())
                    # Validate that these files exist in the tree
                    valid_files = [f for f in additional_files if any(item["path"] == f for item in file_items)]
                    selected_files.extend(valid_files)
                    print(f"LLM selected {len(valid_files)} additional files")
            except Exception as e:
                print(f"Warning: LLM file selection failed: {e}")

    # Remove duplicates again
    selected_files = list(set(selected_files))
    print(f"Total files selected for download: {len(selected_files)}")

    # Download selected files
    files_data = {}
    skipped_files = []
    downloaded_count = 0
    failed_count = 0

    for file_path in selected_files:
        # Get file size from tree
        file_item = next((item for item in file_items if item["path"] == file_path), None)
        file_size = file_item.get("size", 0) if file_item else 0

        # Final size check before download
        if file_size > max_file_size:
            skipped_files.append((file_path, file_size))
            print(f"Skipping {file_path}: size {file_size} exceeds phase 1 limit {max_file_size}")
            continue

        print(f"Downloading: {file_path}")
        content = download_file_content(owner, repo, file_path, ref, token)
        if content is not None:
            # Check actual content size
            content_size = len(content.encode('utf-8'))
            if content_size > max_file_size:
                skipped_files.append((file_path, content_size))
                print(f"Skipping {file_path}: actual size {content_size} exceeds limit")
                continue
            files_data[file_path] = content
            downloaded_count += 1
        else:
            failed_count += 1

    print(f"Phase 1 complete: Downloaded {downloaded_count} files, {failed_count} failed, {len(skipped_files)} skipped")
    return files_data, skipped_files


def phase2_adaptive_refinement(
    owner: str,
    repo: str,
    ref: Optional[str],
    token: Optional[str],
    abstractions: List[Dict[str, Any]],
    already_downloaded: Set[str],
    tree_items: Optional[List[Dict[str, Any]]] = None,
    max_additional_files: int = 20,
    max_file_size: int = 1 * 1024 * 1024  # 1MB default
) -> Tuple[Dict[str, str], List[Tuple[str, int]]]:
    """
    Phase 2: Adaptive refinement based on identified abstractions.

    Returns:
        Tuple of (files_dict, skipped_files_list)
    """
    print("Phase 2: Adaptive refinement based on identified abstractions...")

    # Get file tree if not provided
    if tree_items is None:
        try:
            tree_items = get_repository_tree(owner, repo, ref or "main", token)
        except Exception as e:
            print(f"Error fetching repository tree: {e}")
            return {}

    # Filter to files not yet downloaded
    remaining_files = []
    for item in tree_items:
        if item["type"] == "blob" and item["path"] not in already_downloaded:
            remaining_files.append({
                "path": item["path"],
                "size": item.get("size", 0)
            })

    if not remaining_files:
        print("No additional files to consider")
        return {}

    # Prepare abstraction descriptions for LLM
    abstraction_descriptions = []
    for abstr in abstractions[:10]:  # Limit to first 10 abstractions
        desc = f"- {abstr.get('name', 'Unknown')}: {abstr.get('description', 'No description')}"
        if len(desc) > 150:
            desc = desc[:147] + "..."
        abstraction_descriptions.append(desc)

    abstraction_text = "\n".join(abstraction_descriptions)

    # Prepare list of already downloaded files (sample)
    downloaded_sample = list(already_downloaded)[:30]

    # Prepare remaining files list (sample)
    remaining_sample = remaining_files[:150]

    prompt = f"""Based on these key abstractions identified in the codebase:

{abstraction_text}

I need to find additional files that would help explain and understand these abstractions better.

Already downloaded files (sample):
{json.dumps(downloaded_sample, indent=2)}{"..." if len(already_downloaded) > 30 else ""}
Total downloaded: {len(already_downloaded)} files

Remaining files to choose from:
{json.dumps(remaining_sample, indent=2)}{"..." if len(remaining_files) > 150 else ""}
Total remaining: {len(remaining_files)} files

Please identify up to {max_additional_files} additional files that would be most valuable for understanding the identified abstractions.
Focus on:
- Implementation files for the abstractions
- Usage examples and test files
- Related utility or helper files
- Configuration files that affect these abstractions
- Documentation specific to these components

Return ONLY a JSON array of file paths, nothing else."""

    try:
        llm_response = call_llm(prompt, use_cache=True)
        # Try to extract JSON from the response
        json_match = re.search(r'\[.*?\]', llm_response, re.DOTALL)
        if json_match:
            additional_file_paths = json.loads(json_match.group())
            # Validate that these files exist and aren't already downloaded
            valid_paths = []
            for path in additional_file_paths:
                if path not in already_downloaded and any(f["path"] == path for f in remaining_files):
                    valid_paths.append(path)

            print(f"LLM identified {len(valid_paths)} additional files for phase 2")
        else:
            print("Could not parse LLM response for additional files")
            valid_paths = []
    except Exception as e:
        print(f"Warning: Phase 2 LLM selection failed: {e}")
        valid_paths = []

    # Download the additional files
    additional_files = {}
    skipped_files = []
    downloaded_count = 0
    failed_count = 0

    for file_path in valid_paths[:max_additional_files]:
        # Get file size from remaining files
        file_info = next((f for f in remaining_files if f["path"] == file_path), None)
        file_size = file_info.get("size", 0) if file_info else 0

        # Check size before download
        if file_size > max_file_size:
            skipped_files.append((file_path, file_size))
            print(f"Skipping {file_path}: size {file_size} exceeds limit")
            continue

        print(f"Downloading additional file: {file_path}")
        content = download_file_content(owner, repo, file_path, ref, token)
        if content is not None:
            # Check actual content size
            content_size = len(content.encode('utf-8'))
            if content_size > max_file_size:
                skipped_files.append((file_path, content_size))
                print(f"Skipping {file_path}: actual size {content_size} exceeds limit")
                continue
            additional_files[file_path] = content
            downloaded_count += 1
        else:
            failed_count += 1

    print(f"Phase 2 complete: Downloaded {downloaded_count} additional files, {failed_count} failed, {len(skipped_files)} skipped")
    return additional_files, skipped_files


def identify_abstractions_from_files(files_data: Dict[str, str], repo_name: str) -> List[Dict[str, Any]]:
    """
    Simple abstraction identification from files for phase 2.
    This is a simplified version - in production, this would be more sophisticated.
    """
    # Create a summary of the codebase for abstraction identification
    file_summaries = []
    for path, content in list(files_data.items())[:20]:  # Limit to first 20 files
        lines = content.split('\n')[:50]  # First 50 lines
        file_summaries.append(f"File: {path}\nPreview:\n{chr(10).join(lines[:10])}\n")

    prompt = f"""Analyze these files from the {repo_name} repository and identify the key abstractions (main concepts, patterns, or components).

Files analyzed:
{''.join(file_summaries[:10])}

Identify 3-5 key abstractions in this codebase. For each abstraction provide:
- name: A short name for the abstraction
- description: A brief description (1-2 sentences)

Return ONLY a JSON array with this structure:
[
  {{"name": "AbstractionName", "description": "Brief description"}},
  ...
]
"""

    try:
        response = call_llm(prompt, use_cache=True)
        json_match = re.search(r'\[.*?\]', response, re.DOTALL)
        if json_match:
            abstractions = json.loads(json_match.group())
            return abstractions
        else:
            return []
    except Exception as e:
        print(f"Failed to identify abstractions: {e}")
        return []


def crawl_github_files(
    repo_url,
    token=None,
    max_file_size: int = 1 * 1024 * 1024,  # 1 MB
    use_relative_paths: bool = False,
    include_patterns: Union[str, Set[str]] = None,
    exclude_patterns: Union[str, Set[str]] = None,
    enable_two_phase: bool = True  # New parameter to control two-phase behavior
):
    """
    Two-phase intelligent GitHub repository crawler.

    This implementation maintains compatibility with the existing interface while providing
    a more intelligent crawling strategy that combines rules-based filtering with LLM-guided selection.

    Args:
        repo_url (str): URL of the GitHub repository
        token (str, optional): GitHub personal access token
        max_file_size (int, optional): Maximum file size in bytes to download (default: 1 MB)
        use_relative_paths (bool, optional): If True, file paths will be relative to the specified subdirectory
        include_patterns (str or set of str, optional): Pattern or set of patterns specifying which files to include
        exclude_patterns (str or set of str, optional): Pattern or set of patterns specifying which files to exclude
        enable_two_phase (bool, optional): Enable two-phase crawling with abstraction-based refinement (default: True)

    Returns:
        dict: Dictionary with files and statistics
    """
    # Handle SSH URLs by falling back to the original implementation
    if repo_url.startswith("git@") or repo_url.endswith(".git"):
        print("SSH URL detected, using git clone method...")
        # Import and use the original SSH handling code
        return _crawl_github_files_ssh(repo_url, token, max_file_size, use_relative_paths, include_patterns, exclude_patterns)

    # Parse GitHub URL
    try:
        owner, repo, ref, specific_path = parse_github_url(repo_url)
    except ValueError as e:
        return {"files": {}, "stats": {"error": str(e)}}

    print(f"Repository: {owner}/{repo}")
    print(f"Reference: {ref or 'default branch'}")
    print(f"Path: {specific_path or 'root'}")

    # Convert patterns to sets
    if include_patterns and isinstance(include_patterns, str):
        include_patterns = {include_patterns}
    if exclude_patterns and isinstance(exclude_patterns, str):
        exclude_patterns = {exclude_patterns}

    # Phase 1: Initial structure analysis
    skipped_files_total = []
    try:
        initial_files, phase1_skipped = phase1_initial_analysis(
            owner, repo, ref, token,
            max_file_size=min(max_file_size, 100000),  # Use smaller size for initial phase
            specific_path=specific_path
        )
        skipped_files_total.extend(phase1_skipped)
    except Exception as e:
        print(f"Phase 1 failed: {e}")
        return {"files": {}, "stats": {"error": str(e)}}

    # Apply include/exclude patterns if specified
    if include_patterns or exclude_patterns:
        filtered_files = {}
        for file_path, content in initial_files.items():
            filename = os.path.basename(file_path)

            # Check include patterns
            if include_patterns:
                if not any(fnmatch.fnmatch(filename, pattern) for pattern in include_patterns):
                    continue

            # Check exclude patterns
            if exclude_patterns:
                if any(fnmatch.fnmatch(file_path, pattern) for pattern in exclude_patterns):
                    continue

            filtered_files[file_path] = content

        initial_files = filtered_files
        print(f"After pattern filtering: {len(initial_files)} files")

    all_files = initial_files
    phase2_files_dict = {}
    abstractions = []

    # Phase 2: Adaptive refinement (if enabled and we have initial files)
    if enable_two_phase and initial_files:
        print("Identifying abstractions from initial files...")
        abstractions = identify_abstractions_from_files(initial_files, repo)

        if abstractions:
            print(f"Identified {len(abstractions)} key abstractions")
            try:
                # Get the tree for phase 2 (we can reuse it)
                tree_items = get_repository_tree(owner, repo, ref or "main", token)

                phase2_files_dict, phase2_skipped = phase2_adaptive_refinement(
                    owner, repo, ref, token,
                    abstractions=abstractions,
                    already_downloaded=set(initial_files.keys()),
                    tree_items=tree_items,
                    max_additional_files=20,
                    max_file_size=max_file_size
                )

                # Merge phase 2 files and skipped lists
                all_files.update(phase2_files_dict)
                skipped_files_total.extend(phase2_skipped)
            except Exception as e:
                print(f"Phase 2 failed: {e}")
                # Continue with phase 1 files only

    # Apply relative paths if requested
    if use_relative_paths and specific_path:
        relative_files = {}
        for file_path, content in all_files.items():
            if file_path.startswith(specific_path):
                rel_path = file_path[len(specific_path):].lstrip('/')
            else:
                rel_path = file_path
            relative_files[rel_path] = content
        all_files = relative_files

    # Prepare statistics
    stats = {
        "downloaded_count": len(all_files),
        "skipped_count": len(skipped_files_total),
        "skipped_files": skipped_files_total,
        "base_path": specific_path if use_relative_paths else None,
        "include_patterns": list(include_patterns) if include_patterns else None,
        "exclude_patterns": list(exclude_patterns) if exclude_patterns else None,
        "crawl_method": "two_phase_intelligent",
        "phase1_files": len(initial_files),
        "phase2_files": len(phase2_files_dict),
        "abstractions_found": len(abstractions)
    }

    return {
        "files": all_files,
        "stats": stats
    }


def _crawl_github_files_ssh(
    repo_url,
    token=None,
    max_file_size: int = 1 * 1024 * 1024,
    use_relative_paths: bool = False,
    include_patterns: Union[str, Set[str]] = None,
    exclude_patterns: Union[str, Set[str]] = None
):
    """
    Original SSH implementation for backward compatibility.
    """
    # Import git only when needed to avoid initialization issues
    try:
        import git
    except ImportError as e:
        return {"files": {}, "stats": {"error": f"GitPython not available: {e}"}}
    except Exception as e:
        return {"files": {}, "stats": {"error": f"Git initialization failed: {e}"}}

    # Convert single pattern to set
    if include_patterns and isinstance(include_patterns, str):
        include_patterns = {include_patterns}
    if exclude_patterns and isinstance(exclude_patterns, str):
        exclude_patterns = {exclude_patterns}

    def should_include_file(file_path: str, file_name: str) -> bool:
        """Determine if a file should be included based on patterns"""
        # If no include patterns are specified, include all files
        if not include_patterns:
            include_file = True
        else:
            # Check if file matches any include pattern
            include_file = any(fnmatch.fnmatch(file_name, pattern) for pattern in include_patterns)

        # If exclude patterns are specified, check if file should be excluded
        if exclude_patterns and include_file:
            # Exclude if file matches any exclude pattern
            exclude_file = any(fnmatch.fnmatch(file_path, pattern) for pattern in exclude_patterns)
            return not exclude_file

        return include_file

    # Clone repo via SSH to temp dir
    with tempfile.TemporaryDirectory() as tmpdirname:
        print(f"Cloning SSH repo {repo_url} to temp dir {tmpdirname} ...")
        try:
            repo = git.Repo.clone_from(repo_url, tmpdirname)
        except Exception as e:
            print(f"Error cloning repo: {e}")
            return {"files": {}, "stats": {"error": str(e)}}

        # Walk directory
        files = {}
        skipped_files = []

        for root, dirs, filenames in os.walk(tmpdirname):
            for filename in filenames:
                abs_path = os.path.join(root, filename)
                rel_path = os.path.relpath(abs_path, tmpdirname)

                # Check file size
                try:
                    file_size = os.path.getsize(abs_path)
                except OSError:
                    continue

                if file_size > max_file_size:
                    skipped_files.append((rel_path, file_size))
                    print(f"Skipping {rel_path}: size {file_size} exceeds limit {max_file_size}")
                    continue

                # Check include/exclude patterns
                if not should_include_file(rel_path, filename):
                    print(f"Skipping {rel_path}: does not match include/exclude patterns")
                    continue

                # Read content
                try:
                    with open(abs_path, "r", encoding="utf-8-sig") as f:
                        content = f.read()
                    files[rel_path] = content
                    print(f"Added {rel_path} ({file_size} bytes)")
                except Exception as e:
                    print(f"Failed to read {rel_path}: {e}")

        return {
            "files": files,
            "stats": {
                "downloaded_count": len(files),
                "skipped_count": len(skipped_files),
                "skipped_files": skipped_files,
                "base_path": None,
                "include_patterns": list(include_patterns) if include_patterns else None,
                "exclude_patterns": list(exclude_patterns) if exclude_patterns else None,
                "source": "ssh_clone"
            }
        }