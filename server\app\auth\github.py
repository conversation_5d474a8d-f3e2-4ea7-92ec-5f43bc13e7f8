import requests
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional, List
from sqlalchemy.orm import Session
from github import Github
from datetime import timed<PERSON><PERSON>, datetime
import logging

from ..models.core import User, UserProfile, Organization, Repository
from ..config import settings
from .jwt import create_access_token

logger = logging.getLogger(__name__)

class GitHubAuthError(Exception):
    """Exception raised for GitHub authentication errors."""
    pass

class GitHubClient:
    """GitHub API client."""

    def __init__(self, access_token: str):
        """
        Initialize with a GitHub access token.

        Args:
            access_token: GitHub access token
        """
        self.access_token = access_token
        self.api_url = settings.GITHUB_ENTERPRISE_URL or "https://api.github.com"

        if access_token.startswith('gho_'):
            # This is an OAuth token from the web flow
            self.headers = {
                "Authorization": f"Bearer {access_token}",
                "Accept": "application/vnd.github.v3+json"
            }
        else:
            # This is a personal access token
            self.headers = {
                "Authorization": f"token {access_token}",
                "Accept": "application/vnd.github.v3+json"
            }

    def get_user(self) -> Dict[str, Any]:
        """
        Get the authenticated user's information.

        Returns:
            User information
        """
        try:
            response = requests.get(f"{self.api_url}/user", headers=self.headers)

            if response.status_code != 200:
                raise GitHubAuthError(f"Failed to get user: {response.text}")

            return response.json()
        except Exception as e:
            import traceback
            raise

    def get_user_organizations(self) -> list:
        """
        Get the authenticated user's organizations.

        Returns:
            List of organizations
        """
        response = requests.get(f"{self.api_url}/user/orgs", headers=self.headers)
        if response.status_code != 200:
            raise GitHubAuthError(f"Failed to get organizations: {response.text}")
        return response.json()

    def get_user_repositories(self, page: int = 1, per_page: int = 100, affiliation: str = "owner,collaborator,organization_member") -> List[Dict[str, Any]]:
        """
        Get repositories for the authenticated user.
        Docs: https://docs.github.com/en/rest/repos/repos#list-repositories-for-the-authenticated-user

        Args:
            page: Page number of the results to fetch.
            per_page: The number of results per page (max 100).
            affiliation: Comma-separated list of affiliations. 
                         Can be one or more of: owner, collaborator, organization_member.
                         Default lists all repos the user has access to.

        Returns:
            List of repository data from GitHub.
        """
        params = {
            "page": page,
            "per_page": per_page,
            "affiliation": affiliation,
            "sort": "updated", # Get most recently updated first
            "direction": "desc"
        }
        # The /user/repos endpoint lists repositories for the authenticated user.
        # We can also use /users/{username}/repos, but /user/repos is simpler for the auth'd user.
        response = requests.get(f"{self.api_url}/user/repos", headers=self.headers, params=params)
        if response.status_code != 200:
            raise GitHubAuthError(f"Failed to get user repositories: {response.status_code} - {response.text}")
        return response.json()

class GitHubTokenAuth:
    """
    Handles GitHub authentication using a personal access token.
    """

    def __init__(self, token: str):
        """
        Initialize with a GitHub personal access token.

        Args:
            token: GitHub personal access token
        """
        self.token = token
        self.client = GitHubClient(access_token=token)

    def validate_token(self) -> Optional[Dict[str, Any]]:
        """
        Validate the token by making a test API call.

        Returns:
            User data if the token is valid, None otherwise
        """
        try:
            # Try to get user info to validate the token
            user_data = self.client.get_user()
            if user_data and 'id' in user_data:
                return user_data
            return None
        except Exception as e:
            import traceback
            # Optionally log the error e
            return None

    def get_or_create_user(self, db: Session, user_data_from_validation: Optional[Dict[str, Any]] = None) -> Tuple[User, UserProfile, bool, Dict[str, str]]:
        """
        Get or create a user from GitHub data.

        Args:
            db: Database session
            user_data_from_validation: Optional pre-fetched user data from token validation

        Returns:
            Tuple of (user, profile, created, tokens)
        """
        try:
            user_data = user_data_from_validation if user_data_from_validation else self.client.get_user()
            if not user_data or 'id' not in user_data: 
                raise GitHubAuthError("Failed to get valid user data from GitHub.")
            github_id = str(user_data["id"])
            profile = db.query(UserProfile).filter(UserProfile.github_id == github_id).first()
        except Exception as e:
            import traceback
            raise

        try:
            if profile:
                # User exists, update token
                profile.github_access_token = self.token
                user = profile.user
                db.commit()
                created = False
            else:
                # Create new user
                username = user_data["login"]
                email = user_data.get("email")
                if not email:
                    email = f"{username}+{github_id}@user.github.com"

                user = User(
                    username=username,
                    email=email,
                    first_name=user_data.get("name", "").split(" ")[0] if user_data.get("name") else "",
                    last_name=" ".join(user_data.get("name", "").split(" ")[1:]) if user_data.get("name") and len(user_data.get("name", "").split(" ")) > 1 else "",
                    is_active=True
                )
                db.add(user)
                db.flush()

                profile = UserProfile(
                    user_id=user.id,
                    github_id=github_id,
                    github_access_token=self.token,
                    github_username=username,
                    github_avatar_url=user_data.get("avatar_url"),
                    is_active=True
                )
                db.add(profile)
                db.commit()
                created = True
            
            # Make sure profile is refreshed to get its ID if newly created, and user is associated
            db.refresh(profile)
            if not profile.user:
                profile.user = user

            # Update organizations
            try:
                self.update_user_organizations(db, profile)
            except Exception as e_org:
                logger.error(f"Error updating organizations for {profile.github_username}: {e_org}", exc_info=True)
                # Decide if this is a fatal error for login

            # Sync user repositories
            try:
                self.sync_user_repositories(db, profile, user_data_from_validation or user_data)
            except Exception as e_repo_sync:
                logger.error(f"Error syncing repositories for {profile.github_username}: {e_repo_sync}", exc_info=True)
                # Decide if this is a fatal error for login

            # Generate JWT tokens
            access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
            tokens = {
                "access": create_access_token(
                    data={"sub": user.username},
                    expires_delta=access_token_expires
                ),
                "refresh": create_access_token(
                    data={"sub": user.username, "refresh": True},
                    expires_delta=timedelta(days=7)
                )
            }
            return user, profile, created, tokens
        except Exception as e_main_logic:
            logger.error(f"Unexpected error in get_or_create_user main logic: {str(e_main_logic)}", exc_info=True)
            raise

    def sync_user_repositories(self, db: Session, user_profile: UserProfile, user_github_data: Dict[str, Any]):
        """
        Fetches repositories from GitHub for the user and syncs them to the database.
        Only syncs repositories directly owned by the user for simplicity now.
        Args:
            db: Database session.
            user_profile: The user's UserProfile object from the database.
            user_github_data: The user's raw data from GitHub API (/user).
        """
        logger.info(f"Starting repository sync for user: {user_profile.github_username}")
        
        # Fetch all repositories the user has access to (owner, collaborator, org_member)
        # We will then filter for those directly owned by the user, or decide how to handle others.
        all_github_repos = []
        page = 1
        while True:
            try:
                logger.info(f"Fetching page {page} of repositories for {user_profile.github_username}")
                # Using default affiliation to get all accessible repos first
                repos_page = self.client.get_user_repositories(page=page, per_page=100)
                if not repos_page:
                    break
                all_github_repos.extend(repos_page)
                page += 1
                if len(repos_page) < 100: # Break if last page was not full
                    break
            except GitHubAuthError as e:
                logger.error(f"GitHub API error while fetching repositories for {user_profile.github_username}, page {page}: {e}")
                break # Stop if there's an auth error or other API issue
            except Exception as e_page:
                logger.error(f"Unexpected error fetching repositories page {page} for {user_profile.github_username}: {e_page}", exc_info=True)
                break

        logger.info(f"Fetched a total of {len(all_github_repos)} repository entries from GitHub for {user_profile.github_username}.")

        synced_count = 0
        created_count = 0
        updated_count = 0

        for repo_data in all_github_repos:
            github_repo_id = str(repo_data["id"])
            repo_owner_login = repo_data["owner"]["login"]
            repo_owner_id_str = str(repo_data["owner"]["id"])

            # For this sync, let's only create/update repositories directly owned by the authenticated user.
            # user_github_data['id'] is the authenticated user's GitHub ID.
            # user_github_data['login'] is the authenticated user's GitHub login.
            if repo_owner_id_str != str(user_github_data['id']):
                 logger.info(f"Skipping repo '{repo_data['full_name']}' as it's owned by '{repo_owner_login}', not by the authenticated user '{user_profile.github_username}'.")
                 continue # Skip if not owned by the current user for this simple sync

            # Check if repository exists
            existing_repo = db.query(Repository).filter(Repository.github_id == github_repo_id).first()

            if existing_repo:
                # Update existing repository
                update_needed = False
                if existing_repo.name != repo_data["name"]: 
                    existing_repo.name = repo_data["name"]
                    update_needed = True
                if existing_repo.full_name != repo_data["full_name"]: 
                    existing_repo.full_name = repo_data["full_name"]
                    update_needed = True
                if existing_repo.description != repo_data.get("description"):
                    existing_repo.description = repo_data.get("description")
                    update_needed = True
                if existing_repo.is_private != repo_data["private"]:
                    existing_repo.is_private = repo_data["private"]
                    update_needed = True
                if existing_repo.github_url != repo_data["html_url"]:
                     existing_repo.github_url = repo_data["html_url"]
                     update_needed = True
                # Ensure owner_id is correct (should be, as we filtered by owner)
                if existing_repo.owner_id != user_profile.id:
                    existing_repo.owner_id = user_profile.id
                    update_needed = True
                
                if update_needed:
                    existing_repo.updated_at = datetime.utcnow()
                    updated_count +=1
                    logger.info(f"Updating repository: {existing_repo.full_name} (ID: {existing_repo.github_id})")
            else:
                # Create new repository
                logger.info(f"Creating new repository: {repo_data['full_name']} (ID: {github_repo_id}) for owner {user_profile.github_username}")
                new_repo = Repository(
                    name=repo_data["name"],
                    full_name=repo_data["full_name"],
                    github_id=github_repo_id,
                    github_url=repo_data["html_url"],
                    description=repo_data.get("description"),
                    is_private=repo_data["private"],
                    owner_id=user_profile.id,  # Link to our UserProfile ID
                    # organization_id would be None for user-owned repo
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                db.add(new_repo)
                created_count += 1
            
            synced_count += 1
            # Commit per repository or in batch? For now, let's commit at the end of loop or in larger batches.
            if synced_count % 20 == 0: # Commit every 20 repos
                logger.info(f"Committing batch of synced repositories ({synced_count}/{len(all_github_repos)} processed for ownership check)")
                db.commit()
        
        db.commit() # Final commit
        logger.info(f"Repository sync finished for {user_profile.github_username}. Total GitHub entries processed for ownership: {len(all_github_repos)}. Repos relevant to user: {synced_count}. Created: {created_count}, Updated: {updated_count}.")

    def update_user_organizations(self, db: Session, profile: UserProfile) -> None:
        """
        Update the user's organizations.

        Args:
            db: Database session
            profile: User profile
        """
        # Get organizations from GitHub
        orgs_data = self.client.get_user_organizations()

        # Clear existing organizations
        profile.organizations = []

        # Add organizations
        for org_data in orgs_data:
            # Check if organization exists
            github_id = str(org_data["id"])
            org = db.query(Organization).filter(Organization.github_id == github_id).first()

            if not org:
                # Create organization
                org = Organization(
                    name=org_data["login"],
                    github_id=github_id,
                    github_url=org_data["url"]
                )
                db.add(org)
                db.flush()

            # Add organization to user
            profile.organizations.append(org)

        db.commit()
