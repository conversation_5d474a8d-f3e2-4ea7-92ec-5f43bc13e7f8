'use client';

import { useEffect, useRef } from 'react';

let mermaidInitialized = false;

export function MermaidInitializer() {
  const initRef = useRef(false);

  useEffect(() => {
    if (initRef.current || mermaidInitialized) return;
    initRef.current = true;
    mermaidInitialized = true;

    const initMermaid = async () => {
      try {
        const mermaidModule = await import('mermaid');
        const mermaid = mermaidModule.default;
        
        // Initialize only once with optimal configuration
        mermaid.initialize({
          startOnLoad: false, // We'll handle rendering manually
          theme: 'default',
          securityLevel: 'loose',
          fontFamily: 'inherit',
          flowchart: {
            htmlLabels: true,
            curve: 'basis',
            useMaxWidth: true,
            padding: 15
          },
          sequence: {
            diagramMarginX: 50,
            diagramMarginY: 10,
            actorMargin: 50,
            width: 150,
            height: 65,
            boxMargin: 10,
            boxTextMargin: 5,
            noteMargin: 10,
            messageMargin: 35,
            mirrorActors: false,
            bottomMarginAdj: 1,
            useMaxWidth: true
          },
          gitGraph: {
            useMaxWidth: true
          },
          journey: {
            useMaxWidth: true
          },
          er: {
            useMaxWidth: true
          },
          pie: {
            useMaxWidth: true
          }
        });
        
        // Make mermaid globally available
        window.mermaid = mermaid;
        
      } catch (error) {
        console.error('Failed to initialize Mermaid:', error);
      }
    };

    initMermaid();
  }, []);

  return null;
}

export default MermaidInitializer; 