import axios from 'axios';

// Get API URL from environment variables
const API_URL = process.env.NEXT_PUBLIC_API_URL || '';

// Create axios instance with default config
const api = axios.create({
  baseURL: `${API_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: false, // Set to false for cross-origin requests without credentials
});

// GitHub API endpoints
export const githubApi = {
  // Get repositories - try both endpoints for backward compatibility
  getRepositories: async () => {
    try {
      // First try the GitHub API endpoint
      return await api.get('/github/repositories/');
    } catch (error) {
      // If that fails, try the core API endpoint
      console.log('Falling back to core API endpoint for repositories');
      return await api.get('/core/repositories/');
    }
  },
  getRepository: (owner: string, repoName: string) =>
    api.post('/github/repository/', { owner, repo_name: repoName }),
  getPullRequests: (owner: string, repoName: string, state = 'open') =>
    api.post('/github/pull-requests/list/', { owner, repo_name: repoName, state }),
  getPullRequest: (owner: string, repoName: string, prNumber: number) =>
    api.post('/github/pull-requests/detail/', { owner, repo_name: repoName, pr_number: prNumber }),
  getPullRequestFiles: (owner: string, repoName: string, prNumber: number) =>
    api.post('/github/pull-requests/files/', { owner, repo_name: repoName, pr_number: prNumber }),
  getPullRequestComments: (owner: string, repoName: string, prNumber: number) =>
    api.post('/github/pull-requests/comments/', { owner, repo_name: repoName, pr_number: prNumber }),
  addPullRequestComment: (owner: string, repoName: string, prNumber: number, body: string) =>
    api.post('/github/pull-requests/add-comment/', { owner, repo_name: repoName, pr_number: prNumber, body }),
  getFileContent: (owner: string, repoName: string, path: string, ref?: string) =>
    api.post('/github/file-content/', { owner, repo_name: repoName, path, ref }),
  getCommit: (owner: string, repoName: string, sha: string) =>
    api.post('/github/commit/', { owner, repo_name: repoName, sha }),
  getCommitComments: (owner: string, repoName: string, sha: string) =>
    api.post('/github/commit/comments/', { owner, repo_name: repoName, sha }),
  addCommitComment: (owner: string, repoName: string, sha: string, body: string, path?: string, position?: number) =>
    api.post('/github/commit/add-comment/', { owner, repo_name: repoName, sha, body, path, position }),
  searchCode: (query: string, owner?: string, repoName?: string) =>
    api.post('/github/search/code/', { query, owner, repo_name: repoName }),
};

// Perplexity API endpoints
export const perplexityApi = {
  analyzeCode: (code: string, language: string, context?: string) =>
    api.post('/perplexity/analyze-code/', { code, language, context }),
  generateExplanation: (code: string, language: string) =>
    api.post('/perplexity/explain-code/', { code, language }),
  generateDocumentation: (code: string, language: string) =>
    api.post('/perplexity/generate-docs/', { code, language }),
  suggestImprovements: (code: string, language: string) =>
    api.post('/perplexity/suggest-improvements/', { code, language }),
  generateTests: (code: string, language: string) =>
    api.post('/perplexity/generate-tests/', { code, language }),
  reviewPullRequest: (owner: string, repoName: string, prNumber: number) =>
    api.post('/perplexity/review-pr/', { owner, repo_name: repoName, pr_number: prNumber }),
  askQuestion: (question: string, context?: string) =>
    api.post('/perplexity/ask/', { question, context }),
};

// Tutor API endpoints
export const tutorApi = {
  generateTutorial: (repositoryId: number, options?: {
    includePatterns?: string[],
    excludePatterns?: string[],
    language?: string,
    maxAbstractions?: number,
    useCache?: boolean
  }) =>
    api.post('/tutor/generate-tutorial/', {
      repository_id: repositoryId,
      include_patterns: options?.includePatterns,
      exclude_patterns: options?.excludePatterns,
      language: options?.language || 'english',
      max_abstractions: options?.maxAbstractions || 10,
      use_cache: options?.useCache !== false
    }),
  getTutorialStatus: (actionId: number) =>
    api.get(`/tutor/tutorial-status/${actionId}`),
  getRepositoryTutorials: (repositoryId: number) =>
    api.get(`/tutor/repository-tutorials/${repositoryId}`),
};

export default api;
