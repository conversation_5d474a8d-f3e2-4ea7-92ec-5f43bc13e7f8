'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import NavigationBar from '@/components/NavigationBar';

export default function Search() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('Find all implementations of authentication in my repositories');
  const [searchResults, setSearchResults] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const [searchMode, setSearchMode] = useState('semantic'); // semantic, regex, literal

  useEffect(() => {
    // Redirect to sign-in if not authenticated
    if (isLoaded && !user) {
      router.push('/sign-in');
    }
  }, [user, isLoaded, router]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSearching(true);

    // Simulate search results after a delay
    setTimeout(() => {
      setSearchResults(`# Search Results for: "${searchQuery}"\n\n## Repository: user-authentication-service\n\n### File: src/auth/AuthService.js\n\`\`\`javascript\nclass AuthService {\n  async authenticate(username, password) {\n    // Implementation of user authentication\n    const user = await this.userRepository.findByUsername(username);\n    if (!user) return null;\n    \n    const isValid = await this.passwordService.verify(password, user.passwordHash);\n    return isValid ? user : null;\n  }\n}\n\`\`\`\n\n### File: src/middleware/authMiddleware.js\n\`\`\`javascript\nfunction authMiddleware(req, res, next) {\n  const token = req.headers.authorization?.split(' ')[1];\n  if (!token) return res.status(401).json({ message: 'Authentication required' });\n  \n  try {\n    const decoded = jwt.verify(token, process.env.JWT_SECRET);\n    req.user = decoded;\n    next();\n  } catch (error) {\n    return res.status(401).json({ message: 'Invalid token' });\n  }\n}\n\`\`\`\n\n## Repository: frontend-app\n\n### File: src/hooks/useAuth.js\n\`\`\`javascript\nfunction useAuth() {\n  const [user, setUser] = useState(null);\n  \n  const login = async (username, password) => {\n    try {\n      const response = await api.post('/auth/login', { username, password });\n      localStorage.setItem('token', response.data.token);\n      setUser(response.data.user);\n      return true;\n    } catch (error) {\n      console.error('Login failed:', error);\n      return false;\n    }\n  };\n  \n  // More authentication methods...\n  \n  return { user, login, logout, isAuthenticated: !!user };\n}\n\`\`\``);
      setIsSearching(false);
    }, 2000);
  };

  if (!isLoaded) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to sign-in
  }

  return (
    <>
      <NavigationBar />
      <div className="pt-16 pb-6"> {/* Padding for the fixed navbar */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              AI-Powered Code Search
            </h1>

            <p className="text-gray-600 dark:text-gray-300 mb-8">
              Use natural language to search through your codebase. Ask questions about code structure,
              find specific implementations, or get help understanding complex parts of your repositories.
            </p>

            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-8">
              <form onSubmit={handleSearch}>
                <textarea
                  className="w-full px-3 py-2 text-gray-700 dark:text-gray-200 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 resize-none"
                  rows={4}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Enter your search query..."
                ></textarea>

                <div className="mt-4 flex justify-between items-center">
                  <button
                    type="submit"
                    disabled={isSearching}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    {isSearching ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Searching...
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        Search
                      </>
                    )}
                  </button>

                  <button
                    type="button"
                    className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                    onClick={() => setShowOptions(!showOptions)}
                  >
                    {showOptions ? 'Hide Options' : 'Show Options'}
                  </button>
                </div>

                {showOptions && (
                  <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-md">
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Search Options</h3>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Search Mode
                        </label>
                        <div className="flex space-x-4">
                          <label className="inline-flex items-center">
                            <input
                              type="radio"
                              className="form-radio text-blue-600"
                              name="searchMode"
                              value="semantic"
                              checked={searchMode === 'semantic'}
                              onChange={() => setSearchMode('semantic')}
                            />
                            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Semantic</span>
                          </label>
                          <label className="inline-flex items-center">
                            <input
                              type="radio"
                              className="form-radio text-blue-600"
                              name="searchMode"
                              value="regex"
                              checked={searchMode === 'regex'}
                              onChange={() => setSearchMode('regex')}
                            />
                            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Regex</span>
                          </label>
                          <label className="inline-flex items-center">
                            <input
                              type="radio"
                              className="form-radio text-blue-600"
                              name="searchMode"
                              value="literal"
                              checked={searchMode === 'literal'}
                              onChange={() => setSearchMode('literal')}
                            />
                            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Literal</span>
                          </label>
                        </div>
                      </div>

                      <div>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            className="form-checkbox text-blue-600"
                          />
                          <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Include archived repositories</span>
                        </label>
                      </div>

                      <div>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            className="form-checkbox text-blue-600"
                            defaultChecked
                          />
                          <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Search code comments</span>
                        </label>
                      </div>
                    </div>
                  </div>
                )}
              </form>
            </div>

            {searchResults && (
              <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Search Results
                </h2>
                <div className="prose dark:prose-invert max-w-none">
                  <div className="whitespace-pre-line">{searchResults}</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
