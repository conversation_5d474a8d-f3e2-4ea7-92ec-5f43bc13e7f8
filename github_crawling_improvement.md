# GitHub Repository Crawling: Current vs. Proposed Approach

## Current Approach

The current implementation in the tutor pipeline uses direct file downloads from GitHub repositories, which has several limitations:

### Implementation Overview

```python
def crawl_github_files(
    repo_url,
    token=None,
    max_file_size: int = 1 * 1024 * 1024,  # 1 MB
    use_relative_paths: bool = False,
    include_patterns: Union[str, Set[str]] = None,
    exclude_patterns: Union[str, Set[str]] = None
):
    # Parse GitHub URL
    parsed_url = urlparse(repo_url)
    path_parts = parsed_url.path.strip('/').split('/')

    # Extract owner, repo, branch, etc.
    owner = path_parts[0]
    repo = path_parts[1]

    # Set up headers with authentication if token provided
    headers = {"Accept": "application/vnd.github.v3+json"}
    if token:
        headers["Authorization"] = f"token {token}"

    # Get repository contents
    contents_url = f"https://api.github.com/repos/{owner}/{repo}/contents"
    response = requests.get(contents_url, headers=headers)

    # Process each file/directory
    files = {}
    for item in response.json():
        if item["type"] == "file":
            # Check file size
            if item["size"] <= max_file_size:
                # Apply include/exclude patterns
                if should_include_file(item["path"], include_patterns, exclude_patterns):
                    # Download file content
                    file_response = requests.get(item["download_url"])
                    files[item["path"]] = file_response.text
        elif item["type"] == "dir":
            # Recursively process subdirectories
            # ...

    return files
```

### Limitations

1. **Inefficiency**: Downloads files individually, resulting in many API requests
2. **Rate Limiting**: Easily hits GitHub API rate limits for larger repositories
3. **Static Filtering**: Uses predefined patterns that may not capture semantically important files
4. **Limited Context**: Doesn't consider the relationships between files or their importance
5. **Scalability Issues**: Struggles with large repositories due to the sequential nature of requests

## Proposed Two-Phase Approach

The proposed approach uses a more intelligent, adaptive strategy that combines rules-based filtering with LLM-guided selection.

### Phase 1: Initial Structure Analysis

First, we analyze the repository structure and select an initial set of important files:

```python
def phase1_initial_analysis(repo_owner, repo_name, token=None):
    # Get complete file structure without downloading content
    headers = {"Accept": "application/vnd.github.v3+json"}
    if token:
        headers["Authorization"] = f"token {token}"

    # Get repository metadata to determine primary language
    repo_url = f"https://api.github.com/repos/{repo_owner}/{repo_name}"
    repo_response = requests.get(repo_url, headers=headers)
    repo_data = repo_response.json()
    primary_language = repo_data.get("language")

    # Get complete file tree in one request
    tree_url = f"https://api.github.com/repos/{repo_owner}/{repo_name}/git/trees/main?recursive=1"
    tree_response = requests.get(tree_url, headers=headers)
    tree_data = tree_response.json()

    # Apply rules-based selection first
    selected_files = []

    # 1. Always include documentation and configuration files
    for item in tree_data["tree"]:
        if item["type"] == "blob" and (
            item["path"].lower().startswith("readme") or
            item["path"].lower().startswith("docs/") or
            item["path"].lower().endswith(".md") or
            item["path"].lower() in ["package.json", "setup.py", "requirements.txt", "pyproject.toml"]
        ) and item.get("size", 0) < 100000:  # Size limit
            selected_files.append(item["path"])

    # 2. Include language-specific important files
    language_patterns = {
        "Python": ["*.py"],
        "JavaScript": ["*.js", "*.jsx", "*.ts", "*.tsx"],
        "Java": ["*.java"],
        "Go": ["*.go"],
        # Add more languages as needed
    }

    patterns = language_patterns.get(primary_language, [])
    for item in tree_data["tree"]:
        if item["type"] == "blob" and any(fnmatch.fnmatch(item["path"], pattern) for pattern in patterns):
            if item.get("size", 0) < 100000:  # Size limit
                selected_files.append(item["path"])

    # 3. Use LLM to refine selection
    file_structure = [
        {"path": item["path"], "size": item.get("size", 0)}
        for item in tree_data["tree"]
        if item["type"] == "blob"
    ]

    prompt = f"""
    You are analyzing a GitHub repository structure to determine which files are most important for understanding the codebase.

    Repository: {repo_owner}/{repo_name}
    Primary Language: {primary_language}

    Here is the file structure (showing paths and sizes in bytes):
    {json.dumps(file_structure[:100], indent=2)}  # Limit to first 100 for token reasons

    I've already selected these files using standard rules:
    {json.dumps(selected_files, indent=2)}

    Please identify up to 20 additional files that appear most important for understanding the core functionality.
    Consider entry points, core modules, and key domain models.

    Return only a JSON array of file paths.
    """

    llm_response = call_llm(prompt)

    try:
        additional_files = json.loads(llm_response)
        selected_files.extend(additional_files)
    except:
        # Fallback parsing if LLM doesn't return valid JSON
        additional_files = re.findall(r'"([^"]+\.[^"]+)"', llm_response)
        selected_files.extend(additional_files)

    # Remove duplicates
    selected_files = list(set(selected_files))

    # Download selected files
    files_data = {}
    for path in selected_files:
        content_url = f"https://api.github.com/repos/{repo_owner}/{repo_name}/contents/{path}"
        content_response = requests.get(content_url, headers=headers)
        if content_response.status_code == 200:
            content_data = content_response.json()
            if content_data.get("encoding") == "base64" and "content" in content_data:
                content = base64.b64decode(content_data["content"]).decode('utf-8', errors='replace')
                files_data[path] = content

    return files_data
```

### Phase 2: Adaptive Refinement

After identifying key abstractions from the initial files, we determine if additional files are needed:

```python
def phase2_adaptive_refinement(repo_owner, repo_name, token, abstractions, already_downloaded):
    # Get file structure again (or use cached from Phase 1)
    headers = {"Authorization": f"token {token}"} if token else {}
    tree_url = f"https://api.github.com/repos/{repo_owner}/{repo_name}/git/trees/main?recursive=1"
    tree_response = requests.get(tree_url, headers=headers)
    tree_data = tree_response.json()

    # Create a list of files not yet downloaded
    remaining_files = [
        item["path"] for item in tree_data["tree"]
        if item["type"] == "blob" and item["path"] not in already_downloaded
    ]

    # Use LLM to identify which additional files would help explain the abstractions
    abstraction_descriptions = "\n".join([
        f"- {a['name']}: {a['description'][:100]}..." for a in abstractions
    ])

    prompt = f"""
    Based on these key abstractions identified in the codebase:

    {abstraction_descriptions}

    Which of these additional files would be most helpful for understanding and explaining these abstractions?

    Already downloaded files:
    {json.dumps(list(already_downloaded)[:20])}... (and {len(already_downloaded)-20} more)

    Remaining files to choose from:
    {json.dumps(remaining_files[:100])}... (and {len(remaining_files)-100} more)

    Return a JSON array with the paths of up to 20 additional files that would be most valuable.
    """

    llm_response = call_llm(prompt)

    try:
        additional_file_paths = json.loads(llm_response)
    except:
        # Fallback parsing
        additional_file_paths = re.findall(r'"([^"]+\.[^"]+)"', llm_response)

    # Download the additional files
    additional_files = {}
    for path in additional_file_paths:
        if path in already_downloaded:
            continue

        content_url = f"https://api.github.com/repos/{repo_owner}/{repo_name}/contents/{path}"
        content_response = requests.get(content_url, headers=headers)
        if content_response.status_code == 200:
            content_data = content_response.json()
            if content_data.get("encoding") == "base64" and "content" in content_data:
                content = base64.b64decode(content_data["content"]).decode('utf-8', errors='replace')
                additional_files[path] = content

    return additional_files
```

### Complete Two-Phase Implementation

Here's how to integrate both phases into a complete solution:

```python
def two_phase_github_crawler(repo_url, token=None):
    # Parse GitHub URL
    owner, repo = parse_github_url(repo_url)

    # Phase 1: Initial Structure Analysis
    print("Phase 1: Initial structure analysis...")
    initial_files = phase1_initial_analysis(owner, repo, token)

    # Preliminary abstraction identification
    print("Identifying key abstractions from initial files...")
    abstractions = identify_abstractions(initial_files)

    # Phase 2: Adaptive Refinement
    print("Phase 2: Adaptive refinement based on identified abstractions...")
    additional_files = phase2_adaptive_refinement(owner, repo, token, abstractions, initial_files.keys())

    # Combine results
    all_files = {**initial_files, **additional_files}
    print(f"Total files selected: {len(all_files)}")

    return all_files
```

## Advantages of the Two-Phase Approach

1. **Efficiency**: Significantly reduces the number of API requests by:
   - Getting the complete file structure in one request
   - Downloading only selected files
   - Using a two-phase approach to minimize unnecessary downloads

2. **Intelligence**: Uses LLM to make informed decisions about:
   - Which files are likely to be important for understanding the codebase
   - Which additional files are needed to explain identified abstractions

3. **Adaptability**: Tailors the file selection to:
   - The specific repository structure
   - The primary language of the codebase
   - The abstractions identified in the first phase

4. **Robustness**: Includes fallback mechanisms:
   - Rules-based selection as a foundation
   - Fallback parsing if LLM doesn't return valid JSON
   - Size limits to avoid large files

5. **Scalability**: Handles large repositories better by:
   - Limiting the number of files downloaded
   - Focusing on the most relevant files
   - Using a more efficient API approach

## Implementation Steps

To implement this approach in the existing pipeline:

1. Replace the current `crawl_github_files` function with the new two-phase implementation
2. Update the `FetchRepo` node in the pipeline to use the new approach
3. Ensure proper error handling and fallbacks
4. Add caching for repository structure to avoid redundant API calls

## Conclusion

The proposed two-phase approach represents a significant improvement over the current implementation. By combining rules-based filtering with LLM-guided selection, it achieves a more intelligent and efficient way to crawl GitHub repositories. This approach is particularly well-suited for tutorial generation, as it focuses on the files that are most relevant for understanding the key abstractions in the codebase.
