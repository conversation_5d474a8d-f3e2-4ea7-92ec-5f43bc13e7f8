from sqlalchemy import Column, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Table, DateTime, Text, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..database import Base

# Association table for UserProfile and Organization
user_organization = Table(
    "user_organization",
    Base.metadata,
    Column("user_profile_id", Integer, ForeignKey("user_profiles.id"), primary_key=True),
    Column("organization_id", Integer, ForeignKey("organizations.id"), primary_key=True)
)

class User(Base):
    """User model."""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    first_name = Column(String)
    last_name = Column(String)
    is_active = Column(Boolean, default=True)
    
    # Relationships
    profile = relationship("UserProfile", back_populates="user", uselist=False)

class Organization(Base):
    """Organization model."""
    __tablename__ = "organizations"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    github_id = Column(String, unique=True, index=True)
    github_url = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    members = relationship("UserProfile", secondary=user_organization, back_populates="organizations")
    repositories = relationship("Repository", back_populates="organization")
    settings = relationship("Setting", back_populates="organization")

class UserProfile(Base):
    """User profile model."""
    __tablename__ = "user_profiles"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True)
    github_id = Column(String, unique=True, index=True)
    github_access_token = Column(String)
    github_username = Column(String, index=True)
    github_avatar_url = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="profile")
    organizations = relationship("Organization", secondary=user_organization, back_populates="members")
    owned_repositories = relationship("Repository", back_populates="owner", foreign_keys="Repository.owner_id")
    settings = relationship("Setting", back_populates="user")
    user_accesses = relationship("UserRepositoryAccess", back_populates="user")

class Repository(Base):
    """Repository model."""
    __tablename__ = "repositories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    full_name = Column(String, unique=True, index=True)
    github_id = Column(String, unique=True, index=True)
    github_url = Column(String)
    description = Column(Text, nullable=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"), nullable=True)
    owner_id = Column(Integer, ForeignKey("user_profiles.id"), nullable=True)
    is_private = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    organization = relationship("Organization", back_populates="repositories")
    owner = relationship("UserProfile", back_populates="owned_repositories", foreign_keys=[owner_id])
    settings = relationship("Setting", back_populates="repository")
    user_accesses = relationship("UserRepositoryAccess", back_populates="repository")

class UserRepositoryAccess(Base):
    """User repository access model."""
    __tablename__ = "user_repository_accesses"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("user_profiles.id"))
    repository_id = Column(Integer, ForeignKey("repositories.id"))
    access_level = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("UserProfile", back_populates="user_accesses")
    repository = relationship("Repository", back_populates="user_accesses")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint("user_id", "repository_id", name="uix_user_repository"),
    )

class Setting(Base):
    """Setting model."""
    __tablename__ = "settings"
    
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String, index=True)
    value = Column(String)
    scope = Column(String)
    user_id = Column(Integer, ForeignKey("user_profiles.id"), nullable=True)
    repository_id = Column(Integer, ForeignKey("repositories.id"), nullable=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("UserProfile", back_populates="settings")
    repository = relationship("Repository", back_populates="settings")
    organization = relationship("Organization", back_populates="settings")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint("key", "scope", "user_id", "repository_id", "organization_id", name="uix_setting"),
    )
