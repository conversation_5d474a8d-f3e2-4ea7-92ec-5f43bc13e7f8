import { useUser } from '@clerk/nextjs'
import { useEffect, useState } from 'react'
import { supabase, UserProfile } from '@/lib/supabase'

export const useUserProfile = () => {
  const { user, isLoaded } = useUser()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const syncUserProfile = async () => {
      if (!isLoaded || !user) {
        setLoading(false)
        return
      }

      try {
        // Check if user profile exists
        const { data: existingProfile, error: fetchError } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('clerk_user_id', user.id)
          .single()

        if (fetchError && fetchError.code !== 'PGRST116') {
          console.error('Error fetching user profile:', fetchError)
          setLoading(false)
          return
        }

        if (existingProfile) {
          setProfile(existingProfile)
        } else {
          // Create new user profile
          const newProfile = {
            clerk_user_id: user.id,
            github_username: user.externalAccounts?.find(
              account => account.provider === 'github'
            )?.username || null,
            github_avatar_url: user.imageUrl || null,
          }

          const { data: createdProfile, error: createError } = await supabase
            .from('user_profiles')
            .insert([newProfile])
            .select()
            .single()

          if (createError) {
            console.error('Error creating user profile:', createError)
          } else {
            setProfile(createdProfile)
          }
        }
      } catch (error) {
        console.error('Error syncing user profile:', error)
      } finally {
        setLoading(false)
      }
    }

    syncUserProfile()
  }, [user, isLoaded])

  return { profile, loading, user }
} 