from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
import logging

from .config import settings
from .database import engine, Base
from .api import core, github, perplexity, analytics, tutor, repository_analyzer, webhooks
from .auth.router import router as auth_router

# Configure basic logging if not already configured elsewhere (e.g. in core.py or github.py)
# This ensures logger is set up before first use by middleware
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create the FastAPI app
app = FastAPI(
    title="GitHub Enterprise + Perplexity API Integration",
    description="API for enhancing GitHub Enterprise with AI capabilities from Perplexity",
    version="1.0.0",
    docs_url=None,  # Disable default docs
    redoc_url=None,  # Disable default redoc
)

# THIS SHOULD BE THE VERY FIRST MIDDLEWARE
@app.middleware("http")
async def log_requests_middleware(request: Request, call_next):
    logger.info(f"--- REQUEST MIDDLEWARE: Path: {request.url.path}, Query: {request.url.query} ---")
    response = await call_next(request)
    logger.info(f"--- REQUEST MIDDLEWARE: Response status: {response.status_code} for Path: {request.url.path} ---")
    return response

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for debugging
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# Include routers
app.include_router(auth_router, prefix="/api/github", tags=["Authentication"])
app.include_router(core.router, prefix="/api/core", tags=["Core"])
app.include_router(github.router, prefix="/api/github", tags=["GitHub"])
app.include_router(perplexity.router, prefix="/api/perplexity", tags=["Perplexity"])
app.include_router(analytics.router, prefix="/api/analytics", tags=["Analytics"])
app.include_router(tutor.router, prefix="/api/tutor", tags=["Tutor"])
app.include_router(repository_analyzer.router, prefix="/api/analyzer", tags=["Repository Analyzer"])
app.include_router(webhooks.router, prefix="/api/webhooks", tags=["Webhooks"])

# Custom OpenAPI docs
@app.get("/api/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url="/api/openapi.json",
        title=app.title + " - Swagger UI",
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
    )

@app.get("/api/redoc", include_in_schema=False)
async def redoc_html():
    return get_swagger_ui_html(
        openapi_url="/api/openapi.json",
        title=app.title + " - ReDoc",
        redoc_js_url="https://cdn.jsdelivr.net/npm/redoc@next/bundles/redoc.standalone.js",
    )

@app.get("/api/openapi.json", include_in_schema=False)
async def get_open_api_endpoint():
    return get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

@app.get("/", tags=["Root"])
async def root():
    return {"message": "Welcome to the GitHub Enterprise + Perplexity API Integration"}

# Create tables in the database
@app.on_event("startup")
async def startup():
    # Create tables
    Base.metadata.create_all(bind=engine)
