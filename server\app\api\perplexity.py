from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
import hashlib
import json
import requests
import time

from ..database import get_db
from ..dependencies import get_current_user, get_current_user_profile
from ..auth.router import oauth2_scheme
from ..models.core import User, UserProfile, Repository
from ..models.perplexity import APIKeyConfig, PerplexityQuery, PerplexityRate, PerplexityCache
from ..config import settings
from . import schemas

router = APIRouter()

class PerplexityAPI:
    """Perplexity API client."""
    
    def __init__(self, api_key: str = None):
        """
        Initialize with an API key.
        
        Args:
            api_key: Perplexity API key
        """
        self.api_key = api_key or settings.PERPLEXITY_API_KEY
        
        if not self.api_key:
            raise ValueError("Perplexity API key is required. Please set PERPLEXITY_API_KEY environment variable.")
        
        self.base_url = "https://api.perplexity.ai"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def query(
        self, 
        prompt: str, 
        model: str = "sonar-pro", 
        use_cache: bool = True,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Send a query to the Perplexity API.
        
        Args:
            prompt: The prompt to send
            model: The model to use
            use_cache: Whether to use the cache
            context: Additional context for the query
            **kwargs: Additional parameters for the API
            
        Returns:
            API response
        """
        # Prepare request data
        data = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}]
        }
        
        # Add additional parameters
        for key, value in kwargs.items():
            data[key] = value
        
        # Generate cache key
        if use_cache:
            cache_key = self._generate_cache_key(data)
            cached_response = self._get_from_cache(cache_key, model)
            if cached_response:
                return cached_response
        
        # Send request
        start_time = time.time()
        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=data,
                timeout=30  # 30 second timeout
            )
        except requests.exceptions.Timeout:
            raise HTTPException(
                status_code=status.HTTP_504_GATEWAY_TIMEOUT,
                detail="Perplexity API request timed out"
            )
        except requests.exceptions.RequestException as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Perplexity API connection error: {str(e)}"
            )
        end_time = time.time()
        
        # Check for errors
        if response.status_code != 200:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Perplexity API error: {response.text}"
            )
        
        # Parse response
        response_data = response.json()
        
        # Add metadata
        response_data["_metadata"] = {
            "duration_ms": int((end_time - start_time) * 1000)
        }
        
        # Cache response
        if use_cache:
            self._cache_response(cache_key, model, data, response_data)
        
        return response_data
    
    def _generate_cache_key(self, data: Dict[str, Any]) -> str:
        """
        Generate a cache key for a request.
        
        Args:
            data: Request data
            
        Returns:
            Cache key
        """
        # Create a deterministic string representation of the data
        data_str = json.dumps(data, sort_keys=True)
        
        # Generate hash
        return hashlib.sha256(data_str.encode()).hexdigest()
    
    def _get_from_cache(self, cache_key: str, model: str) -> Optional[Dict[str, Any]]:
        """
        Get a response from the cache.
        
        Args:
            cache_key: Cache key
            model: Model name
            
        Returns:
            Cached response or None
        """
        # This would be implemented with a database query
        return None
    
    def _cache_response(
        self, 
        cache_key: str, 
        model: str, 
        request: Dict[str, Any], 
        response: Dict[str, Any]
    ) -> None:
        """
        Cache a response.
        
        Args:
            cache_key: Cache key
            model: Model name
            request: Request data
            response: Response data
        """
        # This would be implemented with a database insert
        pass

@router.get("/api-keys/", response_model=List[schemas.APIKeyConfig])
async def get_api_keys(
    skip: int = 0,
    limit: int = 100,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get API keys for the current user.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    api_keys = db.query(APIKeyConfig).filter(
        APIKeyConfig.user_id == current_user_profile.id
    ).offset(skip).limit(limit).all()
    
    return api_keys

@router.post("/search/", response_model=schemas.PerplexityResponse)
async def perplexity_search(
    search_data: schemas.PerplexitySearch,
    background_tasks: BackgroundTasks,
    current_user_profile: UserProfile = Depends(get_current_user_profile),
    db: Session = Depends(get_db)
):
    """
    Send a search request to the Perplexity API.
    """
    # Get repository if specified
    repository = None
    if search_data.repository_id:
        repository = db.query(Repository).filter(Repository.id == search_data.repository_id).first()
        if not repository:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Repository not found"
            )
    
    # Prepare API parameters
    api_params = {}
    if search_data.temperature is not None:
        api_params["temperature"] = search_data.temperature
    if search_data.max_tokens is not None:
        api_params["max_tokens"] = search_data.max_tokens
    
    # Initialize API client
    api_client = PerplexityAPI()
    
    try:
        # Send query
        response = api_client.query(
            prompt=search_data.query,
            model=search_data.model,
            use_cache=search_data.use_cache,
            context=None,
            **api_params
        )
        
        # Extract the response text and metadata
        response_text = response.get("choices", [{}])[0].get("message", {}).get("content", "")
        tokens_used = response.get("usage", {}).get("total_tokens", 0)
        duration_ms = response.get("_metadata", {}).get("duration_ms", 0)
        
        # Create a record in the database
        query = PerplexityQuery(
            user_id=current_user_profile.id,
            repository_id=repository.id if repository else None,
            model=search_data.model,
            prompt=search_data.query,
            context=None,
            response=response_text,
            tokens_used=tokens_used,
            duration_ms=duration_ms
        )
        db.add(query)
        db.commit()
        
        # Return response
        return {
            "response": response_text,
            "tokens_used": tokens_used,
            "duration_ms": duration_ms,
            "model": search_data.model
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing search: {str(e)}"
        )

@router.post("/chat/", response_model=schemas.PerplexityResponse)
async def perplexity_chat(
    chat_data: schemas.PerplexityChat,
    background_tasks: BackgroundTasks,
    current_user_profile: UserProfile = Depends(get_current_user_profile),
    db: Session = Depends(get_db)
):
    """
    Send a chat request to the Perplexity API.
    """
    # Get repository if specified
    repository = None
    if chat_data.repository_id:
        repository = db.query(Repository).filter(Repository.id == chat_data.repository_id).first()
        if not repository:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Repository not found"
            )
    
    # Prepare API parameters
    api_params = {}
    if chat_data.temperature is not None:
        api_params["temperature"] = chat_data.temperature
    if chat_data.max_tokens is not None:
        api_params["max_tokens"] = chat_data.max_tokens
    
    # Initialize API client
    api_client = PerplexityAPI()
    
    try:
        # Extract the last user message as the prompt
        prompt = ""
        for message in chat_data.messages:
            if message.get("role") == "user":
                prompt = message.get("content", "")
        
        # Send query
        response = api_client.query(
            prompt=prompt,
            model=chat_data.model,
            use_cache=chat_data.use_cache,
            context=None,
            messages=chat_data.messages,
            **api_params
        )
        
        # Extract the response text and metadata
        response_text = response.get("choices", [{}])[0].get("message", {}).get("content", "")
        tokens_used = response.get("usage", {}).get("total_tokens", 0)
        duration_ms = response.get("_metadata", {}).get("duration_ms", 0)
        
        # Create a record in the database
        query = PerplexityQuery(
            user_id=current_user_profile.id,
            repository_id=repository.id if repository else None,
            model=chat_data.model,
            prompt=prompt,
            context=chat_data.messages,
            response=response_text,
            tokens_used=tokens_used,
            duration_ms=duration_ms
        )
        db.add(query)
        db.commit()
        
        # Return response
        return {
            "response": response_text,
            "tokens_used": tokens_used,
            "duration_ms": duration_ms,
            "model": chat_data.model
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing chat: {str(e)}"
        )
