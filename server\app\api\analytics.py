from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import func
from datetime import date, timedelta

from ..database import get_db
from ..dependencies import get_current_user, get_current_user_profile
from ..auth.router import oauth2_scheme
from ..models.core import User, UserProfile
from ..models.perplexity import PerplexityQuery
from ..models.analytics import QueryLog, UserActivity, UsageMetric
from . import schemas

router = APIRouter()

@router.get("/query-logs/", response_model=List[schemas.QueryLog])
async def get_query_logs(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    user_id: Optional[int] = None,
    skip: int = 0,
    limit: int = 100,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get query logs.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    query = db.query(QueryLog)
    
    # Filter by date range
    if start_date:
        query = query.filter(func.date(QueryLog.created_at) >= start_date)
    if end_date:
        query = query.filter(func.date(QueryLog.created_at) <= end_date)
    
    # Filter by user
    if user_id:
        query = query.filter(QueryLog.user_id == user_id)
    
    # Apply pagination
    logs = query.offset(skip).limit(limit).all()
    
    return logs

@router.get("/user-activities/", response_model=List[schemas.UserActivity])
async def get_user_activities(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    user_id: Optional[int] = None,
    action: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get user activities.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    query = db.query(UserActivity)
    
    # Filter by date range
    if start_date:
        query = query.filter(func.date(UserActivity.created_at) >= start_date)
    if end_date:
        query = query.filter(func.date(UserActivity.created_at) <= end_date)
    
    # Filter by user
    if user_id:
        query = query.filter(UserActivity.user_id == user_id)
    
    # Filter by action
    if action:
        query = query.filter(UserActivity.action == action)
    
    # Apply pagination
    activities = query.offset(skip).limit(limit).all()
    
    return activities

@router.get("/usage-metrics/", response_model=List[schemas.UsageMetric])
async def get_usage_metrics(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    metric: Optional[str] = None,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get usage metrics.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    query = db.query(UsageMetric)
    
    # Filter by date range
    if start_date:
        query = query.filter(UsageMetric.date >= start_date)
    if end_date:
        query = query.filter(UsageMetric.date <= end_date)
    
    # Filter by metric
    if metric:
        query = query.filter(UsageMetric.metric == metric)
    
    # Get all metrics
    metrics = query.all()
    
    return metrics

@router.get("/summary/", response_model=Dict[str, Any])
async def get_analytics_summary(
    days: int = 30,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    Get analytics summary.
    """
    current_user = await get_current_user(token, db)
    current_user_profile = await get_current_user_profile(current_user, db)
    
    # Calculate date range
    end_date = date.today()
    start_date = end_date - timedelta(days=days)
    
    # Get query count
    query_count = db.query(func.count(PerplexityQuery.id)).filter(
        func.date(PerplexityQuery.created_at) >= start_date,
        func.date(PerplexityQuery.created_at) <= end_date
    ).scalar()
    
    # Get active users
    active_users = db.query(func.count(func.distinct(PerplexityQuery.user_id))).filter(
        func.date(PerplexityQuery.created_at) >= start_date,
        func.date(PerplexityQuery.created_at) <= end_date
    ).scalar()
    
    # Get total tokens used
    total_tokens = db.query(func.sum(PerplexityQuery.tokens_used)).filter(
        func.date(PerplexityQuery.created_at) >= start_date,
        func.date(PerplexityQuery.created_at) <= end_date
    ).scalar() or 0
    
    # Get average response time
    avg_duration = db.query(func.avg(PerplexityQuery.duration_ms)).filter(
        func.date(PerplexityQuery.created_at) >= start_date,
        func.date(PerplexityQuery.created_at) <= end_date
    ).scalar() or 0
    
    # Return summary
    return {
        "query_count": query_count,
        "active_users": active_users,
        "total_tokens": total_tokens,
        "avg_duration_ms": round(avg_duration, 2),
        "start_date": start_date,
        "end_date": end_date,
        "days": days
    }
