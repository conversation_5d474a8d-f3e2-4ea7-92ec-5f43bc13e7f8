from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey, DateTime, Text, JSON, UniqueConstraint, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..database import Base

class RepositoryAnalysis(Base):
    """Repository analysis model for storing comprehensive analysis results."""
    __tablename__ = "repository_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    repository_id = Column(Integer, ForeignKey("repositories.id"))
    analysis_type = Column(String)  # 'dependency', 'technology_trend', 'security', 'full'
    status = Column(String, default="pending")  # 'pending', 'in_progress', 'completed', 'failed'
    
    # Analysis results
    dependencies_count = Column(Integer, default=0)
    security_vulnerabilities_count = Column(Integer, default=0)
    technology_score = Column(Float, default=0.0)  # Overall technology relevance score
    
    # Metadata
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Raw results storage
    analysis_data = Column(JSON, nullable=True)  # Store full analysis results
    
    # Relationships
    repository = relationship("Repository", backref="analyses")
    dependencies = relationship("DependencyAnalysis", back_populates="analysis", cascade="all, delete-orphan")
    tech_trends = relationship("TechnologyTrend", back_populates="analysis", cascade="all, delete-orphan")
    security_findings = relationship("SecurityFinding", back_populates="analysis", cascade="all, delete-orphan")

class DependencyAnalysis(Base):
    """Dependency analysis results for each package/library."""
    __tablename__ = "dependency_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("repository_analyses.id"))
    
    # Dependency information
    package_name = Column(String, index=True)
    current_version = Column(String, nullable=True)
    latest_version = Column(String, nullable=True)
    package_manager = Column(String)  # 'npm', 'pip', 'maven', 'composer', etc.
    
    # Analysis results
    is_outdated = Column(Boolean, default=False)
    versions_behind = Column(Integer, default=0)
    has_vulnerabilities = Column(Boolean, default=False)
    vulnerability_count = Column(Integer, default=0)
    
    # Perplexity Sonar findings
    sonar_analysis = Column(JSON, nullable=True)  # Store Sonar API response
    alternative_packages = Column(JSON, nullable=True)  # Suggested alternatives
    recent_discussions = Column(JSON, nullable=True)  # Links to recent discussions
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    analysis = relationship("RepositoryAnalysis", back_populates="dependencies")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint("analysis_id", "package_name", name="uix_analysis_package"),
    )

class TechnologyTrend(Base):
    """Technology trend analysis for technologies used in the repository."""
    __tablename__ = "technology_trends"
    
    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("repository_analyses.id"))
    
    # Technology information
    technology_name = Column(String, index=True)
    technology_type = Column(String)  # 'framework', 'library', 'language', 'tool'
    usage_percentage = Column(Float, default=0.0)  # How much this tech is used in the repo
    
    # Trend analysis
    trend_direction = Column(String)  # 'rising', 'stable', 'declining'
    trend_score = Column(Float, default=0.0)  # -1 to 1 scale
    popularity_rank = Column(Integer, nullable=True)
    
    # Perplexity Sonar findings
    recent_articles = Column(JSON, nullable=True)  # Links to recent articles
    comparisons = Column(JSON, nullable=True)  # Comparison articles
    migration_guides = Column(JSON, nullable=True)  # Migration/upgrade guides
    community_sentiment = Column(JSON, nullable=True)  # Community discussions
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    analysis = relationship("RepositoryAnalysis", back_populates="tech_trends")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint("analysis_id", "technology_name", name="uix_analysis_technology"),
    )

class SecurityFinding(Base):
    """Security vulnerabilities and findings."""
    __tablename__ = "security_findings"
    
    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("repository_analyses.id"))
    
    # Vulnerability information
    cve_id = Column(String, nullable=True, index=True)
    severity = Column(String)  # 'low', 'medium', 'high', 'critical'
    package_name = Column(String)
    affected_versions = Column(String, nullable=True)
    
    # Details
    title = Column(String)
    description = Column(Text)
    solution = Column(Text, nullable=True)
    
    # Perplexity Sonar findings
    related_discussions = Column(JSON, nullable=True)  # Community discussions about this vuln
    fix_examples = Column(JSON, nullable=True)  # Examples of how others fixed it
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    analysis = relationship("RepositoryAnalysis", back_populates="security_findings")

class IssueAugmentation(Base):
    """Issue augmentation with Perplexity Sonar insights."""
    __tablename__ = "issue_augmentations"
    
    id = Column(Integer, primary_key=True, index=True)
    repository_id = Column(Integer, ForeignKey("repositories.id"))
    issue_number = Column(Integer)
    github_issue_id = Column(Integer, nullable=True, index=True)
    
    # Issue information
    issue_title = Column(String)
    issue_body = Column(Text, nullable=True)
    issue_labels = Column(JSON, nullable=True)
    
    # Analysis status
    analysis_status = Column(String, default="pending")  # 'pending', 'completed', 'failed'
    
    # Perplexity Sonar findings
    similar_issues = Column(JSON, nullable=True)  # Links to similar issues in other repos
    potential_solutions = Column(JSON, nullable=True)  # Suggested solutions from web
    relevant_documentation = Column(JSON, nullable=True)  # Related docs and articles
    stack_overflow_links = Column(JSON, nullable=True)  # Related SO questions
    
    # AI-generated content
    suggested_comment = Column(Text, nullable=True)  # Auto-generated helpful comment
    comment_posted = Column(Boolean, default=False)
    github_comment_id = Column(Integer, nullable=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    repository = relationship("Repository", backref="issue_augmentations")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint("repository_id", "issue_number", name="uix_repo_issue"),
    ) 