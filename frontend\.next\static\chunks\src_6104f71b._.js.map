{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/perplexity/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/perplexity/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/perplexity/frontend/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/perplexity/frontend/src/components/NavigationBar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';\r\nimport {\r\n  Github,\r\n  Search,\r\n  MessageSquare,\r\n  FolderGit2,\r\n  Home,\r\n  Menu,\r\n  User,\r\n  Settings,\r\n  LogOut\r\n} from 'lucide-react';\r\nimport { useUser, UserButton, SignInButton } from '@clerk/nextjs';\r\n\r\ninterface NavigationBarProps {\r\n  className?: string;\r\n}\r\n\r\nconst NavigationBar = ({ className }: NavigationBarProps) => {\r\n  const router = useRouter();\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\r\n  const { user, isLoaded } = useUser();\r\n\r\n  const navItems = [\r\n    { text: 'Home', path: '/', icon: Home },\r\n    { text: 'Repositories', path: '/repositories', icon: FolderGit2 },\r\n    { text: 'Code Search', path: '/search', icon: Search },\r\n    { text: 'AI Chat', path: '/chat', icon: MessageSquare },\r\n  ];\r\n\r\n  const NavItems = ({ mobile = false, onItemClick }: { mobile?: boolean; onItemClick?: () => void }) => (\r\n    <>\r\n      {navItems.map((item) => {\r\n        const IconComponent = item.icon;\r\n        return (\r\n          <Link\r\n            key={item.text}\r\n            href={item.path}\r\n            onClick={onItemClick}\r\n            className={`\r\n              flex items-center gap-2 transition-colors hover:text-primary\r\n              ${mobile\r\n                ? 'flex-col py-4 px-2 text-sm text-center'\r\n                : 'px-3 py-2 text-sm font-medium'\r\n              }\r\n            `}\r\n          >\r\n            <IconComponent className={mobile ? 'h-5 w-5' : 'h-4 w-4'} />\r\n            {item.text}\r\n          </Link>\r\n        );\r\n      })}\r\n    </>\r\n  );\r\n\r\n  return (\r\n    <nav className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\r\n      <div className=\"container flex h-16 items-center justify-between\">\r\n        {/* Logo */}\r\n        <Link href=\"/\" className=\"flex items-center space-x-2\">\r\n          <div className=\"flex h-8 w-8 items-center justify-center rounded-lg bg-primary\">\r\n            <Github className=\"h-5 w-5 text-primary-foreground\" />\r\n          </div>\r\n          <span className=\"hidden font-bold sm:inline-block\">\r\n            GitHub + Perplexity\r\n          </span>\r\n        </Link>\r\n\r\n        {/* Desktop Navigation */}\r\n        <div className=\"hidden md:flex md:items-center md:space-x-1\">\r\n          <NavItems />\r\n        </div>\r\n\r\n        {/* User Section */}\r\n        <div className=\"flex items-center space-x-2\">\r\n          {isLoaded ? (\r\n            user ? (\r\n              <UserButton afterSignOutUrl=\"/sign-in\" />\r\n            ) : (\r\n              <SignInButton mode=\"modal\">\r\n                <Button>Sign In</Button>\r\n              </SignInButton>\r\n            )\r\n          ) : (\r\n            <Button disabled>Loading...</Button>\r\n          )}\r\n\r\n          {/* Mobile Menu */}\r\n          <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>\r\n            <SheetTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden\"\r\n              >\r\n                <Menu className=\"h-6 w-6\" />\r\n                <span className=\"sr-only\">Toggle Menu</span>\r\n              </Button>\r\n            </SheetTrigger>\r\n            <SheetContent side=\"left\" className=\"pr-0\">\r\n              <Link\r\n                href=\"/\"\r\n                className=\"flex items-center space-x-2\"\r\n                onClick={() => setMobileMenuOpen(false)}\r\n              >\r\n                <div className=\"flex h-8 w-8 items-center justify-center rounded-lg bg-primary\">\r\n                  <Github className=\"h-5 w-5 text-primary-foreground\" />\r\n                </div>\r\n                <span className=\"font-bold\">GitHub + Perplexity</span>\r\n              </Link>\r\n              <div className=\"my-4 h-[calc(100vh-8rem)] pb-10 pl-6\">\r\n                <div className=\"flex flex-col space-y-2\">\r\n                  <NavItems mobile onItemClick={() => setMobileMenuOpen(false)} />\r\n                </div>\r\n              </div>\r\n            </SheetContent>\r\n          </Sheet>\r\n        </div>\r\n      </div>\r\n    </nav>\r\n  );\r\n};\r\n\r\nexport default NavigationBar;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAWA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAAA;;;AA5BA;;;;;;;;AAkCA,MAAM,gBAAgB,CAAC,EAAE,SAAS,EAAsB;;IACtD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;IAEjC,MAAM,WAAW;QACf;YAAE,MAAM;YAAQ,MAAM;YAAK,MAAM,sMAAA,CAAA,OAAI;QAAC;QACtC;YAAE,MAAM;YAAgB,MAAM;YAAiB,MAAM,yNAAA,CAAA,aAAU;QAAC;QAChE;YAAE,MAAM;YAAe,MAAM;YAAW,MAAM,yMAAA,CAAA,SAAM;QAAC;QACrD;YAAE,MAAM;YAAW,MAAM;YAAS,MAAM,2NAAA,CAAA,gBAAa;QAAC;KACvD;IAED,MAAM,WAAW,CAAC,EAAE,SAAS,KAAK,EAAE,WAAW,EAAkD,iBAC/F;sBACG,SAAS,GAAG,CAAC,CAAC;gBACb,MAAM,gBAAgB,KAAK,IAAI;gBAC/B,qBACE,6LAAC,+JAAA,CAAA,UAAI;oBAEH,MAAM,KAAK,IAAI;oBACf,SAAS;oBACT,WAAW,CAAC;;cAEV,EAAE,SACE,2CACA,gCACH;YACH,CAAC;;sCAED,6LAAC;4BAAc,WAAW,SAAS,YAAY;;;;;;wBAC9C,KAAK,IAAI;;mBAZL,KAAK,IAAI;;;;;YAepB;;IAIJ,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAI,WAAU;;sCACvB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,6LAAC;4BAAK,WAAU;sCAAmC;;;;;;;;;;;;8BAMrD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;;;;;;;;;;8BAIH,6LAAC;oBAAI,WAAU;;wBACZ,WACC,qBACE,6LAAC,8KAAA,CAAA,aAAU;4BAAC,iBAAgB;;;;;iDAE5B,6LAAC,8KAAA,CAAA,eAAY;4BAAC,MAAK;sCACjB,cAAA,6LAAC,qIAAA,CAAA,SAAM;0CAAC;;;;;;;;;;iDAIZ,6LAAC,qIAAA,CAAA,SAAM;4BAAC,QAAQ;sCAAC;;;;;;sCAInB,6LAAC,oIAAA,CAAA,QAAK;4BAAC,MAAM;4BAAgB,cAAc;;8CACzC,6LAAC,oIAAA,CAAA,eAAY;oCAAC,OAAO;8CACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;8CAG9B,6LAAC,oIAAA,CAAA,eAAY;oCAAC,MAAK;oCAAO,WAAU;;sDAClC,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;;8DAEjC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,6LAAC;oDAAK,WAAU;8DAAY;;;;;;;;;;;;sDAE9B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAS,MAAM;oDAAC,aAAa,IAAM,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxE;GAtGM;;QACW,qIAAA,CAAA,YAAS;QAEG,+JAAA,CAAA,UAAO;;;KAH9B;uCAwGS", "debugId": null}}, {"offset": {"line": 589, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/perplexity/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 621, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/perplexity/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 836, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/perplexity/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useUser } from '@clerk/nextjs';\nimport NavigationBar from '@/components/NavigationBar';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { ArrowRight, Loader2 } from 'lucide-react';\n\nexport default function Home() {\n  const { user, isLoaded } = useUser();\n  const router = useRouter();\n  const [repositoryUrl, setRepositoryUrl] = useState('');\n  const [repositoryType, setRepositoryType] = useState('public');\n  const [githubToken, setGithubToken] = useState('');\n  const [isGenerating, setIsGenerating] = useState(false);\n\n  console.log(\"Auth state:\", { user, isLoaded });\n\n  useEffect(() => {\n    // Redirect to login if not authenticated\n    if (isLoaded && !user) {\n      console.log(\"Not authenticated, redirecting to sign-in\");\n      router.push('/sign-in');\n    }\n  }, [user, isLoaded, router]);\n\n  const handleGenerateTutorial = async () => {\n    if (!repositoryUrl.trim()) {\n      alert('Please enter a repository URL');\n      return;\n    }\n\n    if (repositoryType === 'private' && !githubToken.trim()) {\n      alert('Please enter a GitHub token for private repositories');\n      return;\n    }\n\n    // Validate GitHub URL format\n    if (!repositoryUrl.match(/^https:\\/\\/github\\.com\\/[\\w\\-\\.]+\\/[\\w\\-\\.]+/)) {\n      alert('Please enter a valid GitHub repository URL (e.g., https://github.com/owner/repo)');\n      return;\n    }\n\n    setIsGenerating(true);\n\n    try {\n      // Get the session token from Clerk\n      const token = await user?.getToken?.();\n\n      if (!token) {\n        alert('Authentication error. Please try signing in again.');\n        return;\n      }\n\n      // Call the backend API\n      const response = await fetch('http://localhost:8000/api/tutor/generate-tutorial-from-url/', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({\n          repository_url: repositoryUrl,\n          github_token: repositoryType === 'private' ? githubToken : null,\n          include_patterns: ['*.py', '*.js', '*.ts', '*.jsx', '*.tsx', '*.java', '*.cpp', '*.c', '*.h'],\n          exclude_patterns: ['node_modules/*', '*.min.js', '*.bundle.js', 'dist/*', 'build/*'],\n          language: 'english',\n          max_abstractions: 10,\n          use_cache: true\n        })\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || 'Failed to generate tutorial');\n      }\n\n      const result = await response.json();\n\n      // Redirect to tutorial status page or show success message\n      alert(`Tutorial generation started! Tutorial ID: ${result.github_action_id}. You can check the status in your tutorials.`);\n\n      // Optionally redirect to a status page\n      // router.push(`/tutorials/${result.github_action_id}`);\n\n    } catch (error) {\n      console.error('Error generating tutorial:', error);\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n      alert(`Failed to generate tutorial: ${errorMessage}`);\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  if (!isLoaded) {\n    return (\n      <div className=\"flex h-screen items-center justify-center\">\n        <div className=\"flex items-center space-x-2\">\n          <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\n          <p className=\"text-lg text-muted-foreground\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return (\n      <div className=\"flex h-screen items-center justify-center\">\n        <div className=\"flex items-center space-x-2\">\n          <Loader2 className=\"h-6 w-6 animate-spin text-muted-foreground\" />\n          <p className=\"text-muted-foreground\">Not authenticated. Redirecting to sign-in...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <NavigationBar />\n\n      <main className=\"container mx-auto px-4 py-16\">\n        {/* Main Content Area */}\n        <div className=\"mx-auto max-w-6xl\">\n          <div className=\"rounded-3xl border-2 border-muted p-8 md:p-12\">\n            <div className=\"flex items-start gap-8 lg:gap-16\">\n              {/* Left Side - Form */}\n              <div className=\"flex-1 space-y-6\">\n                <div className=\"flex items-center gap-2 rounded-xl border-2 border-muted bg-muted/30 p-2\">\n                  {/* Repository Type Selector */}\n                  <div className=\"flex-shrink-0\">\n                    <Select value={repositoryType} onValueChange={setRepositoryType}>\n                      <SelectTrigger className=\"h-10 w-24 border-0 bg-transparent text-sm font-medium\">\n                        <SelectValue />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"public\">PUBLIC</SelectItem>\n                        <SelectItem value=\"private\">PRIVATE</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  {/* URL Input */}\n                  <Input\n                    type=\"text\"\n                    placeholder=\"Enter URL Here\"\n                    value={repositoryUrl}\n                    onChange={(e) => setRepositoryUrl(e.target.value)}\n                    className=\"h-10 flex-1 border-0 bg-transparent text-lg placeholder:text-muted-foreground/60 focus-visible:ring-0 focus-visible:ring-offset-0\"\n                  />\n\n                  {/* Submit Button */}\n                  <Button\n                    onClick={handleGenerateTutorial}\n                    disabled={isGenerating}\n                    className=\"h-10 w-10 flex-shrink-0 rounded-lg p-0\"\n                  >\n                    {isGenerating ? (\n                      <Loader2 className=\"h-4 w-4 animate-spin\" />\n                    ) : (\n                      <ArrowRight className=\"h-4 w-4\" />\n                    )}\n                  </Button>\n                </div>\n\n                {/* GitHub Token Input for Private Repos */}\n                {repositoryType === 'private' && (\n                  <div className=\"space-y-2\">\n                    <label className=\"text-sm font-medium text-muted-foreground\">\n                      GitHub Personal Access Token (required for private repositories)\n                    </label>\n                    <Input\n                      type=\"password\"\n                      placeholder=\"ghp_xxxxxxxxxxxxxxxxxxxx\"\n                      value={githubToken}\n                      onChange={(e) => setGithubToken(e.target.value)}\n                      className=\"h-12 rounded-xl border-2 border-muted bg-muted/30\"\n                    />\n                  </div>\n                )}\n              </div>\n\n              {/* Right Side - Explanatory Text */}\n              <div className=\"hidden lg:block lg:w-80\">\n                <div className=\"space-y-2\">\n                  <p className=\"text-sm text-muted-foreground leading-relaxed\">\n                    This Page will just fetch the repository and generate an AI-powered tutorial to help you understand the codebase structure and functionality.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Instructions for Mobile */}\n          <div className=\"mt-8 lg:hidden\">\n            <div className=\"rounded-xl border bg-muted/30 p-6\">\n              <h3 className=\"mb-3 font-semibold\">How it works</h3>\n              <p className=\"text-sm text-muted-foreground leading-relaxed\">\n                This page will fetch the repository and generate an AI-powered tutorial to help you understand the codebase structure and functionality.\n              </p>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AATA;;;;;;;;;AAWe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,QAAQ,GAAG,CAAC,eAAe;QAAE;QAAM;IAAS;IAE5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,yCAAyC;YACzC,IAAI,YAAY,CAAC,MAAM;gBACrB,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC;YACd;QACF;yBAAG;QAAC;QAAM;QAAU;KAAO;IAE3B,MAAM,yBAAyB;QAC7B,IAAI,CAAC,cAAc,IAAI,IAAI;YACzB,MAAM;YACN;QACF;QAEA,IAAI,mBAAmB,aAAa,CAAC,YAAY,IAAI,IAAI;YACvD,MAAM;YACN;QACF;QAEA,6BAA6B;QAC7B,IAAI,CAAC,cAAc,KAAK,CAAC,iDAAiD;YACxE,MAAM;YACN;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,mCAAmC;YACnC,MAAM,QAAQ,MAAM,MAAM;YAE1B,IAAI,CAAC,OAAO;gBACV,MAAM;gBACN;YACF;YAEA,uBAAuB;YACvB,MAAM,WAAW,MAAM,MAAM,+DAA+D;gBAC1F,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,gBAAgB;oBAChB,cAAc,mBAAmB,YAAY,cAAc;oBAC3D,kBAAkB;wBAAC;wBAAQ;wBAAQ;wBAAQ;wBAAS;wBAAS;wBAAU;wBAAS;wBAAO;qBAAM;oBAC7F,kBAAkB;wBAAC;wBAAkB;wBAAY;wBAAe;wBAAU;qBAAU;oBACpF,UAAU;oBACV,kBAAkB;oBAClB,WAAW;gBACb;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;YACtC;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,2DAA2D;YAC3D,MAAM,CAAC,0CAA0C,EAAE,OAAO,gBAAgB,CAAC,6CAA6C,CAAC;QAEzH,uCAAuC;QACvC,wDAAwD;QAE1D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,MAAM,CAAC,6BAA6B,EAAE,cAAc;QACtD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;;;;;;IAIrD;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,sIAAA,CAAA,UAAa;;;;;0BAEd,6LAAC;gBAAK,WAAU;0BAEd,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,OAAO;4DAAgB,eAAe;;8EAC5C,6LAAC,qIAAA,CAAA,gBAAa;oEAAC,WAAU;8EACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;8EAEd,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAS;;;;;;sFAC3B,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAU;;;;;;;;;;;;;;;;;;;;;;;kEAMlC,6LAAC,oIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDAChD,WAAU;;;;;;kEAIZ,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,UAAU;wDACV,WAAU;kEAET,6BACC,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAEnB,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;4CAM3B,mBAAmB,2BAClB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAA4C;;;;;;kEAG7D,6LAAC,oIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wDAC9C,WAAU;;;;;;;;;;;;;;;;;;kDAOlB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASrE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,6LAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3E;GArMwB;;QACK,+JAAA,CAAA,UAAO;QACnB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}