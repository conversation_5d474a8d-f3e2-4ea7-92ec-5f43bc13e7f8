from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

from .database import get_db
from .models.core import User, UserProfile

# Import get_current_user from auth.router
# This will be imported at the end of this file
# to avoid circular imports

# Define a placeholder for get_current_user
# This will be replaced with the actual function later
get_current_user = None

async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get the current active user.
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user

async def get_current_user_profile(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> UserProfile:
    """
    Get the current user's profile.
    """
    profile = db.query(UserProfile).filter(UserProfile.user_id == current_user.id).first()
    if profile is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    return profile

# Import get_current_user from auth.router
# This must be at the end of the file to avoid circular imports
from .auth.router import get_current_user
