'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';

interface Repository {
  id: number;
  name: string;
  full_name: string;
  description?: string;
}

interface RepositoryAnalysis {
  id: number;
  repository_id: number;
  analysis_type: string;
  status: string;
  dependencies_count: number;
  security_vulnerabilities_count: number;
  technology_score: number;
  started_at: string;
  completed_at?: string;
  error_message?: string;
}

interface DependencyAnalysis {
  id: number;
  package_name: string;
  current_version?: string;
  latest_version?: string;
  package_manager: string;
  is_outdated: boolean;
  versions_behind: number;
  has_vulnerabilities: boolean;
  vulnerability_count: number;
  sonar_analysis?: any;
  alternative_packages?: any[];
  recent_discussions?: any[];
}

interface TechnologyTrend {
  id: number;
  technology_name: string;
  technology_type: string;
  usage_percentage: number;
  trend_direction: string;
  trend_score: number;
  popularity_rank?: number;
  recent_articles?: any[];
  comparisons?: any[];
  migration_guides?: any[];
  community_sentiment?: any;
}

interface SecurityFinding {
  id: number;
  cve_id?: string;
  severity: string;
  package_name: string;
  title: string;
  description: string;
  solution?: string;
  related_discussions?: any[];
  fix_examples?: any[];
}

interface AnalysisResponse {
  analysis: RepositoryAnalysis;
  dependencies?: DependencyAnalysis[];
  technologies?: TechnologyTrend[];
  security_findings?: SecurityFinding[];
}

export default function RepositoryAnalyzerPage() {
  const params = useParams();
  const repositoryId = params.id as string;

  const [repository, setRepository] = useState<Repository | null>(null);
  const [analysis, setAnalysis] = useState<AnalysisResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('dependencies');

  useEffect(() => {
    fetchRepository();
    fetchLatestAnalysis();
  }, [repositoryId]);

  const fetchRepository = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/core/repositories/${repositoryId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const repo = await response.json();
        setRepository(repo);
      }
    } catch (err) {
      console.error('Failed to fetch repository:', err);
    }
  };

  const fetchLatestAnalysis = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/analyzer/repositories/${repositoryId}/analyses?limit=1`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const analyses = await response.json();
        if (analyses.length > 0) {
          fetchAnalysisDetails(analyses[0].id);
        }
      }
    } catch (err) {
      console.error('Failed to fetch analyses:', err);
    }
  };

  const fetchAnalysisDetails = async (analysisId: number) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/analyzer/analyses/${analysisId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const analysisData = await response.json();
        setAnalysis(analysisData);
      }
    } catch (err) {
      console.error('Failed to fetch analysis details:', err);
    }
  };

  const startAnalysis = async (analysisType: string = 'full') => {
    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/analyzer/repositories/${repositoryId}/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          analysis_type: analysisType,
        }),
      });

      if (response.ok) {
        const analysisData = await response.json();
        setAnalysis(analysisData);
        
        // Poll for updates if analysis is in progress
        if (analysisData.analysis.status === 'in_progress' || analysisData.analysis.status === 'pending') {
          pollAnalysisStatus(analysisData.analysis.id);
        }
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Analysis failed');
      }
    } catch (err) {
      setError('Failed to start analysis');
      console.error('Analysis error:', err);
    } finally {
      setLoading(false);
    }
  };

  const pollAnalysisStatus = async (analysisId: number) => {
    const pollInterval = setInterval(async () => {
      try {
        const token = localStorage.getItem('auth_token');
        const response = await fetch(`/api/analyzer/analyses/${analysisId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const analysisData = await response.json();
          setAnalysis(analysisData);

          if (analysisData.analysis.status === 'completed' || analysisData.analysis.status === 'failed') {
            clearInterval(pollInterval);
          }
        }
      } catch (err) {
        console.error('Failed to poll analysis status:', err);
        clearInterval(pollInterval);
      }
    }, 3000);

    // Stop polling after 5 minutes
    setTimeout(() => clearInterval(pollInterval), 300000);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return '✅';
      case 'failed':
        return '❌';
      case 'in_progress':
        return '🔄';
      default:
        return '⏳';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'bg-red-500 text-white';
      case 'high':
        return 'bg-orange-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-black';
      case 'low':
        return 'bg-blue-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'rising':
        return '📈';
      case 'declining':
        return '📉';
      default:
        return '➡️';
    }
  };

  if (!repository) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Repository Analyzer</h1>
        <p className="text-gray-600 mb-4">
          Analyze {repository.full_name} with Perplexity Sonar insights
        </p>
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <strong>Error:</strong> {error}
          </div>
        )}

        <div className="flex gap-4 mb-6 flex-wrap">
          <button 
            onClick={() => startAnalysis('full')} 
            disabled={loading}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
          >
            {loading && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>}
            Start Full Analysis
          </button>
          <button 
            onClick={() => startAnalysis('dependency')}
            disabled={loading}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 disabled:opacity-50"
          >
            Dependencies Only
          </button>
          <button 
            onClick={() => startAnalysis('technology_trend')}
            disabled={loading}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 disabled:opacity-50"
          >
            Technology Trends
          </button>
          <button 
            onClick={() => startAnalysis('security')}
            disabled={loading}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 disabled:opacity-50"
          >
            Security Analysis
          </button>
        </div>
      </div>

      {analysis && (
        <div className="space-y-6">
          {/* Analysis Status */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <span className="text-2xl">{getStatusIcon(analysis.analysis.status)}</span>
              Analysis Status
            </h2>
            <p className="text-gray-600 mb-4">
              Started: {new Date(analysis.analysis.started_at).toLocaleString()}
              {analysis.analysis.completed_at && (
                <span className="ml-4">
                  Completed: {new Date(analysis.analysis.completed_at).toLocaleString()}
                </span>
              )}
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded">
                <div className="text-2xl font-bold text-blue-600">
                  {analysis.analysis.dependencies_count}
                </div>
                <div className="text-sm text-gray-600">Dependencies</div>
              </div>
              <div className="text-center p-4 bg-red-50 rounded">
                <div className="text-2xl font-bold text-red-600">
                  {analysis.analysis.security_vulnerabilities_count}
                </div>
                <div className="text-sm text-gray-600">Vulnerabilities</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded">
                <div className="text-2xl font-bold text-green-600">
                  {Math.round(analysis.analysis.technology_score * 100)}%
                </div>
                <div className="text-sm text-gray-600">Tech Score</div>
              </div>
            </div>
          </div>

          {/* Analysis Results Tabs */}
          <div className="bg-white rounded-lg shadow-md">
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6">
                {[
                  { id: 'dependencies', label: '📦 Dependencies', icon: '📦' },
                  { id: 'technologies', label: '🔧 Technologies', icon: '🔧' },
                  { id: 'security', label: '🛡️ Security', icon: '🛡️' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>

            <div className="p-6">
              {activeTab === 'dependencies' && (
                <div className="space-y-4">
                  {analysis.dependencies && analysis.dependencies.length > 0 ? (
                    analysis.dependencies.map((dep) => (
                      <div key={dep.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-lg font-semibold">{dep.package_name}</h3>
                          <div className="flex gap-2">
                            {dep.is_outdated && (
                              <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded">
                                Outdated
                              </span>
                            )}
                            {dep.has_vulnerabilities && (
                              <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded">
                                {dep.vulnerability_count} vulnerabilities
                              </span>
                            )}
                            <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">
                              {dep.package_manager}
                            </span>
                          </div>
                        </div>
                        <p className="text-gray-600 text-sm mb-3">
                          Current: {dep.current_version || 'Unknown'} | 
                          Latest: {dep.latest_version || 'Unknown'}
                          {dep.versions_behind > 0 && (
                            <span className="text-orange-600 ml-2">
                              ({dep.versions_behind} versions behind)
                            </span>
                          )}
                        </p>
                        {(dep.alternative_packages || dep.recent_discussions) && (
                          <div className="space-y-2">
                            {dep.alternative_packages && (
                              <div>
                                <h4 className="font-semibold text-sm">Alternatives:</h4>
                                <p className="text-sm text-gray-600">
                                  Perplexity Sonar found alternative packages and migration discussions
                                </p>
                              </div>
                            )}
                            {dep.recent_discussions && (
                              <div>
                                <h4 className="font-semibold text-sm">Recent Discussions:</h4>
                                <p className="text-sm text-gray-600">
                                  Community discussions about issues and updates
                                </p>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <div className="text-6xl mb-4">📦</div>
                      <p className="text-gray-600">No dependency analysis available</p>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'technologies' && (
                <div className="space-y-4">
                  {analysis.technologies && analysis.technologies.length > 0 ? (
                    analysis.technologies.map((tech) => (
                      <div key={tech.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-lg font-semibold">{tech.technology_name}</h3>
                          <div className="flex items-center gap-2">
                            <span className="text-xl">{getTrendIcon(tech.trend_direction)}</span>
                            <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">
                              {tech.technology_type}
                            </span>
                          </div>
                        </div>
                        <p className="text-gray-600 text-sm mb-3">
                          Usage: {Math.round(tech.usage_percentage)}% | 
                          Trend: {tech.trend_direction} | 
                          Score: {Math.round(tech.trend_score * 100)}%
                        </p>
                        <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${tech.usage_percentage}%` }}
                          ></div>
                        </div>
                        {(tech.recent_articles || tech.migration_guides) && (
                          <div className="space-y-2">
                            {tech.recent_articles && (
                              <div className="text-sm">
                                <span className="font-semibold">Recent Articles:</span> 
                                <span className="text-gray-600 ml-2">
                                  Perplexity found recent trends and comparisons
                                </span>
                              </div>
                            )}
                            {tech.migration_guides && (
                              <div className="text-sm">
                                <span className="font-semibold">Migration Guides:</span>
                                <span className="text-gray-600 ml-2">
                                  Available migration and upgrade resources
                                </span>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <div className="text-6xl mb-4">🔧</div>
                      <p className="text-gray-600">No technology analysis available</p>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'security' && (
                <div className="space-y-4">
                  {analysis.security_findings && analysis.security_findings.length > 0 ? (
                    analysis.security_findings.map((finding) => (
                      <div key={finding.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-lg font-semibold">{finding.title}</h3>
                          <div className="flex items-center gap-2">
                            <span className={`text-xs px-2 py-1 rounded ${getSeverityColor(finding.severity)}`}>
                              {finding.severity}
                            </span>
                            {finding.cve_id && (
                              <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">
                                {finding.cve_id}
                              </span>
                            )}
                          </div>
                        </div>
                        <p className="text-gray-600 text-sm mb-3">
                          Package: {finding.package_name}
                        </p>
                        <p className="text-sm mb-4">{finding.description}</p>
                        {finding.solution && (
                          <div className="bg-green-50 p-3 rounded-md mb-4">
                            <h4 className="font-semibold text-green-800 mb-2">Solution:</h4>
                            <p className="text-sm text-green-700">{finding.solution}</p>
                          </div>
                        )}
                        {(finding.related_discussions || finding.fix_examples) && (
                          <div className="space-y-2">
                            {finding.related_discussions && (
                              <div className="text-sm">
                                <span className="font-semibold">Community Discussions:</span>
                                <span className="text-gray-600 ml-2">
                                  Found related security discussions
                                </span>
                              </div>
                            )}
                            {finding.fix_examples && (
                              <div className="text-sm">
                                <span className="font-semibold">Fix Examples:</span>
                                <span className="text-gray-600 ml-2">
                                  Examples of how others fixed this issue
                                </span>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <div className="text-6xl mb-4">🛡️</div>
                      <p className="text-gray-600">No security findings available</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 