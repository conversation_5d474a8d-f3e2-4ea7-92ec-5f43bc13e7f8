import os
import fnmatch
from typing import Dict, <PERSON>, Set, Union, Tuple, Any

def crawl_local_files(
    directory: str,
    include_patterns: Union[str, Set[str]] = None,
    exclude_patterns: Union[str, Set[str]] = None,
    max_file_size: int = 1 * 1024 * 1024,  # 1 MB
    use_relative_paths: bool = True
) -> Dict[str, Any]:
    """
    Crawl files from a local directory.

    Args:
        directory (str): Path to the local directory to crawl.
        include_patterns (str or set of str, optional): Pattern or set of patterns specifying which files to include
                                                      (e.g., "*.py", {"*.md", "*.txt"}).
                                                      If None, all files are included.
        exclude_patterns (str or set of str, optional): Pattern or set of patterns specifying which files to exclude.
                                                      If None, no files are excluded.
        max_file_size (int, optional): Maximum file size in bytes (default: 1 MB).
        use_relative_paths (bool, optional): If True, file paths will be relative to the specified directory.

    Returns:
        dict: Dictionary with files and statistics.
    """
    if not os.path.isdir(directory):
        raise ValueError(f"'{directory}' is not a valid directory")

    # Convert single pattern to set
    if include_patterns and isinstance(include_patterns, str):
        include_patterns = {include_patterns}
    if exclude_patterns and isinstance(exclude_patterns, str):
        exclude_patterns = {exclude_patterns}

    def should_include_file(file_path: str, file_name: str) -> bool:
        """Determine if a file should be included based on patterns"""
        # If no include patterns are specified, include all files
        if not include_patterns:
            include_file = True
        else:
            # Check if file matches any include pattern
            include_file = any(fnmatch.fnmatch(file_name, pattern) for pattern in include_patterns)

        # If exclude patterns are specified, check if file should be excluded
        if exclude_patterns and include_file:
            # Exclude if file matches any exclude pattern
            exclude_file = any(fnmatch.fnmatch(file_path, pattern) for pattern in exclude_patterns)
            return not exclude_file

        return include_file

    files = {}
    skipped_files = []

    # Walk through the directory
    for root, _, filenames in os.walk(directory):
        for filename in filenames:
            abs_path = os.path.join(root, filename)
            
            # Calculate relative path if requested
            if use_relative_paths:
                rel_path = os.path.relpath(abs_path, directory)
            else:
                rel_path = abs_path

            # Check file size
            try:
                file_size = os.path.getsize(abs_path)
            except OSError as e:
                print(f"Error accessing {rel_path}: {e}")
                continue

            if file_size > max_file_size:
                skipped_files.append((rel_path, file_size))
                print(f"Skipping {rel_path}: size {file_size} exceeds limit {max_file_size}")
                continue

            # Check include/exclude patterns
            if not should_include_file(rel_path, filename):
                print(f"Skipping {rel_path}: does not match include/exclude patterns")
                continue

            # Read content
            try:
                with open(abs_path, "r", encoding="utf-8-sig") as f:
                    content = f.read()
                files[rel_path] = content
                print(f"Added {rel_path} ({file_size} bytes)")
            except Exception as e:
                print(f"Failed to read {rel_path}: {e}")

    return {
        "files": files,
        "stats": {
            "downloaded_count": len(files),
            "skipped_count": len(skipped_files),
            "skipped_files": skipped_files,
            "base_path": directory if use_relative_paths else None,
            "include_patterns": include_patterns,
            "exclude_patterns": exclude_patterns,
            "source": "local_directory"
        }
    }