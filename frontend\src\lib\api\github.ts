import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Create axios instance with proper interceptors like the main API
const githubApiClient = axios.create({
  baseURL: `${API_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: false,
});

// Add request interceptor to include auth token
githubApiClient.interceptors.request.use(
  (config) => {
    // In client-side code, access localStorage
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('auth_token');
      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

const githubApi = {
  /**
   * Get repositories for the authenticated user
   */
  getRepositories: () => {
    return githubApiClient.get('/core/repositories/');
  },

  /**
   * Get a specific repository
   */
  getRepository: (id: string) => {
    return githubApiClient.get(`/core/repositories/${id}`);
  },

  /**
   * Get pull requests for a repository
   */
  getPullRequests: (repositoryId: string) => {
    return githubApiClient.get('/github/pull-requests/', {
      params: { repository_id: repositoryId }
    });
  },

  /**
   * Get a specific pull request
   */
  getPullRequest: (pullRequestId: string) => {
    return githubApiClient.get(`/github/pull-requests/${pullRequestId}/`);
  },

  /**
   * Get comments for a pull request
   */
  getPullRequestComments: (pullRequestId: string) => {
    return githubApiClient.get('/github/comments/', {
      params: { pull_request_id: pullRequestId }
    });
  }
};

export default githubApi;
