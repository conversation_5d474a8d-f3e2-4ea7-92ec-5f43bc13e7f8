from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OA<PERSON>2PasswordBearer
from sqlalchemy.orm import Session
from jose import JWTError, jwt
import requests
import logging

from ..database import get_db
from ..config import settings
from .github import G<PERSON>H<PERSON><PERSON><PERSON><PERSON><PERSON>, GitHubAuthError
from ..models.core import User, UserProfile
from .jwt import decode_access_token
from . import schemas

router = APIRouter()

# OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/github/auth/token")

logger = logging.getLogger(__name__)

async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> User:
    """
    Get the current user from the token.
    """
    logger.info(f"=== GET CURRENT USER: Starting with token: {token[:20]}... ===")

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        logger.info(f"=== GET CURRENT USER: Decoding token ===")
        # Decode the token
        payload = decode_access_token(token)
        logger.info(f"=== GET CURRENT USER: Token decoded successfully, payload: {payload} ===")

        username: str = payload.get("sub")
        if username is None:
            logger.error(f"=== GET CURRENT USER: No 'sub' field in token payload ===")
            raise credentials_exception

        logger.info(f"=== GET CURRENT USER: Username from token: {username} ===")
    except JWTError as jwt_err:
        logger.error(f"=== GET CURRENT USER: JWT decode error: {type(jwt_err).__name__} - {str(jwt_err)} ===")
        raise credentials_exception
    except Exception as e:
        logger.error(f"=== GET CURRENT USER: Unexpected decode error: {type(e).__name__} - {str(e)} ===")
        raise credentials_exception

    # Get the user from the database
    logger.info(f"=== GET CURRENT USER: Looking up user in database: {username} ===")
    user = db.query(User).filter(User.username == username).first()
    if user is None:
        logger.error(f"=== GET CURRENT USER: User not found in database: {username} ===")
        raise credentials_exception

    logger.info(f"=== GET CURRENT USER: User found: {user.username} (ID: {user.id}) ===")
    return user

@router.post("/auth/token/", response_model=schemas.Token)
async def login_with_github_token(
    token_data: schemas.TokenAuth,
    db: Session = Depends(get_db)
):
    """
    Login using a GitHub personal access token.
    """
    try:
        # Authenticate with the token
        auth = GitHubTokenAuth(token_data.token)

        # Validate the token
        validated_user_data = auth.validate_token()
        if not validated_user_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid GitHub token"
            )

        # Get or create the user
        user, profile, created, tokens = auth.get_or_create_user(db, user_data_from_validation=validated_user_data)

        # Return user info and JWT tokens
        return {
            "access_token": tokens["access"],
            "refresh_token": tokens["refresh"],
            "token_type": "bearer",
            "user": {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "github_username": profile.github_username,
                "github_avatar": profile.github_avatar_url,
                "created": created
            }
        }
    except GitHubAuthError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        import traceback
        print(f"Server error in login_with_github_token: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Server error: {str(e)}"
        )

# GitHub OAuth endpoints removed - using Clerk for authentication instead

@router.post("/auth/logout/", status_code=status.HTTP_200_OK)
async def logout():
    """
    Logout the current user.
    In a stateless JWT setup, logout is primarily handled client-side by deleting the token.
    This endpoint can be used to acknowledge the logout request.
    """
    # For a truly stateful logout, you might implement token blacklisting here.
    # For example, add the token JTI (JWT ID) to a blacklist in Redis or a database.
    return {"message": "Logout successful"}

# Token refresh endpoint removed - using Clerk for authentication instead

@router.get("/auth/user/", response_model=schemas.User)
async def get_user_details(current_user: User = Depends(get_current_user)):
    """
    Get the current user's details.
    """
    return current_user
