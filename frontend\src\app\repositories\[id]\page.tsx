'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { useUser } from '@clerk/nextjs';
import NavigationBar from '@/components/NavigationBar';
import MermaidInitializer from '@/components/MermaidInitializer';
import githubApi from '@/lib/api/github';
import { tutorApi } from '@/lib/api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  ArrowLeft,
  Search,
  Settings,
  FileText,
  Download,
  ExternalLink,
  Loader2,
  AlertCircle,
  CheckCircle,
  XCircle,
  Clock,
  Globe,
  Lock,
  <PERSON><PERSON><PERSON>,
  Brain
} from 'lucide-react';

interface Repository {
  id: number;
  name: string;
  full_name: string;
  description?: string;
  github_url: string;
  is_private: boolean;
  [key: string]: any;
}

interface TutorialStatus {
  github_action_id: number;
  status: string;
  result?: any;
  error?: string;
}

interface TutorialFile {
  path: string;
  content: string;
}

export default function RepositoryDetail({ params }: { params: Promise<{ id: string }> }) {
  const { user, isLoaded } = useUser();
  const [repository, setRepository] = useState<Repository | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tutorials, setTutorials] = useState<TutorialStatus[]>([]);
  const [selectedTutorial, setSelectedTutorial] = useState<TutorialStatus | null>(null);
  const [currentFile, setCurrentFile] = useState<TutorialFile | null>(null);

  // Tutorial generation states
  const [tutorialLoading, setTutorialLoading] = useState(false);
  const [includePatterns, setIncludePatterns] = useState('');
  const [excludePatterns, setExcludePatterns] = useState('node_modules,dist,build');
  const [language, setLanguage] = useState('english');
  const [maxAbstractions, setMaxAbstractions] = useState(5);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  useEffect(() => {
    const fetchRepositoryData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Await the params to get the repository ID
        const resolvedParams = await params;

        const response = await githubApi.getRepository(resolvedParams.id);
        setRepository(response.data);

        // Fetch existing tutorials
        try {
          const tutorialsResponse = await tutorApi.getRepositoryTutorials(response.data.id);
          setTutorials(tutorialsResponse.data);
        } catch (tutorialError) {
          console.error('Error fetching tutorials:', tutorialError);
          // Don't fail the entire page if tutorials fail to load
        }
      } catch (err: any) {
        console.error('Error fetching repository:', err);
        setError(err.message || 'Failed to load repository data');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchRepositoryData();
    }
  }, [params, user]);

  const handleGenerateTutorial = async () => {
    if (!repository) return;

    try {
      setTutorialLoading(true);

      // Parse patterns as arrays or null
      const includeArray = includePatterns.trim() ? includePatterns.split(',').map(p => p.trim()) : undefined;
      const excludeArray = excludePatterns.trim() ? excludePatterns.split(',').map(p => p.trim()) : undefined;

      const response = await tutorApi.generateTutorial(repository.id, {
        includePatterns: includeArray,
        excludePatterns: excludeArray,
        language,
        maxAbstractions,
        useCache: true
      });

      // Poll for the tutorial status
      const pollInterval = setInterval(async () => {
        try {
          const statusResponse = await tutorApi.getTutorialStatus(response.data.github_action_id);
          const tutorialStatus = statusResponse.data;

          // Update the tutorials list with the latest status
          setTutorials(prev => {
            const index = prev.findIndex(t => t.github_action_id === tutorialStatus.github_action_id);
            if (index >= 0) {
              return [
                ...prev.slice(0, index),
                tutorialStatus,
                ...prev.slice(index + 1)
              ];
            } else {
              return [tutorialStatus, ...prev];
            }
          });

          // If the tutorial generation is completed or failed, stop polling
          if (tutorialStatus.status === 'completed' || tutorialStatus.status === 'failed') {
            clearInterval(pollInterval);
            setTutorialLoading(false);
          }
        } catch (err) {
          console.error('Error polling tutorial status:', err);
          clearInterval(pollInterval);
          setTutorialLoading(false);
        }
      }, 5000); // Poll every 5 seconds

      // Add the new tutorial to the list immediately with 'queued' status
      setTutorials(prev => [response.data, ...prev]);

    } catch (err: any) {
      console.error('Error generating tutorial:', err);
      setError(err.message || 'Failed to generate tutorial');
      setTutorialLoading(false);
    }
  };

  const handleViewTutorial = (tutorial: TutorialStatus) => {
    setSelectedTutorial(tutorial);

    // If tutorial is completed and has files, set the first file as current
    if (tutorial.status === 'completed' && tutorial.result?.files?.length > 0) {
      // Find the index.md file or use the first file
      const indexFile = tutorial.result.files.find((f: TutorialFile) => f.path === 'index.md');
      setCurrentFile(indexFile || tutorial.result.files[0]);
    } else {
      setCurrentFile(null);
    }
  };

  const handleFileSelect = (file: TutorialFile) => {
    setCurrentFile(file);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'default';
      case 'failed':
        return 'destructive';
      case 'running':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  if (!isLoaded || loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-lg text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to sign-in
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/50">
        <NavigationBar />
        <main className="container mx-auto px-4 py-8">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button asChild className="mt-4">
            <Link href="/repositories">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Repositories
            </Link>
          </Button>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/50">
      <MermaidInitializer />
      <NavigationBar />

      <main className="container mx-auto px-4 py-8">
        {repository ? (
          <>
            {/* Repository Header */}
            <Card className="mb-8">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <CardTitle className="text-3xl font-bold">{repository.name}</CardTitle>
                    <CardDescription className="text-lg">
                      {repository.full_name}
                    </CardDescription>
                    {repository.description && (
                      <p className="text-muted-foreground">
                        {repository.description}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={repository.is_private ? "secondary" : "outline"}>
                      {repository.is_private ? (
                        <>
                          <Lock className="mr-1 h-3 w-3" />
                          Private
                        </>
                      ) : (
                        <>
                          <Globe className="mr-1 h-3 w-3" />
                          Public
                        </>
                      )}
                    </Badge>
                    <Button asChild variant="outline">
                      <a
                        href={repository.github_url}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <ExternalLink className="mr-2 h-4 w-4" />
                        View on GitHub
                      </a>
                    </Button>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Repository Analyzer Section */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5 text-green-500" />
                  Repository Analyzer
                </CardTitle>
                <CardDescription>
                  Analyze your repository with Perplexity Sonar to get insights on dependencies,
                  technology trends, security vulnerabilities, and more.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild>
                  <Link href={`/repositories/${repository.id}/analyzer`}>
                    <Search className="mr-2 h-4 w-4" />
                    Analyze Repository
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Tutorial Generation Section */}
            <Card className="mb-8">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <BookOpen className="h-5 w-5 text-blue-500" />
                      Generate Tutorial
                    </CardTitle>
                    <CardDescription>
                      Generate an AI-powered tutorial for your repository
                    </CardDescription>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                  >
                    <Settings className="mr-2 h-4 w-4" />
                    {showAdvancedOptions ? 'Hide Advanced' : 'Advanced Options'}
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4">
                  <div>
                    <Label htmlFor="language">Language</Label>
                    <select
                      id="language"
                      className="mt-1 block w-full rounded-md border border-input bg-background px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-ring"
                      value={language}
                      onChange={(e) => setLanguage(e.target.value)}
                    >
                      <option value="english">English</option>
                      <option value="spanish">Spanish</option>
                      <option value="french">French</option>
                      <option value="german">German</option>
                      <option value="chinese">Chinese</option>
                      <option value="japanese">Japanese</option>
                      <option value="korean">Korean</option>
                      <option value="russian">Russian</option>
                      <option value="portuguese">Portuguese</option>
                    </select>
                  </div>

                  {showAdvancedOptions && (
                    <>
                      <div>
                        <Label htmlFor="includePatterns">
                          Include Patterns (comma-separated, e.g. "*.py,*.js")
                        </Label>
                        <Input
                          id="includePatterns"
                          value={includePatterns}
                          onChange={(e) => setIncludePatterns(e.target.value)}
                          placeholder="*.py,*.js,*.jsx"
                        />
                      </div>

                      <div>
                        <Label htmlFor="excludePatterns">
                          Exclude Patterns (comma-separated, e.g. "tests/*,docs/*")
                        </Label>
                        <Input
                          id="excludePatterns"
                          value={excludePatterns}
                          onChange={(e) => setExcludePatterns(e.target.value)}
                          placeholder="tests/*,docs/*,*.log"
                        />
                      </div>

                      <div>
                        <Label htmlFor="maxAbstractions">Max Abstractions</Label>
                        <Input
                          type="number"
                          id="maxAbstractions"
                          value={maxAbstractions}
                          onChange={(e) => setMaxAbstractions(parseInt(e.target.value) || 10)}
                          min={5}
                          max={20}
                        />
                      </div>
                    </>
                  )}

                  <Button
                    onClick={handleGenerateTutorial}
                    disabled={tutorialLoading}
                  >
                    {tutorialLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <BookOpen className="mr-2 h-4 w-4" />
                        Generate Tutorial
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Tutorials Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Your Tutorials ({tutorials.length})
                </CardTitle>
                <CardDescription>
                  Generated tutorials for this repository
                </CardDescription>
              </CardHeader>
              <CardContent>
                {tutorials.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">
                      No tutorials generated yet. Generate your first tutorial above.
                    </p>
                  </div>
                ) : selectedTutorial ? (
                  <div className="space-y-6">
                    {/* Tutorial Header */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <h3 className="text-xl font-semibold">
                          {selectedTutorial.result?.project_name || 'Repository Tutorial'}
                        </h3>
                        <Badge variant={getStatusBadgeVariant(selectedTutorial.status)}>
                          {getStatusIcon(selectedTutorial.status)}
                          {selectedTutorial.status.charAt(0).toUpperCase() + selectedTutorial.status.slice(1)}
                        </Badge>
                      </div>
                      <Button variant="outline" onClick={() => setSelectedTutorial(null)}>
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to List
                      </Button>
                    </div>

                    <Separator />

                    {selectedTutorial.status === 'completed' ? (
                      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                        {/* Files Sidebar */}
                        <div className="lg:col-span-1">
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">
                                Files ({selectedTutorial.result?.files?.length || 0})
                              </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-2 max-h-96 overflow-y-auto">
                              {selectedTutorial.result?.files?.map((file: TutorialFile) => (
                                <Button
                                  key={file.path}
                                  variant={currentFile?.path === file.path ? "default" : "ghost"}
                                  className="w-full justify-start text-left h-auto p-3"
                                  onClick={() => handleFileSelect(file)}
                                >
                                  <div className="flex items-center gap-2 w-full">
                                    <FileText className="h-4 w-4 opacity-70" />
                                    <span className="text-sm truncate">{file.path}</span>
                                  </div>
                                </Button>
                              ))}
                            </CardContent>
                          </Card>
                        </div>

                        {/* Content Area */}
                        <div className="lg:col-span-3">
                          <Card>
                            <CardContent className="p-6">
                              {currentFile ? (
                                <div className="space-y-4">
                                  <div className="flex items-center justify-between">
                                    <h4 className="text-lg font-semibold">{currentFile.path}</h4>
                                    <Button variant="outline" size="sm">
                                      <Download className="mr-2 h-4 w-4" />
                                      Download
                                    </Button>
                                  </div>
                                  <Separator />
                                  <div className="prose prose-neutral dark:prose-invert max-w-none">
                                    <div
                                      dangerouslySetInnerHTML={{
                                        __html: currentFile.content
                                          .replace(/</g, '&lt;')
                                          .replace(/>/g, '&gt;')
                                          .replace(/```mermaid/g, '<div class="mermaid">')
                                          .replace(/```/g, '</div>')
                                          .replace(/\n/g, '<br />')
                                          .replace(/\# (.*)/g, '<h1>$1</h1>')
                                          .replace(/\#\# (.*)/g, '<h2>$1</h2>')
                                          .replace(/\#\#\# (.*)/g, '<h3>$1</h3>')
                                          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                                          .replace(/\*(.*?)\*/g, '<em>$1</em>')
                                      }}
                                    />
                                  </div>
                                </div>
                              ) : (
                                <div className="text-center py-12">
                                  <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                                  <p className="text-muted-foreground">
                                    Select a file from the sidebar to view its content.
                                  </p>
                                </div>
                              )}
                            </CardContent>
                          </Card>
                        </div>
                      </div>
                    ) : selectedTutorial.status === 'failed' ? (
                      <Alert variant="destructive">
                        <XCircle className="h-4 w-4" />
                        <AlertDescription>
                          <strong>Tutorial generation failed</strong>
                          <p className="mt-2">
                            {selectedTutorial.error || 'An error occurred during tutorial generation.'}
                          </p>
                        </AlertDescription>
                      </Alert>
                    ) : (
                      <div className="text-center py-12">
                        <Loader2 className="mx-auto h-12 w-12 animate-spin text-primary mb-4" />
                        <p className="text-muted-foreground">
                          {selectedTutorial.status === 'queued'
                            ? 'Tutorial generation is queued. It will start shortly.'
                            : 'Tutorial generation is in progress. This may take several minutes.'}
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {tutorials.map((tutorial) => (
                      <Card key={tutorial.github_action_id} className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handleViewTutorial(tutorial)}>
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <CardTitle className="text-lg">
                              {tutorial.result?.project_name || 'Repository Tutorial'}
                            </CardTitle>
                            <Badge variant={getStatusBadgeVariant(tutorial.status)}>
                              {getStatusIcon(tutorial.status)}
                              {tutorial.status.charAt(0).toUpperCase() + tutorial.status.slice(1)}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground mb-4">
                            ID: {tutorial.github_action_id}
                          </p>
                          <Button variant="outline" size="sm" className="w-full">
                            {tutorial.status === 'completed'
                              ? 'View Tutorial'
                              : tutorial.status === 'failed'
                              ? 'View Error'
                              : 'View Status'}
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            <div className="mt-8">
              <Button asChild variant="outline">
                <Link href="/repositories">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Repositories
                </Link>
              </Button>
            </div>
          </>
        ) : (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        )}
      </main>
    </div>
  );
}